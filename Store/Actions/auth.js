import { ImagePropTypes } from 'react-native';
import { Alert } from 'react-native';
import { Item } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as CONSTANT from '../../src/Constant/constants';

export const AUTHENTICATE = 'AUTHENTICATE';
export const LOGOUT = 'LOGOUT';
export const UPDATE_PROFILE = 'UPDATE_PROFILE';

export const authenticate = (token) => {
    return async dispatch => {
        dispatch({ type: AUTHENTICATE, token: token });
    }
};

export const logout = () => {
    return async dispatch => {
        dispatch({ type: LOGOUT, user: response.data });
    }
};


export const Login = (device_id, phone, password) => {
    return async dispatch => {
        const urlencoded = new URLSearchParams();
        urlencoded.append('device_id', device_id);
        urlencoded.append('phone', phone);
        urlencoded.append('password', password);
        console.log('Login urlencoded data', urlencoded);
        var url = CONSTANT.BaseUrl + 'login';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            // body: JSON.stringify({
            //     device_id:device_id
            // }),
            headers: {
                // 'Accept': 'application/x-www-form-urlencoded',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('Login response', response)
                // dispatch({ type: UPDATE_PROFILE, user: response.data.user });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const Register = (device_id, name, email, phone, password, password_confirmation) => {
    return async dispatch => {
        const urlencoded = new URLSearchParams();
        urlencoded.append('name', name);
        urlencoded.append('email', email);
        urlencoded.append('phone', phone);
        urlencoded.append('password', password);
        urlencoded.append('password_confirmation', password_confirmation);
        urlencoded.append('device_id', device_id);
        console.log('Register urlencoded data', urlencoded);
        var url = CONSTANT.BaseUrl + 'register';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            // body: JSON.stringify({
            //     device_id:device_id
            // }),
            headers: {
                // 'Accept': 'application/x-www-form-urlencoded',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('Register response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const Verify = (phone, verification_code) => {
    return async dispatch => {
        const urlencoded = new URLSearchParams();
        urlencoded.append('phone', phone);
        urlencoded.append('verification_code', verification_code);
        console.log('Verify urlencoded data', urlencoded);
        var url = CONSTANT.BaseUrl + 'verify';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            // body: JSON.stringify({
            //     device_id:device_id
            // }),
            headers: {
                // 'Accept': 'application/x-www-form-urlencoded',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('Verify response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const ForgotPassword = (phone) => {
    return async dispatch => {
        const urlencoded = new URLSearchParams();
        urlencoded.append('phone', phone);
        console.log('ForgotPassword urlencoded data', urlencoded);
        var url = CONSTANT.BaseUrl + 'forgot-password';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            // body: JSON.stringify({
            //     device_id:device_id
            // }),
            headers: {
                // 'Accept': 'application/x-www-form-urlencoded',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('ForgotPassword response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const ResetPassword = (phone, forgot_password_code, password, password_confirmation) => {
    return async dispatch => {
        const urlencoded = new URLSearchParams();
        urlencoded.append('phone', phone);
        urlencoded.append('forgot_password_code', forgot_password_code);
        urlencoded.append('password', password);
        urlencoded.append('password_confirmation', password_confirmation);
        console.log('ResetPassword urlencoded data', urlencoded);
        var url = CONSTANT.BaseUrl + 'reset-password';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            // body: JSON.stringify({
            //     device_id:device_id
            // }),
            headers: {
                // 'Accept': 'application/x-www-form-urlencoded',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('ResetPassword response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const ChangePassword = (old_password, password, password_confirmation) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token')
        const urlencoded = new URLSearchParams();
        urlencoded.append('old_password', old_password);
        urlencoded.append('password', password);
        urlencoded.append('password_confirmation', password_confirmation);
        console.log('ChangePassword urlencoded data', urlencoded);
        var url = CONSTANT.BaseUrl + 'change-password';
        console.log(url)

        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('ChangePassword response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const UpdateEmail = (email) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token')
        const urlencoded = new URLSearchParams();
        urlencoded.append('email', email);
        console.log('UpdateEmail urlencoded data', urlencoded);
        var url = CONSTANT.BaseUrl + 'update-profile/email';
        console.log(url)

        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('UpdateEmail response', response)
                if (response.success == true) {
                    dispatch({ type: UPDATE_PROFILE, user: response.data.user });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const UpdatePhone = (phone) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token')
        const urlencoded = new URLSearchParams();
        urlencoded.append('phone', phone);
        console.log('UpdatePhone urlencoded data', urlencoded);
        var url = CONSTANT.BaseUrl + 'update-profile/phone';
        console.log(url)

        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('UpdatePhone response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const editProfileImage = (image) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token')
        const formData = new FormData();
        formData.append('image', image);
        console.log('editProfileImage formData data', formData);
        var url = CONSTANT.BaseUrl + 'update-profile/image';
        console.log(url)

        return fetch(url, {
            method: 'POST',
            body: formData,
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'multipart/form-data',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('editProfileImage response', response)
                if (response.success == true) {
                    dispatch({ type: UPDATE_PROFILE, user: response.data.user });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};


export const user = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token')

        var url = CONSTANT.BaseUrl + 'user';
        console.log(url)

        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('user response', response)
                dispatch({ type: UPDATE_PROFILE, user: response.data });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};