import { ImagePropTypes } from 'react-native';
import { Alert } from 'react-native';
import { Item } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, Domain } from '../../src/components/Constant';
import * as CONSTANT from '../../src/Constant/constants';

export const ORDERTIME = 'ORDERTIME';
export const PAYMENT = 'PAYMENT';
export const OPTIONS = 'OPTIONS';
export const STATUSES = 'STATUSES';


export const getOptions = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders/options';
        console.log("options url", url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('options response', response);
                dispatch({ type: OPTIONS, options: response.data });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getPaymentMethods = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders/payment-methods';
        console.log("paymentmethods url", url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('paymentmethods response', response)
                dispatch({ type: PAYMENT, payment: response.data });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getOrderTimes = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders/order-times';
        console.log("getOrderTimes url", url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getOrderTimes response', response);
                if (response.data) {
                    dispatch({ type: ORDERTIME, orderTime: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getStatuses = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders/statuses';
        console.log("getStatuses url", url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getStatuses response', response)
                dispatch({ type: STATUSES, statuses: response.data });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const CreateOrder = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        const urlencoded = new URLSearchParams();

        var url = CONSTANT.BaseUrl + 'orders';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('CreateOrder response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const RateOrder = (orderId) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        const urlencoded = new URLSearchParams();

        var url = CONSTANT.BaseUrl + 'orders/' + orderId + '/rate';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('RateOrder response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getOrders = (page) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders/all?page=' + page;
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getOrders response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getCurrentOrders = (page) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders?page=' + page;
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getCurrentOrders response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getFinishedOrders = (page) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders/shared?page=' + page;
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getSharedOrders response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getOrder = (orderId) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders/' + orderId;
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getOrder response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const AcceptOrRejectOrder = (orderId, status) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        const urlencoded = new URLSearchParams();
        urlencoded.append('status', status);

        var url = CONSTANT.BaseUrl + 'orders/' + orderId + '/accept-or-reject';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('RateOrder response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};