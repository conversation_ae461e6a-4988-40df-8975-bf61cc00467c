import { ImagePropTypes } from 'react-native';
import { Alert } from 'react-native';
import { Item } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, Domain } from '../../src/components/Constant';
import * as CONSTANT from '../../src/Constant/constants';


export const SETTINGS = 'SETTINGS';
export const SPLASH = 'SPLASH';


export const suggestions = (message) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token')
        const urlencoded = new URLSearchParams();
        urlencoded.append('message', message);

        console.log('Login urlencoded data', urlencoded);
        var url = CONSTANT.BaseUrl + 'suggestions';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            // body: JSON.stringify({
            //     device_id:device_id
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('suggestions response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getArticles = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'articles';
        console.log("articles url", url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('articles response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getSettings = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'settings';
        console.log("getSettings url", url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getSettings response', response)
                dispatch({ type: SETTINGS, settings: response.data });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getSplash = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'splash';
        console.log("getSplash url", url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                // 'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getSplash response', response)
                dispatch({ type: SPLASH, splash: response.data });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};