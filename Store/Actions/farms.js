import { ImagePropTypes } from 'react-native';
import { Alert } from 'react-native';
import { Item } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, Domain } from '../../src/components/Constant';
import * as CONSTANT from '../../src/Constant/constants';

export const getWeights = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        var url = CONSTANT.BaseUrl + 'weights';
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getWeights response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getSheepCategories = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        var url = CONSTANT.BaseUrl + 'sheep-categories';
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getSheepCategories response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getFarms = (sheep_category_id, weight_id, page, search) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let url = '';
        if (weight_id) {
            url = CONSTANT.BaseUrl + 'farms?weight_id=' + weight_id + '&page=' + page + '&search=' + search + '&sheep_category_id=' + sheep_category_id;
        }
        else {
            url = CONSTANT.BaseUrl + 'farms?sheep_category_id=' + sheep_category_id + '&page=' + page + '&search=' + search;
        }

        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getFarms response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getSheeps = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'sheep';
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getSheeps response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getSheepsByFarmId = (farm_id) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'sheep?farm_id=' + farm_id;
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getSheepsByFarmId response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getSheep = (sheep_id) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'sheep?ids[]=' + sheep_id;
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getSheepByFarmId response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getTraderFarms = (traderId) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'traders/' + traderId + '/farms';
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getTraderFarms response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getFavoriteFarms = (page) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'farms/favorite';
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getFavoriteFarms response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const farmToggleFavorite = (farmId) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'farms/' + farmId + '/toggle-favorite';
        console.log(url)
        return fetch(url, {
            method: 'Post',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('farmToggleFavorite response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const traderoggleSubscription = (traderId) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'traders/' + traderId + '/toggle-subscription';
        console.log(url)
        return fetch(url, {
            method: 'Post',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('traderoggleSubscription response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};