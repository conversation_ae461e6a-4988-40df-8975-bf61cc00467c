import { TIMER, CART, CLEARCART } from '../Actions/cart'

const initialState = {
    cart: {
        items: []
    },
    mins: 9,
    secs: 59
};

export default (state = initialState, action) => {
    switch (action.type) {
        case CLEARCART:
            return {
                ...state,
                cart: {
                    items: []
                },
            };
        case CART:
            return {
                ...state,
                cart: action.cart,
            };
        case TIMER:
            return {
                ...state,
                mins: action.mins,
                secs: action.secs,
            };
        default:
            return state;
    }
};
