import { ORDERTIME, OPTIONS, PAYMENT, STATUSES } from '../Actions/orders'

const initialState = {
    orderTime: [],
    options: {},
    payment: [],
    statuses: [],
};

export default (state = initialState, action) => {
    switch (action.type) {
        case ORDERTIME:
            return {
                ...state,
                orderTime: action.orderTime,
            };
        case OPTIONS:
            return {
                ...state,
                options: action.options,
            };
        case PAYMENT:
            return {
                ...state,
                payment: action.payment,
            };
        case STATUSES:
            return {
                ...state,
                statuses: action.statuses,
            };
        default:
            return state;
    }
};
