import { SETTINGS, SPLASH } from "../Actions/settings";

const initialState = {
    settings: {},
    statuses: []
};

export default (state = initialState, action) => {
    switch (action.type) {
        case SETTINGS:
            return {
                ...state,
                settings: action.settings,
            };
        case SPLASH:
            return {
                ...state,
                statuses: action.splash.statuses,
            };
        default:
            return state;
    }
};