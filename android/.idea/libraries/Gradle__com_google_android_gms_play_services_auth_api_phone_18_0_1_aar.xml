<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-auth-api-phone:18.0.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ecbff2677b7764f148a82774b6212b68/transformed/jetified-play-services-auth-api-phone-18.0.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/eef2b3f88c64e9ab602b4bf32c446878/transformed/jetified-play-services-auth-api-phone-18.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/eef2b3f88c64e9ab602b4bf32c446878/transformed/jetified-play-services-auth-api-phone-18.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/eef2b3f88c64e9ab602b4bf32c446878/transformed/jetified-play-services-auth-api-phone-18.0.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/ccd6841d8bac8900f73839737ab18ba9/transformed/jetified-play-services-auth-api-phone-18.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ccd6841d8bac8900f73839737ab18ba9/transformed/jetified-play-services-auth-api-phone-18.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ccd6841d8bac8900f73839737ab18ba9/transformed/jetified-play-services-auth-api-phone-18.0.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-auth-api-phone/18.0.1/e50322b5fef28962e883380be05fcf2298222b00/play-services-auth-api-phone-18.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>