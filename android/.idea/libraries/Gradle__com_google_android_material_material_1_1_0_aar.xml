<component name="libraryTable">
  <library name="Gradle: com.google.android.material:material:1.1.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/57464b4b4d61156f8e6c8bc8c6677a39/transformed/material-1.1.0/annotations.zip!/" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/527711cf5666f59e44db04ec8bfeab14/transformed/material-1.1.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/57464b4b4d61156f8e6c8bc8c6677a39/transformed/material-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/57464b4b4d61156f8e6c8bc8c6677a39/transformed/material-1.1.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/57464b4b4d61156f8e6c8bc8c6677a39/transformed/material-1.1.0/jars/classes.jar!/" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/527711cf5666f59e44db04ec8bfeab14/transformed/material-1.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/527711cf5666f59e44db04ec8bfeab14/transformed/material-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/527711cf5666f59e44db04ec8bfeab14/transformed/material-1.1.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.material/material/1.1.0/7ba5bcd622e3d6d7ab6b9833717c4385e4ba0a51/material-1.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>