<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-measurement-connector:19.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7a9e28b98304b3f345b379fed42ebe54/transformed/jetified-firebase-measurement-connector-19.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/25b5780a4bda685917ff0918e543386d/transformed/jetified-firebase-measurement-connector-19.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/25b5780a4bda685917ff0918e543386d/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/25b5780a4bda685917ff0918e543386d/transformed/jetified-firebase-measurement-connector-19.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/2aca5f378480c5d92758a88b7c8c9fae/transformed/jetified-firebase-measurement-connector-19.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/2aca5f378480c5d92758a88b7c8c9fae/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/2aca5f378480c5d92758a88b7c8c9fae/transformed/jetified-firebase-measurement-connector-19.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-measurement-connector/19.0.0/c0d58b8efa926b2c607984c54fb3d274490badd1/firebase-measurement-connector-19.0.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>