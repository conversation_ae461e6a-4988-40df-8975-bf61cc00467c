<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-location:20.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/10156f19f9e1371aad18d6c9a4f549cb/transformed/jetified-play-services-location-20.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/ebf9f497144434d319531a9277bb4e90/transformed/jetified-play-services-location-20.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ebf9f497144434d319531a9277bb4e90/transformed/jetified-play-services-location-20.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ebf9f497144434d319531a9277bb4e90/transformed/jetified-play-services-location-20.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/ec6832d3c8294ca53e43f6ff437c989a/transformed/jetified-play-services-location-20.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ec6832d3c8294ca53e43f6ff437c989a/transformed/jetified-play-services-location-20.0.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ec6832d3c8294ca53e43f6ff437c989a/transformed/jetified-play-services-location-20.0.0/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-location/20.0.0/cd6ff4a2f2a99f4200e0134f156c0184f6b9467a/play-services-location-20.0.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>