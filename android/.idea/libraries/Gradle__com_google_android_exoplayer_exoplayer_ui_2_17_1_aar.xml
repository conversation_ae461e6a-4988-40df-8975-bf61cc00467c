<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-ui:2.17.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/5df12dc88642257b37b127b6225e08cd/transformed/jetified-exoplayer-ui-2.17.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/5df12dc88642257b37b127b6225e08cd/transformed/jetified-exoplayer-ui-2.17.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/5df12dc88642257b37b127b6225e08cd/transformed/jetified-exoplayer-ui-2.17.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/5df12dc88642257b37b127b6225e08cd/transformed/jetified-exoplayer-ui-2.17.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-ui/2.17.1/c7154f1adfd65e0b859e85d9adc5110226c887cd/exoplayer-ui-2.17.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>