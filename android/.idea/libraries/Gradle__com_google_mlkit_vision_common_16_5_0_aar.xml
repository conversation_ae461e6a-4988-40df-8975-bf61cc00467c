<component name="libraryTable">
  <library name="Gradle: com.google.mlkit:vision-common:16.5.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/65b8c9bed6408983d4a1575df0b88d30/transformed/jetified-vision-common-16.5.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/4da424b8df5fd43c535cf74f48eefbb0/transformed/jetified-vision-common-16.5.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4da424b8df5fd43c535cf74f48eefbb0/transformed/jetified-vision-common-16.5.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4da424b8df5fd43c535cf74f48eefbb0/transformed/jetified-vision-common-16.5.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ba31d3e4092186cef9a050c075d60e6d/transformed/jetified-vision-common-16.5.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/ba31d3e4092186cef9a050c075d60e6d/transformed/jetified-vision-common-16.5.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ba31d3e4092186cef9a050c075d60e6d/transformed/jetified-vision-common-16.5.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.mlkit/vision-common/16.5.0/890e185cbcb78c40e5c6c8eaeff03771f041559c/vision-common-16.5.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>