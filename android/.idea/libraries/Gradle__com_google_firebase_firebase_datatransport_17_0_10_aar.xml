<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-datatransport:17.0.10@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9282a1ba13ba1bdb217616f4651e6f48/transformed/jetified-firebase-datatransport-17.0.10/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/937a9358ba699e24c8422f0c2411bcf0/transformed/jetified-firebase-datatransport-17.0.10/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/937a9358ba699e24c8422f0c2411bcf0/transformed/jetified-firebase-datatransport-17.0.10/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/937a9358ba699e24c8422f0c2411bcf0/transformed/jetified-firebase-datatransport-17.0.10/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f3a543007a48388034f58cf973ed346a/transformed/jetified-firebase-datatransport-17.0.10/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/f3a543007a48388034f58cf973ed346a/transformed/jetified-firebase-datatransport-17.0.10/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f3a543007a48388034f58cf973ed346a/transformed/jetified-firebase-datatransport-17.0.10/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-datatransport/17.0.10/de83e2d6f9c9315c359babdcf03dec170fd6980b/firebase-datatransport-17.0.10-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-datatransport/17.0.10/fd4eac635921dc6f0555efd23564313a20b97120/firebase-datatransport-17.0.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>