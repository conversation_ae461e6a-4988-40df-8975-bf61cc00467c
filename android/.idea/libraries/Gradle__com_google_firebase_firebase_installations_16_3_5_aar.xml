<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-installations:16.3.5@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b338d680b9edc5c291a4d00f15b4d285/transformed/jetified-firebase-installations-16.3.5/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/62982a39e907072a203f2fb1fd8c646f/transformed/jetified-firebase-installations-16.3.5/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/62982a39e907072a203f2fb1fd8c646f/transformed/jetified-firebase-installations-16.3.5/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/62982a39e907072a203f2fb1fd8c646f/transformed/jetified-firebase-installations-16.3.5/jars/classes.jar!/" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/c9e68a751f10c05ef16a5399a21aa80f/transformed/jetified-firebase-installations-16.3.5/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c9e68a751f10c05ef16a5399a21aa80f/transformed/jetified-firebase-installations-16.3.5/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c9e68a751f10c05ef16a5399a21aa80f/transformed/jetified-firebase-installations-16.3.5/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-installations/16.3.5/ba864fdfc85df3cb215b477c4a82732e6c4aad2/firebase-installations-16.3.5-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>