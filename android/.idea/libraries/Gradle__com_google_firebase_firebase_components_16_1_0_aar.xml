<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-components:16.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ef58ed3a472bf647329736cc56c2cbad/transformed/jetified-firebase-components-16.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/215ef28d2a09279423f8e2b291a33076/transformed/jetified-firebase-components-16.1.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/215ef28d2a09279423f8e2b291a33076/transformed/jetified-firebase-components-16.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/215ef28d2a09279423f8e2b291a33076/transformed/jetified-firebase-components-16.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a491f8e181219ca154e5bf25a6439a06/transformed/jetified-firebase-components-16.1.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a491f8e181219ca154e5bf25a6439a06/transformed/jetified-firebase-components-16.1.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/a491f8e181219ca154e5bf25a6439a06/transformed/jetified-firebase-components-16.1.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-components/16.1.0/3a9c1cc3126d1c3fa181be1692de99be7cfe3b83/firebase-components-16.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-components/16.1.0/5f2661eee0882325baf3b7ed9626b0dcbd7cbbaf/firebase-components-16.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>