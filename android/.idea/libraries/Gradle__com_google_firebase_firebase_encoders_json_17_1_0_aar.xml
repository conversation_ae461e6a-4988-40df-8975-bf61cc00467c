<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-encoders-json:17.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/16d1f048ddc7428a004b7d84114273b1/transformed/jetified-firebase-encoders-json-17.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f531bd77ddb086c340f9d6789b6ef618/transformed/jetified-firebase-encoders-json-17.1.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f531bd77ddb086c340f9d6789b6ef618/transformed/jetified-firebase-encoders-json-17.1.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/f531bd77ddb086c340f9d6789b6ef618/transformed/jetified-firebase-encoders-json-17.1.0/jars/classes.jar!/" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/3239b6776d97ed559820a0b4174779f7/transformed/jetified-firebase-encoders-json-17.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3239b6776d97ed559820a0b4174779f7/transformed/jetified-firebase-encoders-json-17.1.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3239b6776d97ed559820a0b4174779f7/transformed/jetified-firebase-encoders-json-17.1.0/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders-json/17.1.0/5fbe3622c8be8565ee56aa41117b13f095965904/firebase-encoders-json-17.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders-json/17.1.0/562e15d8b50abbf64e6e8b33c3ccdab488ae15f8/firebase-encoders-json-17.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>