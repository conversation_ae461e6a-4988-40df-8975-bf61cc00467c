<component name="libraryTable">
  <library name="Gradle: org.videolan.android:libvlc-all:3.6.0-eap9@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/26374561f7af1184db851f4fcfedb479/transformed/jetified-libvlc-all-3.6.0-eap9/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/26374561f7af1184db851f4fcfedb479/transformed/jetified-libvlc-all-3.6.0-eap9/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/26374561f7af1184db851f4fcfedb479/transformed/jetified-libvlc-all-3.6.0-eap9/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6cb1896f33a4df529e1dfaeb041d3a18/transformed/jetified-libvlc-all-3.6.0-eap9/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/6cb1896f33a4df529e1dfaeb041d3a18/transformed/jetified-libvlc-all-3.6.0-eap9/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6cb1896f33a4df529e1dfaeb041d3a18/transformed/jetified-libvlc-all-3.6.0-eap9/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.videolan.android/libvlc-all/3.6.0-eap9/dc1a502573774661e2d0204782292bd82c2444cd/libvlc-all-3.6.0-eap9-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.videolan.android/libvlc-all/3.6.0-eap9/4e05f0b384e3fb17454f13bed4ca5bf1c040a522/libvlc-all-3.6.0-eap9-sources.jar!/" />
    </SOURCES>
  </library>
</component>