<component name="libraryTable">
  <library name="Gradle: me.leolin:ShortcutBadger:1.1.22@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/cea6fd8be999bd1b7a442c2d57ea9751/transformed/jetified-ShortcutBadger-1.1.22/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/b2e820509a73730d5264700f67292c0d/transformed/jetified-ShortcutBadger-1.1.22/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b2e820509a73730d5264700f67292c0d/transformed/jetified-ShortcutBadger-1.1.22/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b2e820509a73730d5264700f67292c0d/transformed/jetified-ShortcutBadger-1.1.22/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0d17b208f62fcc20fb9779e37fab2b52/transformed/jetified-ShortcutBadger-1.1.22/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/0d17b208f62fcc20fb9779e37fab2b52/transformed/jetified-ShortcutBadger-1.1.22/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0d17b208f62fcc20fb9779e37fab2b52/transformed/jetified-ShortcutBadger-1.1.22/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/me.leolin/ShortcutBadger/1.1.22/8d181708c3d0b7cd93e7fbcb4467dae47603ee0b/ShortcutBadger-1.1.22-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/me.leolin/ShortcutBadger/1.1.22/614ef59fdccd8e121fa40ba7d454307025f8f0fe/ShortcutBadger-1.1.22-sources.jar!/" />
    </SOURCES>
  </library>
</component>