<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-iid-interop:17.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b97fc4e159c5992945212707622b590f/transformed/jetified-firebase-iid-interop-17.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/25889949552a9706d702e049fbe17eaa/transformed/jetified-firebase-iid-interop-17.1.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/25889949552a9706d702e049fbe17eaa/transformed/jetified-firebase-iid-interop-17.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/25889949552a9706d702e049fbe17eaa/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/f3863f38518c6219a334cae90cad885c/transformed/jetified-firebase-iid-interop-17.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f3863f38518c6219a334cae90cad885c/transformed/jetified-firebase-iid-interop-17.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f3863f38518c6219a334cae90cad885c/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-iid-interop/17.1.0/7fad4ae6f5c2016d5c7babbbf020b6ac08a9cb08/firebase-iid-interop-17.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>