<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-tasks:17.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/29745d7e06a2eecb7e9025d2eb75ce19/transformed/jetified-play-services-tasks-17.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e7b5816be32a2d3cb42c6edfd6cc314e/transformed/jetified-play-services-tasks-17.0.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e7b5816be32a2d3cb42c6edfd6cc314e/transformed/jetified-play-services-tasks-17.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/e7b5816be32a2d3cb42c6edfd6cc314e/transformed/jetified-play-services-tasks-17.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8356b9edcf7d12eeca25e5762a84b037/transformed/jetified-play-services-tasks-17.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/8356b9edcf7d12eeca25e5762a84b037/transformed/jetified-play-services-tasks-17.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8356b9edcf7d12eeca25e5762a84b037/transformed/jetified-play-services-tasks-17.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>