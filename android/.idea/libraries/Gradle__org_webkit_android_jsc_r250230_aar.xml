<component name="libraryTable">
  <library name="Gradle: org.webkit:android-jsc:r250230@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e66642f58c8356ed89e1de7aff5c3091/transformed/jetified-android-jsc-r250230/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/8c3cd2252007a00645d88e6832e778ac/transformed/jetified-android-jsc-r250230/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8c3cd2252007a00645d88e6832e778ac/transformed/jetified-android-jsc-r250230/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8c3cd2252007a00645d88e6832e778ac/transformed/jetified-android-jsc-r250230/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/06ec61613e71ba38c6a89017fdf1fab3/transformed/jetified-android-jsc-r250230/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/06ec61613e71ba38c6a89017fdf1fab3/transformed/jetified-android-jsc-r250230/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/06ec61613e71ba38c6a89017fdf1fab3/transformed/jetified-android-jsc-r250230/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>