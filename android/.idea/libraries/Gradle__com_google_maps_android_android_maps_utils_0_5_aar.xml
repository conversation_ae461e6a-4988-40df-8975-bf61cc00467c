<component name="libraryTable">
  <library name="Gradle: com.google.maps.android:android-maps-utils:0.5@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/49e6ec6a07384c44468e5e8beb006a8f/transformed/jetified-android-maps-utils-0.5/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/49e6ec6a07384c44468e5e8beb006a8f/transformed/jetified-android-maps-utils-0.5/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/49e6ec6a07384c44468e5e8beb006a8f/transformed/jetified-android-maps-utils-0.5/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/1a9d4b72503c2a0b366e1d5972520c53/transformed/jetified-android-maps-utils-0.5/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/1a9d4b72503c2a0b366e1d5972520c53/transformed/jetified-android-maps-utils-0.5/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/1a9d4b72503c2a0b366e1d5972520c53/transformed/jetified-android-maps-utils-0.5/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/da7a4cd10f976f6aa301e32ae875be4b/transformed/jetified-android-maps-utils-0.5/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/da7a4cd10f976f6aa301e32ae875be4b/transformed/jetified-android-maps-utils-0.5/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/da7a4cd10f976f6aa301e32ae875be4b/transformed/jetified-android-maps-utils-0.5/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/08463b80082fe10899b3156f35282014/transformed/jetified-android-maps-utils-0.5/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/08463b80082fe10899b3156f35282014/transformed/jetified-android-maps-utils-0.5/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/08463b80082fe10899b3156f35282014/transformed/jetified-android-maps-utils-0.5/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>