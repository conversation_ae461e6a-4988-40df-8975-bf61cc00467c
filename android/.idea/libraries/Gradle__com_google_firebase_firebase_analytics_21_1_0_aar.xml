<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-analytics:21.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/cafd2467dfe898fdd72e1e8cb45448c3/transformed/jetified-firebase-analytics-21.1.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/c6ae3be6066ba6b1f64d4aa886856de3/transformed/jetified-firebase-analytics-21.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c6ae3be6066ba6b1f64d4aa886856de3/transformed/jetified-firebase-analytics-21.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c6ae3be6066ba6b1f64d4aa886856de3/transformed/jetified-firebase-analytics-21.1.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c4b1ddffc9b68318232a12dcceae653d/transformed/jetified-firebase-analytics-21.1.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/c4b1ddffc9b68318232a12dcceae653d/transformed/jetified-firebase-analytics-21.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c4b1ddffc9b68318232a12dcceae653d/transformed/jetified-firebase-analytics-21.1.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-analytics/21.1.0/e30555d01f4b419de1eed6ceed27b98aedecf946/firebase-analytics-21.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>