<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-database:2.17.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/d5e78e304c54959a00c787191f98b4eb/transformed/jetified-exoplayer-database-2.17.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/d5e78e304c54959a00c787191f98b4eb/transformed/jetified-exoplayer-database-2.17.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d5e78e304c54959a00c787191f98b4eb/transformed/jetified-exoplayer-database-2.17.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d5e78e304c54959a00c787191f98b4eb/transformed/jetified-exoplayer-database-2.17.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-database/2.17.1/5cd9048b50e8c099647b701dfe79f534ccd73cfe/exoplayer-database-2.17.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>