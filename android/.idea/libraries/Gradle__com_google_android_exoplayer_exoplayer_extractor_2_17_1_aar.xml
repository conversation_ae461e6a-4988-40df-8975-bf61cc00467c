<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-extractor:2.17.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/f20dab02b7cb10886aafa50de03b2976/transformed/jetified-exoplayer-extractor-2.17.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/f20dab02b7cb10886aafa50de03b2976/transformed/jetified-exoplayer-extractor-2.17.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f20dab02b7cb10886aafa50de03b2976/transformed/jetified-exoplayer-extractor-2.17.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f20dab02b7cb10886aafa50de03b2976/transformed/jetified-exoplayer-extractor-2.17.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-extractor/2.17.1/9c61fe4d03f12ae01e3448721254abb5765d5ac6/exoplayer-extractor-2.17.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>