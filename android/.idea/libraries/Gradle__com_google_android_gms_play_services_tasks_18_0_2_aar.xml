<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-tasks:18.0.2@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/49becbbfa1ecfebc9fe96bf3207456a4/transformed/jetified-play-services-tasks-18.0.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4d17e9b1cb0072275deb4482fb9e2160/transformed/jetified-play-services-tasks-18.0.2/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4d17e9b1cb0072275deb4482fb9e2160/transformed/jetified-play-services-tasks-18.0.2/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/4d17e9b1cb0072275deb4482fb9e2160/transformed/jetified-play-services-tasks-18.0.2/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8f65cf4f0573e9791c826b9d4bae6714/transformed/jetified-play-services-tasks-18.0.2/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8f65cf4f0573e9791c826b9d4bae6714/transformed/jetified-play-services-tasks-18.0.2/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/8f65cf4f0573e9791c826b9d4bae6714/transformed/jetified-play-services-tasks-18.0.2/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-tasks/18.0.2/8cb34c1c7ea9ed5e4b284932740bb7d64257c35d/play-services-tasks-18.0.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>