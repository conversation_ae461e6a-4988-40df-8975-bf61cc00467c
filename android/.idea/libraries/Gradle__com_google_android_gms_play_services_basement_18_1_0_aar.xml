<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-basement:18.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/776a11a1ac9aa58f397076f195fdf0bd/transformed/jetified-play-services-basement-18.1.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/776a11a1ac9aa58f397076f195fdf0bd/transformed/jetified-play-services-basement-18.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/776a11a1ac9aa58f397076f195fdf0bd/transformed/jetified-play-services-basement-18.1.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/ee076e00b8ea5e212e4af23a83092ecb/transformed/jetified-play-services-basement-18.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ee076e00b8ea5e212e4af23a83092ecb/transformed/jetified-play-services-basement-18.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ee076e00b8ea5e212e4af23a83092ecb/transformed/jetified-play-services-basement-18.1.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-basement/18.1.0/dc468f5f14200309066c7c5f99374e625c9fa89a/play-services-basement-18.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>