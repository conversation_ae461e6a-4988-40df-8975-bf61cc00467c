<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-smoothstreaming:2.17.1@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/11ba68bbb5efe462b45b3803c98f4231/transformed/jetified-exoplayer-smoothstreaming-2.17.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/11ba68bbb5efe462b45b3803c98f4231/transformed/jetified-exoplayer-smoothstreaming-2.17.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/11ba68bbb5efe462b45b3803c98f4231/transformed/jetified-exoplayer-smoothstreaming-2.17.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-smoothstreaming/2.17.1/11ded9580413942eb8e9d92f100aff42a7a788eb/exoplayer-smoothstreaming-2.17.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>