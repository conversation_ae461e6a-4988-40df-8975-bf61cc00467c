<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-mlkit-face-detection:16.2.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/5fb8f502fd44c48bbb4dc97e12b7cb5d/transformed/jetified-play-services-mlkit-face-detection-16.2.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/05c1cfa61934dabe7489ac480f5c5d58/transformed/jetified-play-services-mlkit-face-detection-16.2.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/05c1cfa61934dabe7489ac480f5c5d58/transformed/jetified-play-services-mlkit-face-detection-16.2.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/05c1cfa61934dabe7489ac480f5c5d58/transformed/jetified-play-services-mlkit-face-detection-16.2.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a364e19a10b2e61b2f879762774cf2ab/transformed/jetified-play-services-mlkit-face-detection-16.2.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/a364e19a10b2e61b2f879762774cf2ab/transformed/jetified-play-services-mlkit-face-detection-16.2.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a364e19a10b2e61b2f879762774cf2ab/transformed/jetified-play-services-mlkit-face-detection-16.2.0/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-mlkit-face-detection/16.2.0/96f8df62ed7068d157c0129e01266eb27d940c7c/play-services-mlkit-face-detection-16.2.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>