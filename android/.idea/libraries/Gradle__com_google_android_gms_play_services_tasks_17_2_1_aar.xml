<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-tasks:17.2.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ae6e8d1ad961b765c0e6255a3d5a0d77/transformed/jetified-play-services-tasks-17.2.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/085a29a7bf1d5d90ae02db3249ed33c2/transformed/jetified-play-services-tasks-17.2.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/085a29a7bf1d5d90ae02db3249ed33c2/transformed/jetified-play-services-tasks-17.2.1/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/085a29a7bf1d5d90ae02db3249ed33c2/transformed/jetified-play-services-tasks-17.2.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/a22cc31fcad34fb4101f29fa953e6508/transformed/jetified-play-services-tasks-17.2.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a22cc31fcad34fb4101f29fa953e6508/transformed/jetified-play-services-tasks-17.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a22cc31fcad34fb4101f29fa953e6508/transformed/jetified-play-services-tasks-17.2.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-tasks/17.2.1/ef19d8e16a762897c399d10edef4ef3772200155/play-services-tasks-17.2.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>