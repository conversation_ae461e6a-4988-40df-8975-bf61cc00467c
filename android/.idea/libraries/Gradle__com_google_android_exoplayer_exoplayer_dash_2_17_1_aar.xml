<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-dash:2.17.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/43eee40e8d025643a8c4221c01dc7cb1/transformed/jetified-exoplayer-dash-2.17.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/43eee40e8d025643a8c4221c01dc7cb1/transformed/jetified-exoplayer-dash-2.17.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/43eee40e8d025643a8c4221c01dc7cb1/transformed/jetified-exoplayer-dash-2.17.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/43eee40e8d025643a8c4221c01dc7cb1/transformed/jetified-exoplayer-dash-2.17.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-dash/2.17.1/55a47ab8360728cf10f89f87610500b291d6bb5f/exoplayer-dash-2.17.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>