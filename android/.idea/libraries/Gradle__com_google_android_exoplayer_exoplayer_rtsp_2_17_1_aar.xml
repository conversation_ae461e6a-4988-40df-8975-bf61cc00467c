<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-rtsp:2.17.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/29a5161467d552d26b84e0c1d453244c/transformed/jetified-exoplayer-rtsp-2.17.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/29a5161467d552d26b84e0c1d453244c/transformed/jetified-exoplayer-rtsp-2.17.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/29a5161467d552d26b84e0c1d453244c/transformed/jetified-exoplayer-rtsp-2.17.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/29a5161467d552d26b84e0c1d453244c/transformed/jetified-exoplayer-rtsp-2.17.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-rtsp/2.17.1/48a84ef0efc08a37d6538823f576e9069b97c28b/exoplayer-rtsp-2.17.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>