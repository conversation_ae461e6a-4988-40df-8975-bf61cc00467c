<component name="libraryTable">
  <library name="Gradle: com.google.android.odml:image:1.0.0-beta1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f5924b0e3509d89a7b4b81818650dd50/transformed/jetified-image-1.0.0-beta1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/abe2b6e57f24c7e0a1ddb7135f33fc84/transformed/jetified-image-1.0.0-beta1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/abe2b6e57f24c7e0a1ddb7135f33fc84/transformed/jetified-image-1.0.0-beta1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/abe2b6e57f24c7e0a1ddb7135f33fc84/transformed/jetified-image-1.0.0-beta1/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7f6118910df85629d1f9de259b49af91/transformed/jetified-image-1.0.0-beta1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7f6118910df85629d1f9de259b49af91/transformed/jetified-image-1.0.0-beta1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/7f6118910df85629d1f9de259b49af91/transformed/jetified-image-1.0.0-beta1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.odml/image/1.0.0-beta1/aa9d23e3124a588104fe600f42af0e192f732599/image-1.0.0-beta1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>