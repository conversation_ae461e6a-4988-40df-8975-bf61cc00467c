<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-auth-base:18.0.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4aa328b06f4afd620d9267cd996e675b/transformed/jetified-play-services-auth-base-18.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ecfb30fae11379ce4246a636f8422305/transformed/jetified-play-services-auth-base-18.0.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/ecfb30fae11379ce4246a636f8422305/transformed/jetified-play-services-auth-base-18.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ecfb30fae11379ce4246a636f8422305/transformed/jetified-play-services-auth-base-18.0.1/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/dc47ee876a195cf196b3524bf6baa482/transformed/jetified-play-services-auth-base-18.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/dc47ee876a195cf196b3524bf6baa482/transformed/jetified-play-services-auth-base-18.0.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/dc47ee876a195cf196b3524bf6baa482/transformed/jetified-play-services-auth-base-18.0.1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-auth-base/18.0.1/3807e6c63954c0b45972ba441e699dca1d03f2a8/play-services-auth-base-18.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>