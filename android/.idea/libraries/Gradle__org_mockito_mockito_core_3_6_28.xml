<component name="libraryTable">
  <library name="Gradle: org.mockito:mockito-core:3.6.28">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/3.6.28/ad16f503916da658bd7b805816ae3b296f3eea4c/mockito-core-3.6.28.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/3.6.28/d5e98992c039bfb223c8109df056e3ea51299732/mockito-core-3.6.28-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/3.6.28/296251bf042984db28998e4c1be22c5e265a0cc7/mockito-core-3.6.28-sources.jar!/" />
    </SOURCES>
  </library>
</component>