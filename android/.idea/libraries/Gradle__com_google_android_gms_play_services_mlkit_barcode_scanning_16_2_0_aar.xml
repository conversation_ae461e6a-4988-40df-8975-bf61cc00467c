<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ba944f7e86c83034ce8dfe9f482c636e/transformed/jetified-play-services-mlkit-barcode-scanning-16.2.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9564651ddc5f6c5fb33387f7e04db9c4/transformed/jetified-play-services-mlkit-barcode-scanning-16.2.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9564651ddc5f6c5fb33387f7e04db9c4/transformed/jetified-play-services-mlkit-barcode-scanning-16.2.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/9564651ddc5f6c5fb33387f7e04db9c4/transformed/jetified-play-services-mlkit-barcode-scanning-16.2.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d249b2d6e4fe5dc553a8cd207faa6d63/transformed/jetified-play-services-mlkit-barcode-scanning-16.2.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d249b2d6e4fe5dc553a8cd207faa6d63/transformed/jetified-play-services-mlkit-barcode-scanning-16.2.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/d249b2d6e4fe5dc553a8cd207faa6d63/transformed/jetified-play-services-mlkit-barcode-scanning-16.2.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-mlkit-barcode-scanning/16.2.0/2a7792d1057a980a2fe32cc4cdc423755fb3b09e/play-services-mlkit-barcode-scanning-16.2.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>