<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-installations:17.0.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/bb3df486faa3423e7839ff0a4dcbef7c/transformed/jetified-firebase-installations-17.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/990c5477ea5ce1c0da90bb53130789a7/transformed/jetified-firebase-installations-17.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/990c5477ea5ce1c0da90bb53130789a7/transformed/jetified-firebase-installations-17.0.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/990c5477ea5ce1c0da90bb53130789a7/transformed/jetified-firebase-installations-17.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/2bc9dd5bee550ecf51ce6a9032ddef64/transformed/jetified-firebase-installations-17.0.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/2bc9dd5bee550ecf51ce6a9032ddef64/transformed/jetified-firebase-installations-17.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/2bc9dd5bee550ecf51ce6a9032ddef64/transformed/jetified-firebase-installations-17.0.1/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-installations/17.0.1/a5de27e8894c107c06e27ee0bfdf8c24216354a2/firebase-installations-17.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>