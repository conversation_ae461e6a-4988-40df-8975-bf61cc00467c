<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-core:2.17.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/0edf4521de86a663ced14b3c1fe42379/transformed/jetified-exoplayer-core-2.17.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/0edf4521de86a663ced14b3c1fe42379/transformed/jetified-exoplayer-core-2.17.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0edf4521de86a663ced14b3c1fe42379/transformed/jetified-exoplayer-core-2.17.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0edf4521de86a663ced14b3c1fe42379/transformed/jetified-exoplayer-core-2.17.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-core/2.17.1/3f905ce73b7758a0a41d66f92797d503d2a543e6/exoplayer-core-2.17.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>