<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-base:18.0.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/537de1698813a80acd1f09b5c391aaaa/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/537de1698813a80acd1f09b5c391aaaa/transformed/jetified-play-services-base-18.0.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/537de1698813a80acd1f09b5c391aaaa/transformed/jetified-play-services-base-18.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/deb51f0a4b7852d0f144b8597de8d28c/transformed/jetified-play-services-base-18.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/deb51f0a4b7852d0f144b8597de8d28c/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/deb51f0a4b7852d0f144b8597de8d28c/transformed/jetified-play-services-base-18.0.1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-base/18.0.1/5c3b2e3ce7819eeff73b937ae37741ad2bd88172/play-services-base-18.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>