<component name="libraryTable">
  <library name="Gradle: com.google.android.material:material:1.4.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/981002461b3e86044ea09da67ae0bbee/transformed/material-1.4.0/annotations.zip!/" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/949cdb477b09f000fb524d546627ea98/transformed/material-1.4.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/981002461b3e86044ea09da67ae0bbee/transformed/material-1.4.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/981002461b3e86044ea09da67ae0bbee/transformed/material-1.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/981002461b3e86044ea09da67ae0bbee/transformed/material-1.4.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/949cdb477b09f000fb524d546627ea98/transformed/material-1.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/949cdb477b09f000fb524d546627ea98/transformed/material-1.4.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/949cdb477b09f000fb524d546627ea98/transformed/material-1.4.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.material/material/1.4.0/51035a8f6342c20d4d8f242d100e6f25e0c271ac/material-1.4.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>