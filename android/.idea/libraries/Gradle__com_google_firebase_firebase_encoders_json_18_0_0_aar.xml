<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-encoders-json:18.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/1d6fc00826bc45f1113036e7171f885f/transformed/jetified-firebase-encoders-json-18.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/015a2d7dd1a1e2e91ceb0935e02b1c27/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/015a2d7dd1a1e2e91ceb0935e02b1c27/transformed/jetified-firebase-encoders-json-18.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/015a2d7dd1a1e2e91ceb0935e02b1c27/transformed/jetified-firebase-encoders-json-18.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ee64bed28b3457541943253d9fa80735/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ee64bed28b3457541943253d9fa80735/transformed/jetified-firebase-encoders-json-18.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/ee64bed28b3457541943253d9fa80735/transformed/jetified-firebase-encoders-json-18.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders-json/18.0.0/8b885e5d8450f7afe805d5efbff0bb6085301c62/firebase-encoders-json-18.0.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders-json/18.0.0/d1cddbffa2ae77d26fd0c0b2c37b94331be039ef/firebase-encoders-json-18.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>