<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-tasks:18.0.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9dc373f2f66636c827c5018e5798a641/transformed/jetified-play-services-tasks-18.0.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/9356d8c7ac06f88ecda3754c8698a4c3/transformed/jetified-play-services-tasks-18.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9356d8c7ac06f88ecda3754c8698a4c3/transformed/jetified-play-services-tasks-18.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9356d8c7ac06f88ecda3754c8698a4c3/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/478ea1e182a4dc4f9ec5a60590f2ccf2/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/478ea1e182a4dc4f9ec5a60590f2ccf2/transformed/jetified-play-services-tasks-18.0.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/478ea1e182a4dc4f9ec5a60590f2ccf2/transformed/jetified-play-services-tasks-18.0.1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-tasks/18.0.1/2e3c5d886f131c9c8ae2d93dcf32aa58dad438e3/play-services-tasks-18.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>