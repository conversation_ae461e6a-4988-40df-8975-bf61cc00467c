<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-installations-interop:16.0.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/070ba81f8f9c5d0a2f0f666410089ef9/transformed/jetified-firebase-installations-interop-16.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c8f616a8f1409e8b33fa2f18dc8cd4f2/transformed/jetified-firebase-installations-interop-16.0.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/c8f616a8f1409e8b33fa2f18dc8cd4f2/transformed/jetified-firebase-installations-interop-16.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c8f616a8f1409e8b33fa2f18dc8cd4f2/transformed/jetified-firebase-installations-interop-16.0.1/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c71d4fe8180ecf0e61f483e0aee090fb/transformed/jetified-firebase-installations-interop-16.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c71d4fe8180ecf0e61f483e0aee090fb/transformed/jetified-firebase-installations-interop-16.0.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/c71d4fe8180ecf0e61f483e0aee090fb/transformed/jetified-firebase-installations-interop-16.0.1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-installations-interop/16.0.1/e0baec714e4cffbf7aed0e3daa5530c10c018eb2/firebase-installations-interop-16.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>