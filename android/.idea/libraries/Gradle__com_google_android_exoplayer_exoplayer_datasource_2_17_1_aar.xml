<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-datasource:2.17.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/19b2e6f9a1845aa182c9a309d7fc2670/transformed/jetified-exoplayer-datasource-2.17.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/19b2e6f9a1845aa182c9a309d7fc2670/transformed/jetified-exoplayer-datasource-2.17.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/19b2e6f9a1845aa182c9a309d7fc2670/transformed/jetified-exoplayer-datasource-2.17.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/19b2e6f9a1845aa182c9a309d7fc2670/transformed/jetified-exoplayer-datasource-2.17.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-datasource/2.17.1/2af27e6275fae11f8148af54830d966bb8a32059/exoplayer-datasource-2.17.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>