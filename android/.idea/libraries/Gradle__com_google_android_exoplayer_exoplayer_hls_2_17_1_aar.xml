<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-hls:2.17.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/e2f3bb3755dc489bd132e89dba6351b2/transformed/jetified-exoplayer-hls-2.17.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/e2f3bb3755dc489bd132e89dba6351b2/transformed/jetified-exoplayer-hls-2.17.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e2f3bb3755dc489bd132e89dba6351b2/transformed/jetified-exoplayer-hls-2.17.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e2f3bb3755dc489bd132e89dba6351b2/transformed/jetified-exoplayer-hls-2.17.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-hls/2.17.1/83965673f7fb3eb10b59c68b14c54c95ee3bb1b8/exoplayer-hls-2.17.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>