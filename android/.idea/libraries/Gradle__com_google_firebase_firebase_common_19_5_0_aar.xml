<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-common:19.5.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/26c157b93855155e43aa9a928b1f7190/transformed/jetified-firebase-common-19.5.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/45807415b73dbf7e1d6e3200438c6629/transformed/jetified-firebase-common-19.5.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/45807415b73dbf7e1d6e3200438c6629/transformed/jetified-firebase-common-19.5.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/45807415b73dbf7e1d6e3200438c6629/transformed/jetified-firebase-common-19.5.0/jars/classes.jar!/" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/05950ea996a9023258c157038e9dafe9/transformed/jetified-firebase-common-19.5.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/05950ea996a9023258c157038e9dafe9/transformed/jetified-firebase-common-19.5.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/05950ea996a9023258c157038e9dafe9/transformed/jetified-firebase-common-19.5.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-common/19.5.0/74c6d45bd7a7f96f91a986795e875bafc361902b/firebase-common-19.5.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-common/19.5.0/cd6c1ba1a90712022acda0def30cacec1111a9a/firebase-common-19.5.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>