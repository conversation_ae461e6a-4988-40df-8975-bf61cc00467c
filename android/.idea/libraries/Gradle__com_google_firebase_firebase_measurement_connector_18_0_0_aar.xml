<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-measurement-connector:18.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e05eea69880f4ef6283dd9e68c9be132/transformed/jetified-firebase-measurement-connector-18.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/5f97c919def370c0d9ab9f3fea49445a/transformed/jetified-firebase-measurement-connector-18.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/5f97c919def370c0d9ab9f3fea49445a/transformed/jetified-firebase-measurement-connector-18.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/5f97c919def370c0d9ab9f3fea49445a/transformed/jetified-firebase-measurement-connector-18.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/9810ae735c541f9a4de4b416e8047cdf/transformed/jetified-firebase-measurement-connector-18.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9810ae735c541f9a4de4b416e8047cdf/transformed/jetified-firebase-measurement-connector-18.0.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9810ae735c541f9a4de4b416e8047cdf/transformed/jetified-firebase-measurement-connector-18.0.0/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>