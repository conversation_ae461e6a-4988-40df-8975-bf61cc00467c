<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-iid:21.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/811cdaddae933e6fb0c25c043ac1a2a2/transformed/jetified-firebase-iid-21.1.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/184eb9a697b6c84f18b39c3c04959575/transformed/jetified-firebase-iid-21.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/184eb9a697b6c84f18b39c3c04959575/transformed/jetified-firebase-iid-21.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/184eb9a697b6c84f18b39c3c04959575/transformed/jetified-firebase-iid-21.1.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/6bbb33ad5c89fe6fe71c5f5ab1853b86/transformed/jetified-firebase-iid-21.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6bbb33ad5c89fe6fe71c5f5ab1853b86/transformed/jetified-firebase-iid-21.1.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6bbb33ad5c89fe6fe71c5f5ab1853b86/transformed/jetified-firebase-iid-21.1.0/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-iid/21.1.0/99becc43d33ea13642dec8d8c196db391b180b4d/firebase-iid-21.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>