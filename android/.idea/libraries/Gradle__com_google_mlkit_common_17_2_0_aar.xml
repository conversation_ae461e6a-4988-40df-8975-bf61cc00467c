<component name="libraryTable">
  <library name="Gradle: com.google.mlkit:common:17.2.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6f1302ae04e529ab7295f3e4e9f5fe53/transformed/jetified-common-17.2.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/07ece2d126e1f7cd604e48b60c8312bf/transformed/jetified-common-17.2.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/07ece2d126e1f7cd604e48b60c8312bf/transformed/jetified-common-17.2.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/07ece2d126e1f7cd604e48b60c8312bf/transformed/jetified-common-17.2.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e0499c773a3f80959302800c8ed331a3/transformed/jetified-common-17.2.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/e0499c773a3f80959302800c8ed331a3/transformed/jetified-common-17.2.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e0499c773a3f80959302800c8ed331a3/transformed/jetified-common-17.2.0/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.mlkit/common/17.2.0/79c1584a5f35389e648a9265798e6ba4c38a6483/common-17.2.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>