<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-installations-interop:17.0.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/bbe561d6a8280ebe60b380646b25393a/transformed/jetified-firebase-installations-interop-17.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3a89b6a8ddf84b39589c2d2e8fcc5fc2/transformed/jetified-firebase-installations-interop-17.0.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/3a89b6a8ddf84b39589c2d2e8fcc5fc2/transformed/jetified-firebase-installations-interop-17.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3a89b6a8ddf84b39589c2d2e8fcc5fc2/transformed/jetified-firebase-installations-interop-17.0.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/d2b61feb377199f6faf3a8cd056659bf/transformed/jetified-firebase-installations-interop-17.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d2b61feb377199f6faf3a8cd056659bf/transformed/jetified-firebase-installations-interop-17.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d2b61feb377199f6faf3a8cd056659bf/transformed/jetified-firebase-installations-interop-17.0.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-installations-interop/17.0.1/a75db5c1d9ed5d246082b53d2a327e9758ce72d7/firebase-installations-interop-17.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>