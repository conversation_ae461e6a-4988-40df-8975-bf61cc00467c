<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-cloud-messaging:17.0.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7d814e7fd75512a40b10b3709bed6c37/transformed/jetified-play-services-cloud-messaging-17.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/aef6c9bb56f2d54c5aa86a5a080acf15/transformed/jetified-play-services-cloud-messaging-17.0.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/aef6c9bb56f2d54c5aa86a5a080acf15/transformed/jetified-play-services-cloud-messaging-17.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/aef6c9bb56f2d54c5aa86a5a080acf15/transformed/jetified-play-services-cloud-messaging-17.0.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a274d805c07806ef3df8e2be1affec9d/transformed/jetified-play-services-cloud-messaging-17.0.1/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a274d805c07806ef3df8e2be1affec9d/transformed/jetified-play-services-cloud-messaging-17.0.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/a274d805c07806ef3df8e2be1affec9d/transformed/jetified-play-services-cloud-messaging-17.0.1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-cloud-messaging/17.0.1/5b54e40c89bf1882a551b10312acdf03dcf34e82/play-services-cloud-messaging-17.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>