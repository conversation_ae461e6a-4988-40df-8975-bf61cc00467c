<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-datatransport:18.1.5@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c19acc3da1fb686fdec84812107ca204/transformed/jetified-firebase-datatransport-18.1.5/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/73f422b0d1aec651db3f88e3a5009c07/transformed/jetified-firebase-datatransport-18.1.5/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/73f422b0d1aec651db3f88e3a5009c07/transformed/jetified-firebase-datatransport-18.1.5/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/73f422b0d1aec651db3f88e3a5009c07/transformed/jetified-firebase-datatransport-18.1.5/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/e2a93b15f0c88034931babf41de48076/transformed/jetified-firebase-datatransport-18.1.5/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e2a93b15f0c88034931babf41de48076/transformed/jetified-firebase-datatransport-18.1.5/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e2a93b15f0c88034931babf41de48076/transformed/jetified-firebase-datatransport-18.1.5/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-datatransport/18.1.5/6e130e8203fdd09849ef9a05da21490cd08b8502/firebase-datatransport-18.1.5-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-datatransport/18.1.5/6188d73e8c4c71dde690ec8a65c4c02a4ee52dc9/firebase-datatransport-18.1.5-sources.jar!/" />
    </SOURCES>
  </library>
</component>