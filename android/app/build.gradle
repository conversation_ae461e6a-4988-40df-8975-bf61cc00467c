apply plugin: "com.android.application"

apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
import org.apache.tools.ant.taskdefs.condition.Os


/*
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */

react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")
 
    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]
 
    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []
 
    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}
 
/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false
 
/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 */
def jscFlavor = 'org.webkit:android-jsc:+'
 

android {
    ndkVersion rootProject.ext.ndkVersion

    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion
    namespace "com.ModrkClient"


     packagingOptions {
            pickFirst 'lib/x86/libc++_shared.so'
            pickFirst 'lib/arm64-v8a/libc++_shared.so'
            pickFirst 'lib/x86_64/libc++_shared.so'
            pickFirst 'lib/armeabi-v7a/libc++_shared.so'
          }

    defaultConfig {
        multiDexEnabled true
        applicationId "com.ModrkClient"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        missingDimensionStrategy 'react-native-camera', 'general'
        versionCode 14
        versionName "1.1.4"
    //     buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()

    //     if (isNewArchitectureEnabled()) {
    //         // We configure the NDK build only if you decide to opt-in for the New Architecture.
    //         externalNativeBuild {
    //             ndkBuild {
    //                 arguments "APP_PLATFORM=android-21",
    //                     "APP_STL=c++_shared",
    //                     "NDK_TOOLCHAIN_VERSION=clang",
    //                     "GENERATED_SRC_DIR=$buildDir/generated/source",
    //                     "PROJECT_BUILD_DIR=$buildDir",
    //                     "REACT_ANDROID_DIR=$rootDir/../node_modules/react-native/ReactAndroid",
    //                     "REACT_ANDROID_BUILD_DIR=$rootDir/../node_modules/react-native/ReactAndroid/build"
    //                 cFlags "-Wall", "-Werror", "-fexceptions", "-frtti", "-DWITH_INSPECTOR=1"
    //                 cppFlags "-std=c++17"
    //                 // Make sure this target name is the same you specify inside the
    //                 // src/main/jni/Android.mk file for the `LOCAL_MODULE` variable.
    //                 targets "ModrkClient_appmodules"
    //                 // Fix for windows limit on number of character in file paths and in command lines
    //                 if (Os.isFamily(Os.FAMILY_WINDOWS)) {
    //                     arguments "NDK_APP_SHORT_COMMANDS=true"
    //                 }
    //             }
    //         }
    //         if (!enableSeparateBuildPerCPUArchitecture) {
    //             ndk {
    //                 abiFilters (*reactNativeArchitectures())
    //             }
    //         }
    //     }
    // }

    // if (isNewArchitectureEnabled()) {
    //     // We configure the NDK build only if you decide to opt-in for the New Architecture.
    //     externalNativeBuild {
    //         ndkBuild {
    //             path "$projectDir/src/main/jni/Android.mk"
    //         }
    //     }
    //     def reactAndroidProjectDir = project(':ReactAndroid').projectDir
    //     def packageReactNdkDebugLibs = tasks.register("packageReactNdkDebugLibs", Copy) {
    //         dependsOn(":ReactAndroid:packageReactNdkDebugLibsForBuck")
    //         from("$reactAndroidProjectDir/src/main/jni/prebuilt/lib")
    //         into("$buildDir/react-ndk/exported")
    //     }
    //     def packageReactNdkReleaseLibs = tasks.register("packageReactNdkReleaseLibs", Copy) {
    //         dependsOn(":ReactAndroid:packageReactNdkReleaseLibsForBuck")
    //         from("$reactAndroidProjectDir/src/main/jni/prebuilt/lib")
    //         into("$buildDir/react-ndk/exported")
    //     }
    //     afterEvaluate {
    //         // If you wish to add a custom TurboModule or component locally,
    //         // you should uncomment this line.
    //         // preBuild.dependsOn("generateCodegenArtifactsFromSchema")
    //         preDebugBuild.dependsOn(packageReactNdkDebugLibs)
    //         preReleaseBuild.dependsOn(packageReactNdkReleaseLibs)

    //         // Due to a bug inside AGP, we have to explicitly set a dependency
    //         // between configureNdkBuild* tasks and the preBuild tasks.
    //         // This can be removed once this is solved: https://issuetracker.google.com/issues/207403732
    //         configureNdkBuildRelease.dependsOn(preReleaseBuild)
    //         configureNdkBuildDebug.dependsOn(preDebugBuild)
    //         reactNativeArchitectures().each { architecture ->
    //             tasks.findByName("configureNdkBuildDebug[${architecture}]")?.configure {
    //                 dependsOn("preDebugBuild")
    //             }
    //             tasks.findByName("configureNdkBuildRelease[${architecture}]")?.configure {
    //                 dependsOn("preReleaseBuild")
    //             }
    //         }
    //     }
    // }

    // splits {
    //     abi {
    //         reset()
    //         enable enableSeparateBuildPerCPUArchitecture
    //         universalApk false  // If true, also generate a universal APK
    //         include (*reactNativeArchitectures())
    //     }
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            if (project.hasProperty('MYAPP_RELEASE_STORE_FILE')) {
                storeFile file(MYAPP_RELEASE_STORE_FILE)
                storePassword MYAPP_RELEASE_STORE_PASSWORD
                keyAlias MYAPP_RELEASE_KEY_ALIAS
                keyPassword MYAPP_RELEASE_KEY_PASSWORD
            }
        }
    }
    buildFeatures {
        viewBinding true
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    // // applicationVariants are e.g. debug, release
    // applicationVariants.all { variant ->
    //     variant.outputs.each { output ->
    //         // For each separate APK per architecture, set a unique version code as described here:
    //         // https://developer.android.com/studio/build/configure-apk-splits.html
    //         // Example: versionCode 1 will generate 1001 for armeabi-v7a, 1002 for x86, etc.
    //         def versionCodes = ["armeabi-v7a": 1, "x86": 2, "arm64-v8a": 3, "x86_64": 4]
    //         def abi = output.getFilter(OutputFile.ABI)
    //         if (abi != null) {  // null for the universal-debug, universal-release variants
    //             output.versionCodeOverride =
    //                     defaultConfig.versionCode * 1000 + versionCodes.get(abi)
    //         }

    //     }
    // }

    kotlinOptions {
        jvmTarget = "17"
    }
}

dependencies {
    implementation project(':react-native-video')
    implementation 'com.android.support:multidex:2.0.1'
    implementation 'com.google.android.exoplayer:exoplayer:2.17.1'

    implementation 'org.videolan.android:libvlc-all:3.6.0-eap9'     
    implementation 'com.facebook.fresco:animated-gif:2.5.0'
    implementation "androidx.appcompat:appcompat:1.0.0"
    // implementation fileTree(dir: "libs", include: ["*.jar"])

    implementation project(':react-native-push-notification')


    implementation platform('com.google.firebase:firebase-bom:30.3.2')
    implementation 'com.google.firebase:firebase-analytics'

    //noinspection GradleDynamicVersion
    // implementation "com.facebook.react:react-native:+"  // From node_modules

    // implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.navigation:navigation-fragment:2.3.5'
    implementation 'androidx.navigation:navigation-ui:2.3.5'
    // implementation project(':react-native-vector-icons')

    // debugImplementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}") {
    //     exclude group:'com.facebook.fbjni'
    // }

    // debugImplementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
    //     exclude group:'com.facebook.flipper'
    //     exclude group:'com.squareup.okhttp3', module:'okhttp'
    // }

    // debugImplementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}") {
    //     exclude group:'com.facebook.flipper'
    // }

     // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
    implementation project(':react-native-audio-recorder-player')

}

// if (isNewArchitectureEnabled()) {
//     // If new architecture is enabled, we let you build RN from source
//     // Otherwise we fallback to a prebuilt .aar bundled in the NPM package.
//     // This will be applied to all the imported transtitive dependency.
//     configurations.all {
//         resolutionStrategy.dependencySubstitution {
//             substitute(module("com.facebook.react:react-native"))
//                     .using(project(":ReactAndroid")).because("On New Architecture we're building React Native from source")
//         }
//     }
// }

// // Run this once to be able to run the application with BUCK
// // puts all compile dependencies into folder libs for BUCK to use
// task copyDownloadableDepsToLibs(type: Copy) {
//     from configurations.implementation
//     into 'libs'
// }

// apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle")
// apply plugin: 'org.jetbrains.kotlin.android'; applyNativeModulesAppBuildGradle(project)

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); 
applyNativeModulesAppBuildGradle(project)

// def isNewArchitectureEnabled() {
//     // To opt-in for the New Architecture, you can either:
//     // - Set `newArchEnabled` to true inside the `gradle.properties` file
//     // - Invoke gradle with `-newArchEnabled=true`
//     // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
//     return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
// }

tasks.whenTaskAdded((tas -> {
    // when task is 'mergeLocalDebugNativeLibs' or 'mergeLocalReleaseNativeLibs'
    if (tas.name.contains("merge") && tas.name.contains("NativeLibs")) {
        tasks.named(tas.name) {it
            doFirst {
                java.nio.file.Path notNeededDirectory = it.externalLibNativeLibs
                        .getFiles()
                        .stream()
                        // for React Native 0.71, the file value now contains "jetified-react-android" instead of "jetified-react-native"
                        .filter(file -> file.toString().contains("jetified-react-native"))
                        .findAny()
                        .orElse(null)
                        .toPath();
                java.nio.file.Files.walk(notNeededDirectory).forEach(file -> {
                    if (file.toString().contains("libc++_shared.so")) {
                        java.nio.file.Files.delete(file);
                    }
                });
            }
        }
    }
}))


