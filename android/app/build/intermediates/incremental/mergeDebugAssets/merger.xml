<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":react-native-image-resizer" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-image-resizer/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config="org.videolan.android:libvlc-all:3.6.0-eap9" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets"><file name="lua/playlist/mpora.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/mpora.lua"/><file name="lua/playlist/rockbox_fm_presets.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/rockbox_fm_presets.lua"/><file name="lua/playlist/vocaroo.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/vocaroo.lua"/><file name="lua/playlist/anevia_xml.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/anevia_xml.lua"/><file name="lua/playlist/vimeo.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/vimeo.lua"/><file name="lua/playlist/bbc_co_uk.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/bbc_co_uk.lua"/><file name="lua/playlist/anevia_streams.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/anevia_streams.lua"/><file name="lua/playlist/koreus.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/koreus.lua"/><file name="lua/playlist/appletrailers.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/appletrailers.lua"/><file name="lua/playlist/liveleak.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/liveleak.lua"/><file name="lua/playlist/cue.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/cue.lua"/><file name="lua/playlist/jamendo.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/jamendo.lua"/><file name="lua/playlist/zapiks.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/zapiks.lua"/><file name="lua/playlist/pinkbike.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/pinkbike.lua"/><file name="lua/playlist/soundcloud.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/soundcloud.lua"/><file name="lua/playlist/dailymotion.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/dailymotion.lua"/><file name="lua/playlist/youtube.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/youtube.lua"/><file name="lua/playlist/twitch.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/twitch.lua"/><file name="lua/playlist/break.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/break.lua"/><file name="lua/playlist/newgrounds.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/newgrounds.lua"/><file name="lua/playlist/lelombrik.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/lelombrik.lua"/><file name="lua/playlist/extreme.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/extreme.lua"/><file name="lua/playlist/france2.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/france2.lua"/><file name="lua/playlist/katsomo.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/katsomo.lua"/><file name="lua/playlist/metacafe.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/playlist/metacafe.lua"/><file name="lua/meta/reader/filename.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/meta/reader/filename.lua"/><file name="lua/meta/art/02_frenchtv.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/meta/art/02_frenchtv.lua"/><file name="lua/meta/art/00_musicbrainz.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/meta/art/00_musicbrainz.lua"/><file name="lua/meta/art/01_googleimage.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/meta/art/01_googleimage.lua"/><file name="lua/meta/art/03_lastfm.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/meta/art/03_lastfm.lua"/><file name="lua/modules/sandbox.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/modules/sandbox.lua"/><file name="lua/modules/simplexml.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/modules/simplexml.lua"/><file name="lua/modules/dkjson.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/modules/dkjson.lua"/><file name="lua/modules/common.lua" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/lua/modules/common.lua"/><file name="hrtfs/dodeca_and_7channel_3DSL_HRTF.sofa" path="/Users/<USER>/.gradle/caches/transforms-4/ff8bfa9a72f986ef0579f3a03c22e599/transformed/jetified-libvlc-all-3.6.0-eap9/assets/hrtfs/dodeca_and_7channel_3DSL_HRTF.sofa"/></source></dataSet><dataSet config=":rn-fetch-blob" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/rn-fetch-blob/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-webview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-webview/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-vlc-media-player" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-vlc-media-player/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-svg/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-screens/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-safe-area-context/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-restart" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-restart/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-permissions" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-permissions/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-pager-view" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-pager-view/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-maps" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-maps/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-linear-gradient" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-linear-gradient/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-image-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-image-picker/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-i18n" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-i18n/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-gesture-handler/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-geolocation-service" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-geolocation-service/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-fast-image" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-fast-image/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-document-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-document-picker/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-device-info" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-device-info/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-camera" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/build/intermediates/library_assets/generalDebug/packageGeneralDebugAssets/out"/></dataSet><dataSet config=":react-native-masked-view_masked-view" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/@react-native-masked-view/masked-view/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-firebase_app" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/@react-native-firebase/app/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/@react-native-firebase/messaging/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-community_netinfo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/@react-native-community/netinfo/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-community_datetimepicker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/@react-native-community/datetimepicker/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-clipboard_clipboard" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/@react-native-clipboard/clipboard/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-vector-icons/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-audio-recorder-player" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-audio-recorder-player/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-push-notification" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-push-notification/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-video" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-video/android-exoplayer/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets"><file name="index.android" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/index.android"/><file name="fonts/Fontisto.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Fontisto.ttf"/><file name="fonts/Octicons.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Octicons.ttf"/><file name="fonts/Feather.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Feather.ttf"/><file name="fonts/NotoKufiArabic-VariableFont_wght.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/NotoKufiArabic-VariableFont_wght.ttf"/><file name="fonts/Roboto_medium.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Roboto_medium.ttf"/><file name="fonts/Entypo.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Entypo.ttf"/><file name="fonts/FontAwesome5_Brands.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/FontAwesome5_Brands.ttf"/><file name="fonts/MaterialCommunityIcons.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/MaterialCommunityIcons.ttf"/><file name="fonts/Cairo-Medium.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Cairo-Medium.ttf"/><file name="fonts/AntDesign.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/AntDesign.ttf"/><file name="fonts/Foundation.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Foundation.ttf"/><file name="fonts/Ionicons.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Ionicons.ttf"/><file name="fonts/FontAwesome5_Solid.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/FontAwesome5_Solid.ttf"/><file name="fonts/FontAwesome5_Regular.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/FontAwesome5_Regular.ttf"/><file name="fonts/Cairo-Regular.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Cairo-Regular.ttf"/><file name="fonts/rubicon-icon-font.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/rubicon-icon-font.ttf"/><file name="fonts/FontAwesome.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/FontAwesome.ttf"/><file name="fonts/Zocial.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Zocial.ttf"/><file name="fonts/Roboto.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Roboto.ttf"/><file name="fonts/KufiArabic.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/KufiArabic.ttf"/><file name="fonts/EvilIcons.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/EvilIcons.ttf"/><file name="fonts/Cairo-Bold.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/Cairo-Bold.ttf"/><file name="fonts/NotoKufiArabic.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/NotoKufiArabic.ttf"/><file name="fonts/SimpleLineIcons.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/SimpleLineIcons.ttf"/><file name="fonts/MaterialIcons.ttf" path="/Users/<USER>/Projects/ModrkClient/android/app/src/main/assets/fonts/MaterialIcons.ttf"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/android/app/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/android/app/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>