rootProject.name = 'ModrkClient'

include ':app'

// ✅ Native Modules
include ':react-native-vlc-media-player'
project(':react-native-vlc-media-player').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-vlc-media-player/android')

include ':react-native-vlc-rtsp'
project(':react-native-vlc-rtsp').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-vlc-rtsp/android')

include ':react-native-video'
project(':react-native-video').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-video/android-exoplayer')

include ':rn-fetch-blob'
project(':rn-fetch-blob').projectDir = new File(rootProject.projectDir, '../node_modules/rn-fetch-blob/android')

include ':react-native-audio-recorder-player'
project(':react-native-audio-recorder-player').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-audio-recorder-player/android')

// ✅ React Native auto-linking
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle")
applyNativeModulesSettingsGradle(settings)

// ✅ React Native 0.71+ build modules
includeBuild('../node_modules/@react-native/gradle-plugin')

def reactNativeRoot = file("../node_modules/react-native")
includeBuild("${reactNativeRoot}/ReactAndroid")
includeBuild("../node_modules/react-native-codegen")

// ✅ Required for Gradle 7.5+ and RN 0.71+
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    
    versionCatalogs {
        libs {
            from(files("./gradle/libs.versions.toml"))
        }
    }

    repositories {
        google()
        mavenCentral()
        maven { url("$rootDir/../node_modules/react-native/android") }
        maven { url 'https://www.jitpack.io' } // Optional but useful for some 3rd-party libs
    }
}
