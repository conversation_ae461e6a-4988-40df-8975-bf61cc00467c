  Context android.content  ContextWrapper android.content  baseContext android.content.Context  getBASEContext android.content.Context  getBaseContext android.content.Context  getNativeModule android.content.Context  baseContext android.content.ContextWrapper  getBASEContext android.content.ContextWrapper  getBaseContext android.content.ContextWrapper  getNativeModule android.content.ContextWrapper  setBaseContext android.content.ContextWrapper  AttributeSet android.util  MotionEvent android.view  View android.view  ViewConfiguration android.view  	ViewGroup android.view  
ViewParent android.view  ACTION_DOWN android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  action android.view.MotionEvent  	getACTION android.view.MotionEvent  	getAction android.view.MotionEvent  getX android.view.MotionEvent  getY android.view.MotionEvent  	setAction android.view.MotionEvent  setX android.view.MotionEvent  setY android.view.MotionEvent  x android.view.MotionEvent  y android.view.MotionEvent  AttributeSet android.view.View  Boolean android.view.View  Context android.view.View  Float android.view.View  IllegalArgumentException android.view.View  Int android.view.View  LAYOUT_DIRECTION_LTR android.view.View  LAYOUT_DIRECTION_RTL android.view.View  MeasureSpec android.view.View  MotionEvent android.view.View  ORIENTATION_HORIZONTAL android.view.View  View android.view.View  ViewConfiguration android.view.View  
ViewPager2 android.view.View  
absoluteValue android.view.View  addView android.view.View  bottom android.view.View  canChildScroll android.view.View  canScrollHorizontally android.view.View  canScrollVertically android.view.View  equals android.view.View  	getBOTTOM android.view.View  	getBottom android.view.View  
getChildAt android.view.View  
getContext android.view.View  	getHEIGHT android.view.View  	getHeight android.view.View  getLEFT android.view.View  getLeft android.view.View  getOVERScrollMode android.view.View  getOverScrollMode android.view.View  	getPARENT android.view.View  	getParent android.view.View  getRIGHT android.view.View  getRight android.view.View  getTOP android.view.View  getTop android.view.View  getTranslationX android.view.View  getTranslationY android.view.View  getWIDTH android.view.View  getWidth android.view.View  handleInterceptTouchEvent android.view.View  height android.view.View  layout android.view.View  left android.view.View  measure android.view.View  onInterceptTouchEvent android.view.View  overScrollMode android.view.View  parent android.view.View  post android.view.View  registerOnPageChangeCallback android.view.View  removeAllViews android.view.View  
removeView android.view.View  right android.view.View  	setBottom android.view.View  setCurrentItem android.view.View  	setHeight android.view.View  setLeft android.view.View  setOverScrollMode android.view.View  setPageTransformer android.view.View  	setParent android.view.View  setRight android.view.View  setTop android.view.View  setTranslationX android.view.View  setTranslationY android.view.View  setWidth android.view.View  sign android.view.View  top android.view.View  translationX android.view.View  translationY android.view.View  width android.view.View  EXACTLY android.view.View.MeasureSpec  makeMeasureSpec android.view.View.MeasureSpec  get android.view.ViewConfiguration  getSCALEDTouchSlop android.view.ViewConfiguration  getScaledTouchSlop android.view.ViewConfiguration  scaledTouchSlop android.view.ViewConfiguration  setScaledTouchSlop android.view.ViewConfiguration  AttributeSet android.view.ViewGroup  Boolean android.view.ViewGroup  Context android.view.ViewGroup  Float android.view.ViewGroup  IllegalArgumentException android.view.ViewGroup  Int android.view.ViewGroup  LayoutParams android.view.ViewGroup  MotionEvent android.view.ViewGroup  ORIENTATION_HORIZONTAL android.view.ViewGroup  View android.view.ViewGroup  ViewConfiguration android.view.ViewGroup  
ViewPager2 android.view.ViewGroup  
absoluteValue android.view.ViewGroup  addView android.view.ViewGroup  canChildScroll android.view.ViewGroup  context android.view.ViewGroup  
getCONTEXT android.view.ViewGroup  
getChildAt android.view.ViewGroup  
getContext android.view.ViewGroup  handleInterceptTouchEvent android.view.ViewGroup  onInterceptTouchEvent android.view.ViewGroup  post android.view.ViewGroup  registerOnPageChangeCallback android.view.ViewGroup  removeAllViews android.view.ViewGroup  
removeView android.view.ViewGroup  
setContext android.view.ViewGroup  setCurrentItem android.view.ViewGroup  setPageTransformer android.view.ViewGroup  sign android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  equals android.view.ViewParent  	getPARENT android.view.ViewParent  	getParent android.view.ViewParent  parent android.view.ViewParent  "requestDisallowInterceptTouchEvent android.view.ViewParent  	setParent android.view.ViewParent  FrameLayout android.widget  AttributeSet android.widget.FrameLayout  Boolean android.widget.FrameLayout  Context android.widget.FrameLayout  Float android.widget.FrameLayout  IllegalArgumentException android.widget.FrameLayout  Int android.widget.FrameLayout  MotionEvent android.widget.FrameLayout  ORIENTATION_HORIZONTAL android.widget.FrameLayout  View android.widget.FrameLayout  ViewConfiguration android.widget.FrameLayout  
ViewPager2 android.widget.FrameLayout  
absoluteValue android.widget.FrameLayout  addView android.widget.FrameLayout  canChildScroll android.widget.FrameLayout  
childCount android.widget.FrameLayout  
getCHILDCount android.widget.FrameLayout  
getChildAt android.widget.FrameLayout  
getChildCount android.widget.FrameLayout  getISSaveEnabled android.widget.FrameLayout  getIsSaveEnabled android.widget.FrameLayout  getLAYOUTParams android.widget.FrameLayout  getLayoutParams android.widget.FrameLayout  handleInterceptTouchEvent android.widget.FrameLayout  
isSaveEnabled android.widget.FrameLayout  layoutParams android.widget.FrameLayout  onInterceptTouchEvent android.widget.FrameLayout  removeAllViews android.widget.FrameLayout  
removeView android.widget.FrameLayout  
setChildCount android.widget.FrameLayout  setLayoutParams android.widget.FrameLayout  setSaveEnabled android.widget.FrameLayout  sign android.widget.FrameLayout  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  	ArrayList 1androidx.recyclerview.widget.RecyclerView.Adapter  FrameLayout 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  ViewPagerViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  addChild 1androidx.recyclerview.widget.RecyclerView.Adapter  
getChildAt 1androidx.recyclerview.widget.RecyclerView.Adapter  getITEMCount 1androidx.recyclerview.widget.RecyclerView.Adapter  getItemCount 1androidx.recyclerview.widget.RecyclerView.Adapter  	itemCount 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyItemInserted 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyItemRangeRemoved 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyItemRemoved 1androidx.recyclerview.widget.RecyclerView.Adapter  	removeAll 1androidx.recyclerview.widget.RecyclerView.Adapter  removeChild 1androidx.recyclerview.widget.RecyclerView.Adapter  
removeChildAt 1androidx.recyclerview.widget.RecyclerView.Adapter  setItemCount 1androidx.recyclerview.widget.RecyclerView.Adapter  FrameLayout 4androidx.recyclerview.widget.RecyclerView.ViewHolder  MATCH_PARENT 4androidx.recyclerview.widget.RecyclerView.ViewHolder  	ViewGroup 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ViewPagerViewHolder 4androidx.recyclerview.widget.RecyclerView.ViewHolder  invoke 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
ViewPager2 androidx.viewpager2.widget  OFFSCREEN_PAGE_LIMIT_DEFAULT %androidx.viewpager2.widget.ViewPager2  ORIENTATION_HORIZONTAL %androidx.viewpager2.widget.ViewPager2  ORIENTATION_VERTICAL %androidx.viewpager2.widget.ViewPager2  OVER_SCROLL_ALWAYS %androidx.viewpager2.widget.ViewPager2  OVER_SCROLL_IF_CONTENT_SCROLLS %androidx.viewpager2.widget.ViewPager2  OVER_SCROLL_NEVER %androidx.viewpager2.widget.ViewPager2  OnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  SCROLL_STATE_DRAGGING %androidx.viewpager2.widget.ViewPager2  SCROLL_STATE_IDLE %androidx.viewpager2.widget.ViewPager2  SCROLL_STATE_SETTLING %androidx.viewpager2.widget.ViewPager2  adapter %androidx.viewpager2.widget.ViewPager2  currentItem %androidx.viewpager2.widget.ViewPager2  
getADAPTER %androidx.viewpager2.widget.ViewPager2  
getAdapter %androidx.viewpager2.widget.ViewPager2  getCURRENTItem %androidx.viewpager2.widget.ViewPager2  
getChildAt %androidx.viewpager2.widget.ViewPager2  getCurrentItem %androidx.viewpager2.widget.ViewPager2  getISSaveEnabled %androidx.viewpager2.widget.ViewPager2  getISUserInputEnabled %androidx.viewpager2.widget.ViewPager2  getIsSaveEnabled %androidx.viewpager2.widget.ViewPager2  getIsUserInputEnabled %androidx.viewpager2.widget.ViewPager2  getLAYOUTDirection %androidx.viewpager2.widget.ViewPager2  getLayoutDirection %androidx.viewpager2.widget.ViewPager2  getOFFSCREENPageLimit %androidx.viewpager2.widget.ViewPager2  getORIENTATION %androidx.viewpager2.widget.ViewPager2  getOffscreenPageLimit %androidx.viewpager2.widget.ViewPager2  getOrientation %androidx.viewpager2.widget.ViewPager2  
isSaveEnabled %androidx.viewpager2.widget.ViewPager2  isUserInputEnabled %androidx.viewpager2.widget.ViewPager2  layoutDirection %androidx.viewpager2.widget.ViewPager2  offscreenPageLimit %androidx.viewpager2.widget.ViewPager2  orientation %androidx.viewpager2.widget.ViewPager2  post %androidx.viewpager2.widget.ViewPager2  registerOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  
setAdapter %androidx.viewpager2.widget.ViewPager2  setCurrentItem %androidx.viewpager2.widget.ViewPager2  setLayoutDirection %androidx.viewpager2.widget.ViewPager2  setOffscreenPageLimit %androidx.viewpager2.widget.ViewPager2  setOrientation %androidx.viewpager2.widget.ViewPager2  setPageTransformer %androidx.viewpager2.widget.ViewPager2  setSaveEnabled %androidx.viewpager2.widget.ViewPager2  setUserInputEnabled %androidx.viewpager2.widget.ViewPager2  Float :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  IllegalStateException :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  Int :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  PageScrollEvent :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  PageScrollStateChangedEvent :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  PageSelectedEvent :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  String :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  
ViewPager2 :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  eventDispatcher :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  invoke :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  onPageScrollStateChanged :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  onPageScrolled :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  onPageSelected :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  <SAM-CONSTRUCTOR> 5androidx.viewpager2.widget.ViewPager2.PageTransformer  
Assertions com.facebook.infer.annotation  
assertNotNull (com.facebook.infer.annotation.Assertions  ReactPackage com.facebook.react  	Arguments com.facebook.react.bridge  NativeModule com.facebook.react.bridge  ReactApplicationContext com.facebook.react.bridge  ReactContext com.facebook.react.bridge  
ReadableArray com.facebook.react.bridge  WritableMap com.facebook.react.bridge  	createMap #com.facebook.react.bridge.Arguments  
Assertions (com.facebook.react.bridge.BaseJavaModule  Boolean (com.facebook.react.bridge.BaseJavaModule  COMMAND_SET_PAGE (com.facebook.react.bridge.BaseJavaModule  "COMMAND_SET_PAGE_WITHOUT_ANIMATION (com.facebook.react.bridge.BaseJavaModule  COMMAND_SET_SCROLL_ENABLED (com.facebook.react.bridge.BaseJavaModule  ClassNotFoundException (com.facebook.react.bridge.BaseJavaModule  EventDispatcher (com.facebook.react.bridge.BaseJavaModule  Float (com.facebook.react.bridge.BaseJavaModule  IllegalArgumentException (com.facebook.react.bridge.BaseJavaModule  IllegalStateException (com.facebook.react.bridge.BaseJavaModule  Int (com.facebook.react.bridge.BaseJavaModule  Map (com.facebook.react.bridge.BaseJavaModule  
MapBuilder (com.facebook.react.bridge.BaseJavaModule  
MutableMap (com.facebook.react.bridge.BaseJavaModule  NestedScrollableHost (com.facebook.react.bridge.BaseJavaModule  OnPageChangeCallback (com.facebook.react.bridge.BaseJavaModule  PageScrollEvent (com.facebook.react.bridge.BaseJavaModule  PageScrollStateChangedEvent (com.facebook.react.bridge.BaseJavaModule  PageSelectedEvent (com.facebook.react.bridge.BaseJavaModule  	PixelUtil (com.facebook.react.bridge.BaseJavaModule  REACT_CLASS (com.facebook.react.bridge.BaseJavaModule  	ReactProp (com.facebook.react.bridge.BaseJavaModule  
ReadableArray (com.facebook.react.bridge.BaseJavaModule  String (com.facebook.react.bridge.BaseJavaModule  ThemedReactContext (com.facebook.react.bridge.BaseJavaModule  UIManagerModule (com.facebook.react.bridge.BaseJavaModule  View (com.facebook.react.bridge.BaseJavaModule  	ViewGroup (com.facebook.react.bridge.BaseJavaModule  
ViewPager2 (com.facebook.react.bridge.BaseJavaModule  ViewPagerAdapter (com.facebook.react.bridge.BaseJavaModule  eventDispatcher (com.facebook.react.bridge.BaseJavaModule  format (com.facebook.react.bridge.BaseJavaModule  getViewPager (com.facebook.react.bridge.BaseJavaModule  invoke (com.facebook.react.bridge.BaseJavaModule  java (com.facebook.react.bridge.BaseJavaModule  	javaClass (com.facebook.react.bridge.BaseJavaModule  receiveCommand (com.facebook.react.bridge.BaseJavaModule  refreshViewChildrenLayout (com.facebook.react.bridge.BaseJavaModule  setCurrentItem (com.facebook.react.bridge.BaseJavaModule  getNativeModule &com.facebook.react.bridge.ReactContext  
getBoolean 'com.facebook.react.bridge.ReadableArray  getInt 'com.facebook.react.bridge.ReadableArray  	putDouble %com.facebook.react.bridge.WritableMap  putInt %com.facebook.react.bridge.WritableMap  	putString %com.facebook.react.bridge.WritableMap  
MapBuilder com.facebook.react.common  of $com.facebook.react.common.MapBuilder  	PixelUtil com.facebook.react.uimanager  ThemedReactContext com.facebook.react.uimanager  UIManagerModule com.facebook.react.uimanager  ViewGroupManager com.facebook.react.uimanager  ViewManager com.facebook.react.uimanager  
Assertions ,com.facebook.react.uimanager.BaseViewManager  Boolean ,com.facebook.react.uimanager.BaseViewManager  COMMAND_SET_PAGE ,com.facebook.react.uimanager.BaseViewManager  "COMMAND_SET_PAGE_WITHOUT_ANIMATION ,com.facebook.react.uimanager.BaseViewManager  COMMAND_SET_SCROLL_ENABLED ,com.facebook.react.uimanager.BaseViewManager  ClassNotFoundException ,com.facebook.react.uimanager.BaseViewManager  EventDispatcher ,com.facebook.react.uimanager.BaseViewManager  Float ,com.facebook.react.uimanager.BaseViewManager  IllegalArgumentException ,com.facebook.react.uimanager.BaseViewManager  IllegalStateException ,com.facebook.react.uimanager.BaseViewManager  Int ,com.facebook.react.uimanager.BaseViewManager  Map ,com.facebook.react.uimanager.BaseViewManager  
MapBuilder ,com.facebook.react.uimanager.BaseViewManager  
MutableMap ,com.facebook.react.uimanager.BaseViewManager  NestedScrollableHost ,com.facebook.react.uimanager.BaseViewManager  OnPageChangeCallback ,com.facebook.react.uimanager.BaseViewManager  PageScrollEvent ,com.facebook.react.uimanager.BaseViewManager  PageScrollStateChangedEvent ,com.facebook.react.uimanager.BaseViewManager  PageSelectedEvent ,com.facebook.react.uimanager.BaseViewManager  	PixelUtil ,com.facebook.react.uimanager.BaseViewManager  REACT_CLASS ,com.facebook.react.uimanager.BaseViewManager  	ReactProp ,com.facebook.react.uimanager.BaseViewManager  
ReadableArray ,com.facebook.react.uimanager.BaseViewManager  String ,com.facebook.react.uimanager.BaseViewManager  ThemedReactContext ,com.facebook.react.uimanager.BaseViewManager  UIManagerModule ,com.facebook.react.uimanager.BaseViewManager  View ,com.facebook.react.uimanager.BaseViewManager  	ViewGroup ,com.facebook.react.uimanager.BaseViewManager  
ViewPager2 ,com.facebook.react.uimanager.BaseViewManager  ViewPagerAdapter ,com.facebook.react.uimanager.BaseViewManager  eventDispatcher ,com.facebook.react.uimanager.BaseViewManager  format ,com.facebook.react.uimanager.BaseViewManager  getViewPager ,com.facebook.react.uimanager.BaseViewManager  invoke ,com.facebook.react.uimanager.BaseViewManager  java ,com.facebook.react.uimanager.BaseViewManager  	javaClass ,com.facebook.react.uimanager.BaseViewManager  receiveCommand ,com.facebook.react.uimanager.BaseViewManager  refreshViewChildrenLayout ,com.facebook.react.uimanager.BaseViewManager  setCurrentItem ,com.facebook.react.uimanager.BaseViewManager  toPixelFromDIP &com.facebook.react.uimanager.PixelUtil  getNativeModule /com.facebook.react.uimanager.ThemedReactContext  eventDispatcher ,com.facebook.react.uimanager.UIManagerModule  getEVENTDispatcher ,com.facebook.react.uimanager.UIManagerModule  getEventDispatcher ,com.facebook.react.uimanager.UIManagerModule  setEventDispatcher ,com.facebook.react.uimanager.UIManagerModule  
Assertions -com.facebook.react.uimanager.ViewGroupManager  Boolean -com.facebook.react.uimanager.ViewGroupManager  COMMAND_SET_PAGE -com.facebook.react.uimanager.ViewGroupManager  "COMMAND_SET_PAGE_WITHOUT_ANIMATION -com.facebook.react.uimanager.ViewGroupManager  COMMAND_SET_SCROLL_ENABLED -com.facebook.react.uimanager.ViewGroupManager  ClassNotFoundException -com.facebook.react.uimanager.ViewGroupManager  EventDispatcher -com.facebook.react.uimanager.ViewGroupManager  Float -com.facebook.react.uimanager.ViewGroupManager  IllegalArgumentException -com.facebook.react.uimanager.ViewGroupManager  IllegalStateException -com.facebook.react.uimanager.ViewGroupManager  Int -com.facebook.react.uimanager.ViewGroupManager  Map -com.facebook.react.uimanager.ViewGroupManager  
MapBuilder -com.facebook.react.uimanager.ViewGroupManager  
MutableMap -com.facebook.react.uimanager.ViewGroupManager  NestedScrollableHost -com.facebook.react.uimanager.ViewGroupManager  OnPageChangeCallback -com.facebook.react.uimanager.ViewGroupManager  PageScrollEvent -com.facebook.react.uimanager.ViewGroupManager  PageScrollStateChangedEvent -com.facebook.react.uimanager.ViewGroupManager  PageSelectedEvent -com.facebook.react.uimanager.ViewGroupManager  	PixelUtil -com.facebook.react.uimanager.ViewGroupManager  REACT_CLASS -com.facebook.react.uimanager.ViewGroupManager  	ReactProp -com.facebook.react.uimanager.ViewGroupManager  
ReadableArray -com.facebook.react.uimanager.ViewGroupManager  String -com.facebook.react.uimanager.ViewGroupManager  ThemedReactContext -com.facebook.react.uimanager.ViewGroupManager  UIManagerModule -com.facebook.react.uimanager.ViewGroupManager  View -com.facebook.react.uimanager.ViewGroupManager  	ViewGroup -com.facebook.react.uimanager.ViewGroupManager  
ViewPager2 -com.facebook.react.uimanager.ViewGroupManager  ViewPagerAdapter -com.facebook.react.uimanager.ViewGroupManager  eventDispatcher -com.facebook.react.uimanager.ViewGroupManager  format -com.facebook.react.uimanager.ViewGroupManager  getViewPager -com.facebook.react.uimanager.ViewGroupManager  invoke -com.facebook.react.uimanager.ViewGroupManager  java -com.facebook.react.uimanager.ViewGroupManager  	javaClass -com.facebook.react.uimanager.ViewGroupManager  receiveCommand -com.facebook.react.uimanager.ViewGroupManager  refreshViewChildrenLayout -com.facebook.react.uimanager.ViewGroupManager  setCurrentItem -com.facebook.react.uimanager.ViewGroupManager  
Assertions (com.facebook.react.uimanager.ViewManager  Boolean (com.facebook.react.uimanager.ViewManager  COMMAND_SET_PAGE (com.facebook.react.uimanager.ViewManager  "COMMAND_SET_PAGE_WITHOUT_ANIMATION (com.facebook.react.uimanager.ViewManager  COMMAND_SET_SCROLL_ENABLED (com.facebook.react.uimanager.ViewManager  ClassNotFoundException (com.facebook.react.uimanager.ViewManager  EventDispatcher (com.facebook.react.uimanager.ViewManager  Float (com.facebook.react.uimanager.ViewManager  IllegalArgumentException (com.facebook.react.uimanager.ViewManager  IllegalStateException (com.facebook.react.uimanager.ViewManager  Int (com.facebook.react.uimanager.ViewManager  Map (com.facebook.react.uimanager.ViewManager  
MapBuilder (com.facebook.react.uimanager.ViewManager  
MutableMap (com.facebook.react.uimanager.ViewManager  NestedScrollableHost (com.facebook.react.uimanager.ViewManager  OnPageChangeCallback (com.facebook.react.uimanager.ViewManager  PageScrollEvent (com.facebook.react.uimanager.ViewManager  PageScrollStateChangedEvent (com.facebook.react.uimanager.ViewManager  PageSelectedEvent (com.facebook.react.uimanager.ViewManager  	PixelUtil (com.facebook.react.uimanager.ViewManager  REACT_CLASS (com.facebook.react.uimanager.ViewManager  	ReactProp (com.facebook.react.uimanager.ViewManager  
ReadableArray (com.facebook.react.uimanager.ViewManager  String (com.facebook.react.uimanager.ViewManager  ThemedReactContext (com.facebook.react.uimanager.ViewManager  UIManagerModule (com.facebook.react.uimanager.ViewManager  View (com.facebook.react.uimanager.ViewManager  	ViewGroup (com.facebook.react.uimanager.ViewManager  
ViewPager2 (com.facebook.react.uimanager.ViewManager  ViewPagerAdapter (com.facebook.react.uimanager.ViewManager  eventDispatcher (com.facebook.react.uimanager.ViewManager  format (com.facebook.react.uimanager.ViewManager  getViewPager (com.facebook.react.uimanager.ViewManager  invoke (com.facebook.react.uimanager.ViewManager  java (com.facebook.react.uimanager.ViewManager  	javaClass (com.facebook.react.uimanager.ViewManager  receiveCommand (com.facebook.react.uimanager.ViewManager  refreshViewChildrenLayout (com.facebook.react.uimanager.ViewManager  setCurrentItem (com.facebook.react.uimanager.ViewManager  	ReactProp (com.facebook.react.uimanager.annotations  Event #com.facebook.react.uimanager.events  EventDispatcher #com.facebook.react.uimanager.events  RCTEventEmitter #com.facebook.react.uimanager.events  	Arguments )com.facebook.react.uimanager.events.Event  
EVENT_NAME )com.facebook.react.uimanager.events.Event  Float )com.facebook.react.uimanager.events.Event  Int )com.facebook.react.uimanager.events.Event  RCTEventEmitter )com.facebook.react.uimanager.events.Event  String )com.facebook.react.uimanager.events.Event  WritableMap )com.facebook.react.uimanager.events.Event  
isInfinite )com.facebook.react.uimanager.events.Event  isNaN )com.facebook.react.uimanager.events.Event  serializeEventData )com.facebook.react.uimanager.events.Event  
dispatchEvent 3com.facebook.react.uimanager.events.EventDispatcher  receiveEvent 3com.facebook.react.uimanager.events.RCTEventEmitter  	ArrayList com.reactnativepagerview  
Assertions com.reactnativepagerview  Boolean com.reactnativepagerview  COMMAND_SET_PAGE com.reactnativepagerview  "COMMAND_SET_PAGE_WITHOUT_ANIMATION com.reactnativepagerview  COMMAND_SET_SCROLL_ENABLED com.reactnativepagerview  ClassNotFoundException com.reactnativepagerview  Float com.reactnativepagerview  FrameLayout com.reactnativepagerview  Helper com.reactnativepagerview  IllegalArgumentException com.reactnativepagerview  IllegalStateException com.reactnativepagerview  Int com.reactnativepagerview  List com.reactnativepagerview  MATCH_PARENT com.reactnativepagerview  Map com.reactnativepagerview  
MapBuilder com.reactnativepagerview  MotionEvent com.reactnativepagerview  
MutableMap com.reactnativepagerview  NestedScrollableHost com.reactnativepagerview  ORIENTATION_HORIZONTAL com.reactnativepagerview  PageScrollEvent com.reactnativepagerview  PageScrollStateChangedEvent com.reactnativepagerview  PageSelectedEvent com.reactnativepagerview  PagerViewPackage com.reactnativepagerview  PagerViewViewManager com.reactnativepagerview  	PixelUtil com.reactnativepagerview  REACT_CLASS com.reactnativepagerview  String com.reactnativepagerview  UIManagerModule com.reactnativepagerview  View com.reactnativepagerview  ViewConfiguration com.reactnativepagerview  	ViewGroup com.reactnativepagerview  
ViewPager2 com.reactnativepagerview  ViewPagerAdapter com.reactnativepagerview  ViewPagerViewHolder com.reactnativepagerview  	emptyList com.reactnativepagerview  eventDispatcher com.reactnativepagerview  format com.reactnativepagerview  invoke com.reactnativepagerview  java com.reactnativepagerview  	javaClass com.reactnativepagerview  listOf com.reactnativepagerview  Context com.reactnativepagerview.Helper  ContextWrapper com.reactnativepagerview.Helper  ReactContext com.reactnativepagerview.Helper  View com.reactnativepagerview.Helper  Context )com.reactnativepagerview.Helper.Companion  ContextWrapper )com.reactnativepagerview.Helper.Companion  ReactContext )com.reactnativepagerview.Helper.Companion  View )com.reactnativepagerview.Helper.Companion  AttributeSet -com.reactnativepagerview.NestedScrollableHost  Boolean -com.reactnativepagerview.NestedScrollableHost  Context -com.reactnativepagerview.NestedScrollableHost  Float -com.reactnativepagerview.NestedScrollableHost  IllegalArgumentException -com.reactnativepagerview.NestedScrollableHost  Int -com.reactnativepagerview.NestedScrollableHost  MotionEvent -com.reactnativepagerview.NestedScrollableHost  ORIENTATION_HORIZONTAL -com.reactnativepagerview.NestedScrollableHost  View -com.reactnativepagerview.NestedScrollableHost  ViewConfiguration -com.reactnativepagerview.NestedScrollableHost  
ViewPager2 -com.reactnativepagerview.NestedScrollableHost  
absoluteValue -com.reactnativepagerview.NestedScrollableHost  addView -com.reactnativepagerview.NestedScrollableHost  canChildScroll -com.reactnativepagerview.NestedScrollableHost  child -com.reactnativepagerview.NestedScrollableHost  
childCount -com.reactnativepagerview.NestedScrollableHost  context -com.reactnativepagerview.NestedScrollableHost  didSetInitialIndex -com.reactnativepagerview.NestedScrollableHost  
getCHILDCount -com.reactnativepagerview.NestedScrollableHost  
getCONTEXT -com.reactnativepagerview.NestedScrollableHost  
getChildAt -com.reactnativepagerview.NestedScrollableHost  
getChildCount -com.reactnativepagerview.NestedScrollableHost  
getContext -com.reactnativepagerview.NestedScrollableHost  getID -com.reactnativepagerview.NestedScrollableHost  getISSaveEnabled -com.reactnativepagerview.NestedScrollableHost  getId -com.reactnativepagerview.NestedScrollableHost  getIsSaveEnabled -com.reactnativepagerview.NestedScrollableHost  getLAYOUTParams -com.reactnativepagerview.NestedScrollableHost  getLayoutParams -com.reactnativepagerview.NestedScrollableHost  	getPARENT -com.reactnativepagerview.NestedScrollableHost  	getParent -com.reactnativepagerview.NestedScrollableHost  handleInterceptTouchEvent -com.reactnativepagerview.NestedScrollableHost  id -com.reactnativepagerview.NestedScrollableHost  initialIndex -com.reactnativepagerview.NestedScrollableHost  initialX -com.reactnativepagerview.NestedScrollableHost  initialY -com.reactnativepagerview.NestedScrollableHost  
isSaveEnabled -com.reactnativepagerview.NestedScrollableHost  layoutParams -com.reactnativepagerview.NestedScrollableHost  parent -com.reactnativepagerview.NestedScrollableHost  parentViewPager -com.reactnativepagerview.NestedScrollableHost  
setChildCount -com.reactnativepagerview.NestedScrollableHost  
setContext -com.reactnativepagerview.NestedScrollableHost  setId -com.reactnativepagerview.NestedScrollableHost  setLayoutParams -com.reactnativepagerview.NestedScrollableHost  	setParent -com.reactnativepagerview.NestedScrollableHost  setSaveEnabled -com.reactnativepagerview.NestedScrollableHost  sign -com.reactnativepagerview.NestedScrollableHost  	touchSlop -com.reactnativepagerview.NestedScrollableHost  List )com.reactnativepagerview.PagerViewPackage  NativeModule )com.reactnativepagerview.PagerViewPackage  PagerViewViewManager )com.reactnativepagerview.PagerViewPackage  ReactApplicationContext )com.reactnativepagerview.PagerViewPackage  ViewManager )com.reactnativepagerview.PagerViewPackage  	emptyList )com.reactnativepagerview.PagerViewPackage  getEMPTYList )com.reactnativepagerview.PagerViewPackage  getEmptyList )com.reactnativepagerview.PagerViewPackage  	getLISTOf )com.reactnativepagerview.PagerViewPackage  	getListOf )com.reactnativepagerview.PagerViewPackage  invoke )com.reactnativepagerview.PagerViewPackage  listOf )com.reactnativepagerview.PagerViewPackage  
Assertions -com.reactnativepagerview.PagerViewViewManager  Boolean -com.reactnativepagerview.PagerViewViewManager  COMMAND_SET_PAGE -com.reactnativepagerview.PagerViewViewManager  "COMMAND_SET_PAGE_WITHOUT_ANIMATION -com.reactnativepagerview.PagerViewViewManager  COMMAND_SET_SCROLL_ENABLED -com.reactnativepagerview.PagerViewViewManager  ClassNotFoundException -com.reactnativepagerview.PagerViewViewManager  EventDispatcher -com.reactnativepagerview.PagerViewViewManager  Float -com.reactnativepagerview.PagerViewViewManager  IllegalArgumentException -com.reactnativepagerview.PagerViewViewManager  IllegalStateException -com.reactnativepagerview.PagerViewViewManager  Int -com.reactnativepagerview.PagerViewViewManager  Map -com.reactnativepagerview.PagerViewViewManager  
MapBuilder -com.reactnativepagerview.PagerViewViewManager  
MutableMap -com.reactnativepagerview.PagerViewViewManager  NestedScrollableHost -com.reactnativepagerview.PagerViewViewManager  OnPageChangeCallback -com.reactnativepagerview.PagerViewViewManager  PageScrollEvent -com.reactnativepagerview.PagerViewViewManager  PageScrollStateChangedEvent -com.reactnativepagerview.PagerViewViewManager  PageSelectedEvent -com.reactnativepagerview.PagerViewViewManager  	PixelUtil -com.reactnativepagerview.PagerViewViewManager  REACT_CLASS -com.reactnativepagerview.PagerViewViewManager  	ReactProp -com.reactnativepagerview.PagerViewViewManager  
ReadableArray -com.reactnativepagerview.PagerViewViewManager  String -com.reactnativepagerview.PagerViewViewManager  ThemedReactContext -com.reactnativepagerview.PagerViewViewManager  UIManagerModule -com.reactnativepagerview.PagerViewViewManager  View -com.reactnativepagerview.PagerViewViewManager  	ViewGroup -com.reactnativepagerview.PagerViewViewManager  
ViewPager2 -com.reactnativepagerview.PagerViewViewManager  ViewPagerAdapter -com.reactnativepagerview.PagerViewViewManager  eventDispatcher -com.reactnativepagerview.PagerViewViewManager  format -com.reactnativepagerview.PagerViewViewManager  	getFORMAT -com.reactnativepagerview.PagerViewViewManager  	getFormat -com.reactnativepagerview.PagerViewViewManager  getJAVAClass -com.reactnativepagerview.PagerViewViewManager  getJavaClass -com.reactnativepagerview.PagerViewViewManager  getViewPager -com.reactnativepagerview.PagerViewViewManager  invoke -com.reactnativepagerview.PagerViewViewManager  java -com.reactnativepagerview.PagerViewViewManager  	javaClass -com.reactnativepagerview.PagerViewViewManager  refreshViewChildrenLayout -com.reactnativepagerview.PagerViewViewManager  setCurrentItem -com.reactnativepagerview.PagerViewViewManager  
Assertions 7com.reactnativepagerview.PagerViewViewManager.Companion  Boolean 7com.reactnativepagerview.PagerViewViewManager.Companion  COMMAND_SET_PAGE 7com.reactnativepagerview.PagerViewViewManager.Companion  "COMMAND_SET_PAGE_WITHOUT_ANIMATION 7com.reactnativepagerview.PagerViewViewManager.Companion  COMMAND_SET_SCROLL_ENABLED 7com.reactnativepagerview.PagerViewViewManager.Companion  ClassNotFoundException 7com.reactnativepagerview.PagerViewViewManager.Companion  EventDispatcher 7com.reactnativepagerview.PagerViewViewManager.Companion  Float 7com.reactnativepagerview.PagerViewViewManager.Companion  IllegalArgumentException 7com.reactnativepagerview.PagerViewViewManager.Companion  IllegalStateException 7com.reactnativepagerview.PagerViewViewManager.Companion  Int 7com.reactnativepagerview.PagerViewViewManager.Companion  Map 7com.reactnativepagerview.PagerViewViewManager.Companion  
MapBuilder 7com.reactnativepagerview.PagerViewViewManager.Companion  
MutableMap 7com.reactnativepagerview.PagerViewViewManager.Companion  NestedScrollableHost 7com.reactnativepagerview.PagerViewViewManager.Companion  OnPageChangeCallback 7com.reactnativepagerview.PagerViewViewManager.Companion  PageScrollEvent 7com.reactnativepagerview.PagerViewViewManager.Companion  PageScrollStateChangedEvent 7com.reactnativepagerview.PagerViewViewManager.Companion  PageSelectedEvent 7com.reactnativepagerview.PagerViewViewManager.Companion  	PixelUtil 7com.reactnativepagerview.PagerViewViewManager.Companion  REACT_CLASS 7com.reactnativepagerview.PagerViewViewManager.Companion  	ReactProp 7com.reactnativepagerview.PagerViewViewManager.Companion  
ReadableArray 7com.reactnativepagerview.PagerViewViewManager.Companion  String 7com.reactnativepagerview.PagerViewViewManager.Companion  ThemedReactContext 7com.reactnativepagerview.PagerViewViewManager.Companion  UIManagerModule 7com.reactnativepagerview.PagerViewViewManager.Companion  View 7com.reactnativepagerview.PagerViewViewManager.Companion  	ViewGroup 7com.reactnativepagerview.PagerViewViewManager.Companion  
ViewPager2 7com.reactnativepagerview.PagerViewViewManager.Companion  ViewPagerAdapter 7com.reactnativepagerview.PagerViewViewManager.Companion  eventDispatcher 7com.reactnativepagerview.PagerViewViewManager.Companion  format 7com.reactnativepagerview.PagerViewViewManager.Companion  	getFORMAT 7com.reactnativepagerview.PagerViewViewManager.Companion  	getFormat 7com.reactnativepagerview.PagerViewViewManager.Companion  invoke 7com.reactnativepagerview.PagerViewViewManager.Companion  java 7com.reactnativepagerview.PagerViewViewManager.Companion  	javaClass 7com.reactnativepagerview.PagerViewViewManager.Companion  getEVENTDispatcher _com.reactnativepagerview.PagerViewViewManager.createViewInstance.<anonymous>.<no name provided>  getEventDispatcher _com.reactnativepagerview.PagerViewViewManager.createViewInstance.<anonymous>.<no name provided>  	ArrayList )com.reactnativepagerview.ViewPagerAdapter  FrameLayout )com.reactnativepagerview.ViewPagerAdapter  Int )com.reactnativepagerview.ViewPagerAdapter  View )com.reactnativepagerview.ViewPagerAdapter  	ViewGroup )com.reactnativepagerview.ViewPagerAdapter  ViewPagerViewHolder )com.reactnativepagerview.ViewPagerAdapter  addChild )com.reactnativepagerview.ViewPagerAdapter  
childrenViews )com.reactnativepagerview.ViewPagerAdapter  
getChildAt )com.reactnativepagerview.ViewPagerAdapter  notifyItemInserted )com.reactnativepagerview.ViewPagerAdapter  notifyItemRangeRemoved )com.reactnativepagerview.ViewPagerAdapter  notifyItemRemoved )com.reactnativepagerview.ViewPagerAdapter  	removeAll )com.reactnativepagerview.ViewPagerAdapter  removeChild )com.reactnativepagerview.ViewPagerAdapter  
removeChildAt )com.reactnativepagerview.ViewPagerAdapter  FrameLayout ,com.reactnativepagerview.ViewPagerViewHolder  MATCH_PARENT ,com.reactnativepagerview.ViewPagerViewHolder  	ViewGroup ,com.reactnativepagerview.ViewPagerViewHolder  ViewPagerViewHolder ,com.reactnativepagerview.ViewPagerViewHolder  	container ,com.reactnativepagerview.ViewPagerViewHolder  create ,com.reactnativepagerview.ViewPagerViewHolder  invoke ,com.reactnativepagerview.ViewPagerViewHolder  itemView ,com.reactnativepagerview.ViewPagerViewHolder  FrameLayout 6com.reactnativepagerview.ViewPagerViewHolder.Companion  MATCH_PARENT 6com.reactnativepagerview.ViewPagerViewHolder.Companion  	ViewGroup 6com.reactnativepagerview.ViewPagerViewHolder.Companion  ViewPagerViewHolder 6com.reactnativepagerview.ViewPagerViewHolder.Companion  create 6com.reactnativepagerview.ViewPagerViewHolder.Companion  invoke 6com.reactnativepagerview.ViewPagerViewHolder.Companion  	Arguments com.reactnativepagerview.event  
EVENT_NAME com.reactnativepagerview.event  Float com.reactnativepagerview.event  Int com.reactnativepagerview.event  PageScrollEvent com.reactnativepagerview.event  PageScrollStateChangedEvent com.reactnativepagerview.event  PageSelectedEvent com.reactnativepagerview.event  String com.reactnativepagerview.event  
isInfinite com.reactnativepagerview.event  isNaN com.reactnativepagerview.event  	Arguments .com.reactnativepagerview.event.PageScrollEvent  
EVENT_NAME .com.reactnativepagerview.event.PageScrollEvent  Float .com.reactnativepagerview.event.PageScrollEvent  Int .com.reactnativepagerview.event.PageScrollEvent  RCTEventEmitter .com.reactnativepagerview.event.PageScrollEvent  String .com.reactnativepagerview.event.PageScrollEvent  WritableMap .com.reactnativepagerview.event.PageScrollEvent  	eventName .com.reactnativepagerview.event.PageScrollEvent  getEVENTName .com.reactnativepagerview.event.PageScrollEvent  getEventName .com.reactnativepagerview.event.PageScrollEvent  
getISInfinite .com.reactnativepagerview.event.PageScrollEvent  getISNaN .com.reactnativepagerview.event.PageScrollEvent  
getIsInfinite .com.reactnativepagerview.event.PageScrollEvent  getIsNaN .com.reactnativepagerview.event.PageScrollEvent  
getVIEWTag .com.reactnativepagerview.event.PageScrollEvent  
getViewTag .com.reactnativepagerview.event.PageScrollEvent  
isInfinite .com.reactnativepagerview.event.PageScrollEvent  isNaN .com.reactnativepagerview.event.PageScrollEvent  mOffset .com.reactnativepagerview.event.PageScrollEvent  	mPosition .com.reactnativepagerview.event.PageScrollEvent  serializeEventData .com.reactnativepagerview.event.PageScrollEvent  setEventName .com.reactnativepagerview.event.PageScrollEvent  
setViewTag .com.reactnativepagerview.event.PageScrollEvent  viewTag .com.reactnativepagerview.event.PageScrollEvent  	Arguments 8com.reactnativepagerview.event.PageScrollEvent.Companion  
EVENT_NAME 8com.reactnativepagerview.event.PageScrollEvent.Companion  Float 8com.reactnativepagerview.event.PageScrollEvent.Companion  Int 8com.reactnativepagerview.event.PageScrollEvent.Companion  RCTEventEmitter 8com.reactnativepagerview.event.PageScrollEvent.Companion  String 8com.reactnativepagerview.event.PageScrollEvent.Companion  WritableMap 8com.reactnativepagerview.event.PageScrollEvent.Companion  
getISInfinite 8com.reactnativepagerview.event.PageScrollEvent.Companion  getISNaN 8com.reactnativepagerview.event.PageScrollEvent.Companion  
getIsInfinite 8com.reactnativepagerview.event.PageScrollEvent.Companion  getIsNaN 8com.reactnativepagerview.event.PageScrollEvent.Companion  invoke 8com.reactnativepagerview.event.PageScrollEvent.Companion  
isInfinite 8com.reactnativepagerview.event.PageScrollEvent.Companion  isNaN 8com.reactnativepagerview.event.PageScrollEvent.Companion  	Arguments :com.reactnativepagerview.event.PageScrollStateChangedEvent  
EVENT_NAME :com.reactnativepagerview.event.PageScrollStateChangedEvent  Int :com.reactnativepagerview.event.PageScrollStateChangedEvent  RCTEventEmitter :com.reactnativepagerview.event.PageScrollStateChangedEvent  String :com.reactnativepagerview.event.PageScrollStateChangedEvent  WritableMap :com.reactnativepagerview.event.PageScrollStateChangedEvent  	eventName :com.reactnativepagerview.event.PageScrollStateChangedEvent  getEVENTName :com.reactnativepagerview.event.PageScrollStateChangedEvent  getEventName :com.reactnativepagerview.event.PageScrollStateChangedEvent  
getVIEWTag :com.reactnativepagerview.event.PageScrollStateChangedEvent  
getViewTag :com.reactnativepagerview.event.PageScrollStateChangedEvent  mPageScrollState :com.reactnativepagerview.event.PageScrollStateChangedEvent  serializeEventData :com.reactnativepagerview.event.PageScrollStateChangedEvent  setEventName :com.reactnativepagerview.event.PageScrollStateChangedEvent  
setViewTag :com.reactnativepagerview.event.PageScrollStateChangedEvent  viewTag :com.reactnativepagerview.event.PageScrollStateChangedEvent  	Arguments Dcom.reactnativepagerview.event.PageScrollStateChangedEvent.Companion  
EVENT_NAME Dcom.reactnativepagerview.event.PageScrollStateChangedEvent.Companion  Int Dcom.reactnativepagerview.event.PageScrollStateChangedEvent.Companion  RCTEventEmitter Dcom.reactnativepagerview.event.PageScrollStateChangedEvent.Companion  String Dcom.reactnativepagerview.event.PageScrollStateChangedEvent.Companion  WritableMap Dcom.reactnativepagerview.event.PageScrollStateChangedEvent.Companion  invoke Dcom.reactnativepagerview.event.PageScrollStateChangedEvent.Companion  	Arguments 0com.reactnativepagerview.event.PageSelectedEvent  
EVENT_NAME 0com.reactnativepagerview.event.PageSelectedEvent  Int 0com.reactnativepagerview.event.PageSelectedEvent  RCTEventEmitter 0com.reactnativepagerview.event.PageSelectedEvent  String 0com.reactnativepagerview.event.PageSelectedEvent  WritableMap 0com.reactnativepagerview.event.PageSelectedEvent  	eventName 0com.reactnativepagerview.event.PageSelectedEvent  getEVENTName 0com.reactnativepagerview.event.PageSelectedEvent  getEventName 0com.reactnativepagerview.event.PageSelectedEvent  
getVIEWTag 0com.reactnativepagerview.event.PageSelectedEvent  
getViewTag 0com.reactnativepagerview.event.PageSelectedEvent  	mPosition 0com.reactnativepagerview.event.PageSelectedEvent  serializeEventData 0com.reactnativepagerview.event.PageSelectedEvent  setEventName 0com.reactnativepagerview.event.PageSelectedEvent  
setViewTag 0com.reactnativepagerview.event.PageSelectedEvent  viewTag 0com.reactnativepagerview.event.PageSelectedEvent  	Arguments :com.reactnativepagerview.event.PageSelectedEvent.Companion  
EVENT_NAME :com.reactnativepagerview.event.PageSelectedEvent.Companion  Int :com.reactnativepagerview.event.PageSelectedEvent.Companion  RCTEventEmitter :com.reactnativepagerview.event.PageSelectedEvent.Companion  String :com.reactnativepagerview.event.PageSelectedEvent.Companion  WritableMap :com.reactnativepagerview.event.PageSelectedEvent.Companion  invoke :com.reactnativepagerview.event.PageSelectedEvent.Companion  	Arguments 	java.lang  	ArrayList 	java.lang  
Assertions 	java.lang  COMMAND_SET_PAGE 	java.lang  "COMMAND_SET_PAGE_WITHOUT_ANIMATION 	java.lang  COMMAND_SET_SCROLL_ENABLED 	java.lang  Class 	java.lang  ClassNotFoundException 	java.lang  
EVENT_NAME 	java.lang  Float 	java.lang  FrameLayout 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  MATCH_PARENT 	java.lang  
MapBuilder 	java.lang  MotionEvent 	java.lang  NestedScrollableHost 	java.lang  ORIENTATION_HORIZONTAL 	java.lang  PageScrollEvent 	java.lang  PageScrollStateChangedEvent 	java.lang  PageSelectedEvent 	java.lang  PagerViewViewManager 	java.lang  	PixelUtil 	java.lang  REACT_CLASS 	java.lang  String 	java.lang  UIManagerModule 	java.lang  View 	java.lang  ViewConfiguration 	java.lang  	ViewGroup 	java.lang  
ViewPager2 	java.lang  ViewPagerAdapter 	java.lang  ViewPagerViewHolder 	java.lang  	emptyList 	java.lang  eventDispatcher 	java.lang  format 	java.lang  
isInfinite 	java.lang  isNaN 	java.lang  java 	java.lang  	javaClass 	java.lang  listOf 	java.lang  
getSIMPLEName java.lang.Class  
getSimpleName java.lang.Class  
setSimpleName java.lang.Class  
simpleName java.lang.Class  
isInfinite java.lang.Float  isNaN java.lang.Float  <SAM-CONSTRUCTOR> java.lang.Runnable  	ArrayList 	java.util  ViewPagerViewHolder 	java.util  add java.util.AbstractCollection  clear java.util.AbstractCollection  get java.util.AbstractCollection  indexOf java.util.AbstractCollection  removeAt java.util.AbstractCollection  add java.util.AbstractList  clear java.util.AbstractList  get java.util.AbstractList  indexOf java.util.AbstractList  removeAt java.util.AbstractList  add java.util.ArrayList  clear java.util.ArrayList  get java.util.ArrayList  indexOf java.util.ArrayList  removeAt java.util.ArrayList  size java.util.ArrayList  	Arguments kotlin  	ArrayList kotlin  
Assertions kotlin  Boolean kotlin  COMMAND_SET_PAGE kotlin  "COMMAND_SET_PAGE_WITHOUT_ANIMATION kotlin  COMMAND_SET_SCROLL_ENABLED kotlin  ClassNotFoundException kotlin  Double kotlin  
EVENT_NAME kotlin  Float kotlin  FrameLayout kotlin  	Function0 kotlin  	Function2 kotlin  IllegalArgumentException kotlin  IllegalStateException kotlin  Int kotlin  MATCH_PARENT kotlin  
MapBuilder kotlin  MotionEvent kotlin  NestedScrollableHost kotlin  Nothing kotlin  ORIENTATION_HORIZONTAL kotlin  PageScrollEvent kotlin  PageScrollStateChangedEvent kotlin  PageSelectedEvent kotlin  PagerViewViewManager kotlin  	PixelUtil kotlin  REACT_CLASS kotlin  String kotlin  UIManagerModule kotlin  Unit kotlin  View kotlin  ViewConfiguration kotlin  	ViewGroup kotlin  
ViewPager2 kotlin  ViewPagerAdapter kotlin  ViewPagerViewHolder kotlin  	emptyList kotlin  eventDispatcher kotlin  format kotlin  
isInfinite kotlin  isNaN kotlin  java kotlin  	javaClass kotlin  listOf kotlin  getABSOLUTEValue kotlin.Float  getAbsoluteValue kotlin.Float  getSIGN kotlin.Float  getSign kotlin.Float  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  	Arguments kotlin.annotation  	ArrayList kotlin.annotation  
Assertions kotlin.annotation  COMMAND_SET_PAGE kotlin.annotation  "COMMAND_SET_PAGE_WITHOUT_ANIMATION kotlin.annotation  COMMAND_SET_SCROLL_ENABLED kotlin.annotation  ClassNotFoundException kotlin.annotation  
EVENT_NAME kotlin.annotation  FrameLayout kotlin.annotation  IllegalArgumentException kotlin.annotation  IllegalStateException kotlin.annotation  MATCH_PARENT kotlin.annotation  
MapBuilder kotlin.annotation  MotionEvent kotlin.annotation  NestedScrollableHost kotlin.annotation  ORIENTATION_HORIZONTAL kotlin.annotation  PageScrollEvent kotlin.annotation  PageScrollStateChangedEvent kotlin.annotation  PageSelectedEvent kotlin.annotation  PagerViewViewManager kotlin.annotation  	PixelUtil kotlin.annotation  REACT_CLASS kotlin.annotation  String kotlin.annotation  UIManagerModule kotlin.annotation  View kotlin.annotation  ViewConfiguration kotlin.annotation  	ViewGroup kotlin.annotation  
ViewPager2 kotlin.annotation  ViewPagerAdapter kotlin.annotation  ViewPagerViewHolder kotlin.annotation  	emptyList kotlin.annotation  eventDispatcher kotlin.annotation  format kotlin.annotation  
isInfinite kotlin.annotation  isNaN kotlin.annotation  java kotlin.annotation  	javaClass kotlin.annotation  listOf kotlin.annotation  	Arguments kotlin.collections  	ArrayList kotlin.collections  
Assertions kotlin.collections  COMMAND_SET_PAGE kotlin.collections  "COMMAND_SET_PAGE_WITHOUT_ANIMATION kotlin.collections  COMMAND_SET_SCROLL_ENABLED kotlin.collections  ClassNotFoundException kotlin.collections  
EVENT_NAME kotlin.collections  FrameLayout kotlin.collections  IllegalArgumentException kotlin.collections  IllegalStateException kotlin.collections  List kotlin.collections  MATCH_PARENT kotlin.collections  Map kotlin.collections  
MapBuilder kotlin.collections  MotionEvent kotlin.collections  
MutableMap kotlin.collections  NestedScrollableHost kotlin.collections  ORIENTATION_HORIZONTAL kotlin.collections  PageScrollEvent kotlin.collections  PageScrollStateChangedEvent kotlin.collections  PageSelectedEvent kotlin.collections  PagerViewViewManager kotlin.collections  	PixelUtil kotlin.collections  REACT_CLASS kotlin.collections  String kotlin.collections  UIManagerModule kotlin.collections  View kotlin.collections  ViewConfiguration kotlin.collections  	ViewGroup kotlin.collections  
ViewPager2 kotlin.collections  ViewPagerAdapter kotlin.collections  ViewPagerViewHolder kotlin.collections  	emptyList kotlin.collections  eventDispatcher kotlin.collections  format kotlin.collections  
isInfinite kotlin.collections  isNaN kotlin.collections  java kotlin.collections  	javaClass kotlin.collections  listOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  	Arguments kotlin.comparisons  	ArrayList kotlin.comparisons  
Assertions kotlin.comparisons  COMMAND_SET_PAGE kotlin.comparisons  "COMMAND_SET_PAGE_WITHOUT_ANIMATION kotlin.comparisons  COMMAND_SET_SCROLL_ENABLED kotlin.comparisons  ClassNotFoundException kotlin.comparisons  
EVENT_NAME kotlin.comparisons  FrameLayout kotlin.comparisons  IllegalArgumentException kotlin.comparisons  IllegalStateException kotlin.comparisons  MATCH_PARENT kotlin.comparisons  
MapBuilder kotlin.comparisons  MotionEvent kotlin.comparisons  NestedScrollableHost kotlin.comparisons  ORIENTATION_HORIZONTAL kotlin.comparisons  PageScrollEvent kotlin.comparisons  PageScrollStateChangedEvent kotlin.comparisons  PageSelectedEvent kotlin.comparisons  PagerViewViewManager kotlin.comparisons  	PixelUtil kotlin.comparisons  REACT_CLASS kotlin.comparisons  String kotlin.comparisons  UIManagerModule kotlin.comparisons  View kotlin.comparisons  ViewConfiguration kotlin.comparisons  	ViewGroup kotlin.comparisons  
ViewPager2 kotlin.comparisons  ViewPagerAdapter kotlin.comparisons  ViewPagerViewHolder kotlin.comparisons  	emptyList kotlin.comparisons  eventDispatcher kotlin.comparisons  format kotlin.comparisons  
isInfinite kotlin.comparisons  isNaN kotlin.comparisons  java kotlin.comparisons  	javaClass kotlin.comparisons  listOf kotlin.comparisons  	Arguments 	kotlin.io  	ArrayList 	kotlin.io  
Assertions 	kotlin.io  COMMAND_SET_PAGE 	kotlin.io  "COMMAND_SET_PAGE_WITHOUT_ANIMATION 	kotlin.io  COMMAND_SET_SCROLL_ENABLED 	kotlin.io  ClassNotFoundException 	kotlin.io  
EVENT_NAME 	kotlin.io  FrameLayout 	kotlin.io  IllegalArgumentException 	kotlin.io  IllegalStateException 	kotlin.io  MATCH_PARENT 	kotlin.io  
MapBuilder 	kotlin.io  MotionEvent 	kotlin.io  NestedScrollableHost 	kotlin.io  ORIENTATION_HORIZONTAL 	kotlin.io  PageScrollEvent 	kotlin.io  PageScrollStateChangedEvent 	kotlin.io  PageSelectedEvent 	kotlin.io  PagerViewViewManager 	kotlin.io  	PixelUtil 	kotlin.io  REACT_CLASS 	kotlin.io  String 	kotlin.io  UIManagerModule 	kotlin.io  View 	kotlin.io  ViewConfiguration 	kotlin.io  	ViewGroup 	kotlin.io  
ViewPager2 	kotlin.io  ViewPagerAdapter 	kotlin.io  ViewPagerViewHolder 	kotlin.io  	emptyList 	kotlin.io  eventDispatcher 	kotlin.io  format 	kotlin.io  
isInfinite 	kotlin.io  isNaN 	kotlin.io  java 	kotlin.io  	javaClass 	kotlin.io  listOf 	kotlin.io  	Arguments 
kotlin.jvm  	ArrayList 
kotlin.jvm  
Assertions 
kotlin.jvm  COMMAND_SET_PAGE 
kotlin.jvm  "COMMAND_SET_PAGE_WITHOUT_ANIMATION 
kotlin.jvm  COMMAND_SET_SCROLL_ENABLED 
kotlin.jvm  ClassNotFoundException 
kotlin.jvm  
EVENT_NAME 
kotlin.jvm  FrameLayout 
kotlin.jvm  IllegalArgumentException 
kotlin.jvm  IllegalStateException 
kotlin.jvm  MATCH_PARENT 
kotlin.jvm  
MapBuilder 
kotlin.jvm  MotionEvent 
kotlin.jvm  NestedScrollableHost 
kotlin.jvm  ORIENTATION_HORIZONTAL 
kotlin.jvm  PageScrollEvent 
kotlin.jvm  PageScrollStateChangedEvent 
kotlin.jvm  PageSelectedEvent 
kotlin.jvm  PagerViewViewManager 
kotlin.jvm  	PixelUtil 
kotlin.jvm  REACT_CLASS 
kotlin.jvm  String 
kotlin.jvm  UIManagerModule 
kotlin.jvm  View 
kotlin.jvm  ViewConfiguration 
kotlin.jvm  	ViewGroup 
kotlin.jvm  
ViewPager2 
kotlin.jvm  ViewPagerAdapter 
kotlin.jvm  ViewPagerViewHolder 
kotlin.jvm  	emptyList 
kotlin.jvm  eventDispatcher 
kotlin.jvm  format 
kotlin.jvm  
isInfinite 
kotlin.jvm  isNaN 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  listOf 
kotlin.jvm  
absoluteValue kotlin.math  getAbsoluteValue kotlin.math  getSign kotlin.math  sign kotlin.math  	Arguments 
kotlin.ranges  	ArrayList 
kotlin.ranges  
Assertions 
kotlin.ranges  COMMAND_SET_PAGE 
kotlin.ranges  "COMMAND_SET_PAGE_WITHOUT_ANIMATION 
kotlin.ranges  COMMAND_SET_SCROLL_ENABLED 
kotlin.ranges  ClassNotFoundException 
kotlin.ranges  
EVENT_NAME 
kotlin.ranges  FrameLayout 
kotlin.ranges  IllegalArgumentException 
kotlin.ranges  IllegalStateException 
kotlin.ranges  IntRange 
kotlin.ranges  MATCH_PARENT 
kotlin.ranges  
MapBuilder 
kotlin.ranges  MotionEvent 
kotlin.ranges  NestedScrollableHost 
kotlin.ranges  ORIENTATION_HORIZONTAL 
kotlin.ranges  PageScrollEvent 
kotlin.ranges  PageScrollStateChangedEvent 
kotlin.ranges  PageSelectedEvent 
kotlin.ranges  PagerViewViewManager 
kotlin.ranges  	PixelUtil 
kotlin.ranges  REACT_CLASS 
kotlin.ranges  String 
kotlin.ranges  UIManagerModule 
kotlin.ranges  View 
kotlin.ranges  ViewConfiguration 
kotlin.ranges  	ViewGroup 
kotlin.ranges  
ViewPager2 
kotlin.ranges  ViewPagerAdapter 
kotlin.ranges  ViewPagerViewHolder 
kotlin.ranges  	emptyList 
kotlin.ranges  eventDispatcher 
kotlin.ranges  format 
kotlin.ranges  
isInfinite 
kotlin.ranges  isNaN 
kotlin.ranges  java 
kotlin.ranges  	javaClass 
kotlin.ranges  listOf 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  	Arguments kotlin.sequences  	ArrayList kotlin.sequences  
Assertions kotlin.sequences  COMMAND_SET_PAGE kotlin.sequences  "COMMAND_SET_PAGE_WITHOUT_ANIMATION kotlin.sequences  COMMAND_SET_SCROLL_ENABLED kotlin.sequences  ClassNotFoundException kotlin.sequences  
EVENT_NAME kotlin.sequences  FrameLayout kotlin.sequences  IllegalArgumentException kotlin.sequences  IllegalStateException kotlin.sequences  MATCH_PARENT kotlin.sequences  
MapBuilder kotlin.sequences  MotionEvent kotlin.sequences  NestedScrollableHost kotlin.sequences  ORIENTATION_HORIZONTAL kotlin.sequences  PageScrollEvent kotlin.sequences  PageScrollStateChangedEvent kotlin.sequences  PageSelectedEvent kotlin.sequences  PagerViewViewManager kotlin.sequences  	PixelUtil kotlin.sequences  REACT_CLASS kotlin.sequences  String kotlin.sequences  UIManagerModule kotlin.sequences  View kotlin.sequences  ViewConfiguration kotlin.sequences  	ViewGroup kotlin.sequences  
ViewPager2 kotlin.sequences  ViewPagerAdapter kotlin.sequences  ViewPagerViewHolder kotlin.sequences  	emptyList kotlin.sequences  eventDispatcher kotlin.sequences  format kotlin.sequences  
isInfinite kotlin.sequences  isNaN kotlin.sequences  java kotlin.sequences  	javaClass kotlin.sequences  listOf kotlin.sequences  	Arguments kotlin.text  	ArrayList kotlin.text  
Assertions kotlin.text  COMMAND_SET_PAGE kotlin.text  "COMMAND_SET_PAGE_WITHOUT_ANIMATION kotlin.text  COMMAND_SET_SCROLL_ENABLED kotlin.text  ClassNotFoundException kotlin.text  
EVENT_NAME kotlin.text  FrameLayout kotlin.text  IllegalArgumentException kotlin.text  IllegalStateException kotlin.text  MATCH_PARENT kotlin.text  
MapBuilder kotlin.text  MotionEvent kotlin.text  NestedScrollableHost kotlin.text  ORIENTATION_HORIZONTAL kotlin.text  PageScrollEvent kotlin.text  PageScrollStateChangedEvent kotlin.text  PageSelectedEvent kotlin.text  PagerViewViewManager kotlin.text  	PixelUtil kotlin.text  REACT_CLASS kotlin.text  String kotlin.text  UIManagerModule kotlin.text  View kotlin.text  ViewConfiguration kotlin.text  	ViewGroup kotlin.text  
ViewPager2 kotlin.text  ViewPagerAdapter kotlin.text  ViewPagerViewHolder kotlin.text  	emptyList kotlin.text  eventDispatcher kotlin.text  format kotlin.text  
isInfinite kotlin.text  isNaN kotlin.text  java kotlin.text  	javaClass kotlin.text  listOf kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               