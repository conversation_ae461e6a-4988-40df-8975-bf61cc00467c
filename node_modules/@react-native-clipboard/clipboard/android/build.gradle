buildscript {
    repositories {
        google()
	mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.2.1'
    }
}

def getExtOrDefault(name) {
    return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['ReactNativeClipBoard_' + name]
}

def getExtOrIntegerDefault(name) {
    return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['ReactNativeClipBoard_' + name]).toInteger()
}

apply plugin: 'com.android.library'

def resolveReactNativeDirectory() {
    // monorepo workaround
    // react-native can be hoisted or in project's own node_modules
    def reactNativeFromProjectNodeModules = file("${rootProject.projectDir}/../node_modules/react-native")
    if (reactNativeFromProjectNodeModules.exists()) {
        return reactNativeFromProjectNodeModules
    }

    // This code is taken from react-native-reanimated, but with an extra "../" due to @react-native-cliploard/clipboard being in a subdirectory
    def reactNativeFromNodeModulesWithReactNativeClipboard = file("${projectDir}/../../../react-native")
    if (reactNativeFromNodeModulesWithReactNativeClipboard.exists()) {
        return reactNativeFromNodeModulesWithReactNativeClipboard
    }

    throw new Exception(
            "[react-native-clipboard] Unable to resolve react-native location in " +
                    "node_modules. You should add project extension property (in app/build.gradle) " +
                    "`REACT_NATIVE_NODE_MODULES_DIR` with path to react-native."
    )
}

def REACT_NATIVE_DIR = resolveReactNativeDirectory()

def reactProperties = new Properties()
file("$REACT_NATIVE_DIR/ReactAndroid/gradle.properties").withInputStream { reactProperties.load(it) }

def REACT_NATIVE_VERSION = reactProperties.getProperty("VERSION_NAME")
def REACT_NATIVE_MINOR_VERSION = REACT_NATIVE_VERSION.startsWith("0.0.0-") ? 1000 : REACT_NATIVE_VERSION.split("\\.")[1].toInteger()

def isNewArchitectureEnabled() {
    // To opt-in for the New Architecture, you can either:
    // - Set `newArchEnabled` to true inside the `gradle.properties` file
    // - Invoke gradle with `-newArchEnabled=true`
    // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

if (isNewArchitectureEnabled()) {
    apply plugin: "com.facebook.react"
}

android {
	def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
	if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
	  namespace "com.reactnativecommunity.clipboard"
	}
	
    compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')

    // Used to override the NDK path/version on internal CI or by allowing
    // users to customize the NDK path/version from their root project (e.g. for M1 support)
    if (rootProject.hasProperty("ndkPath")) {
        ndkPath rootProject.ext.ndkPath
    }
    if (rootProject.hasProperty("ndkVersion")) {
        ndkVersion rootProject.ext.ndkVersion
    }

    defaultConfig {
        minSdkVersion getExtOrIntegerDefault('minSdkVersion')
        targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
    }

    sourceSets.main {
        java {
            if (!isNewArchitectureEnabled()) {
                srcDirs += 'src/paper/java'
            }
        }
    }
}

repositories {
    google()
    mavenCentral()
}

dependencies {
    //noinspection GradleDynamicVersion
    if (isNewArchitectureEnabled() && REACT_NATIVE_MINOR_VERSION < 71) {
        implementation project(":ReactAndroid")
    } else {
        implementation 'com.facebook.react:react-native:+'
    }
}
