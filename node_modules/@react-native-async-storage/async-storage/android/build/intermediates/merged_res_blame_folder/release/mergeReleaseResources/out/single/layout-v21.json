[{"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout-v21/notification_action_tombstone.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-core-1.9.0-5:/layout-v21/notification_action_tombstone.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout-v21/notification_template_icon_group.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-core-1.9.0-5:/layout-v21/notification_template_icon_group.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout-v21/notification_action.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-core-1.9.0-5:/layout-v21/notification_action.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout-v21/notification_template_custom_big.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-core-1.9.0-5:/layout-v21/notification_template_custom_big.xml"}]