[{"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/interpolator/fast_out_slow_in.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/interpolator/fast_out_slow_in.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml"}]