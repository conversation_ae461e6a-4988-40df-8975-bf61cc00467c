[{"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/drawable-v21/abc_list_divider_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/drawable-v21/abc_list_divider_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/drawable-v21/abc_btn_colored_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/drawable-v21/abc_btn_colored_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/drawable-v21/notification_action_background.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-core-1.9.0-5:/drawable-v21/notification_action_background.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/drawable-v21/abc_action_bar_item_background_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/drawable-v21/abc_action_bar_item_background_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/drawable-v21/abc_dialog_material_background.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/drawable-v21/abc_dialog_material_background.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/drawable-v21/abc_edit_text_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/drawable-v21/abc_edit_text_material.xml"}]