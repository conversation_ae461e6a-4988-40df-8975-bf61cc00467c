[{"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_cascading_menu_item_layout.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_cascading_menu_item_layout.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/redbox_item_title.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-jetified-react-android-0.74.1-release-10:/layout/redbox_item_title.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_activity_chooser_view_list_item.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_activity_chooser_view_list_item.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_search_dropdown_item_icons_2line.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_search_dropdown_item_icons_2line.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_alert_dialog_button_bar_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_alert_dialog_button_bar_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_list_menu_item_checkbox.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_list_menu_item_checkbox.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_action_bar_title_item.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_action_bar_title_item.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_popup_menu_header_item_layout.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_popup_menu_header_item_layout.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_alert_dialog_title_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_alert_dialog_title_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_screen_toolbar.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_screen_toolbar.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/select_dialog_item_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/select_dialog_item_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_action_menu_item_layout.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_action_menu_item_layout.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_select_dialog_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_select_dialog_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/notification_template_part_chronometer.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-core-1.9.0-5:/layout/notification_template_part_chronometer.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_alert_dialog_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_alert_dialog_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_screen_simple_overlay_action_mode.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_screen_simple_overlay_action_mode.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/autofill_inline_suggestion.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-jetified-autofill-1.1.0-1:/layout/autofill_inline_suggestion.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/dev_loading_view.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-jetified-react-android-0.74.1-release-10:/layout/dev_loading_view.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_expanded_menu_layout.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_expanded_menu_layout.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/select_dialog_singlechoice_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/select_dialog_singlechoice_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_action_mode_bar.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_action_mode_bar.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_list_menu_item_layout.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_list_menu_item_layout.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_search_view.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_search_view.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_action_bar_up_container.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_action_bar_up_container.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/fps_view.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-jetified-react-android-0.74.1-release-10:/layout/fps_view.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/notification_template_part_time.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-core-1.9.0-5:/layout/notification_template_part_time.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/redbox_view.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-jetified-react-android-0.74.1-release-10:/layout/redbox_view.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/support_simple_spinner_dropdown_item.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/support_simple_spinner_dropdown_item.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_action_menu_layout.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_action_menu_layout.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/select_dialog_multichoice_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/select_dialog_multichoice_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_tooltip.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_tooltip.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_screen_simple.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_screen_simple.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/custom_dialog.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-core-1.9.0-5:/layout/custom_dialog.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_list_menu_item_icon.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_list_menu_item_icon.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_dialog_title_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_dialog_title_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_activity_chooser_view.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_activity_chooser_view.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_popup_menu_item_layout.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_popup_menu_item_layout.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_screen_content_include.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_screen_content_include.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_list_menu_item_radio.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_list_menu_item_radio.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/abc_action_mode_close_item_material.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-appcompat-1.6.1-7:/layout/abc_action_mode_close_item_material.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/layout/redbox_item_frame.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-jetified-react-android-0.74.1-release-10:/layout/redbox_item_frame.xml"}]