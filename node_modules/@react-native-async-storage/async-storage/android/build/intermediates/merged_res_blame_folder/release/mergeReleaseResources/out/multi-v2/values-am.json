{"logs": [{"outputFile": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-mergeReleaseResources-22:/values-am/values-am.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/44fb890b3476edbdbdd38f9f2ba920ee/transformed/core-1.9.0/res/values-am/values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2803", "endColumns": "100", "endOffsets": "2899"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/6bfb408f2404c1ef80c188894c79b500/transformed/appcompat-1.6.1/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}}]}, {"outputFile": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/values-am/values-am.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/44fb890b3476edbdbdd38f9f2ba920ee/transformed/core-1.9.0/res/values-am/values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2803", "endColumns": "100", "endOffsets": "2899"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/6bfb408f2404c1ef80c188894c79b500/transformed/appcompat-1.6.1/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}}]}]}