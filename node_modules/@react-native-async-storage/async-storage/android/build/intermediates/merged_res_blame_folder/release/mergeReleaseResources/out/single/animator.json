[{"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/animator/fragment_close_enter.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-fragment-1.3.6-0:/animator/fragment_close_enter.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/animator/fragment_open_exit.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-fragment-1.3.6-0:/animator/fragment_open_exit.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/animator/fragment_fade_enter.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-fragment-1.3.6-0:/animator/fragment_fade_enter.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/animator/fragment_close_exit.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-fragment-1.3.6-0:/animator/fragment_close_exit.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/animator/fragment_fade_exit.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-fragment-1.3.6-0:/animator/fragment_fade_exit.xml"}, {"merged": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-release-24:/animator/fragment_open_enter.xml", "source": "com.reactnativecommunity.asyncstorage.react-native-async-storage_async-storage-fragment-1.3.6-0:/animator/fragment_open_enter.xml"}]