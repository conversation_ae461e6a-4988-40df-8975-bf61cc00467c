!com/facebook/react/ReactExtensioncom/facebook/react/ReactPlugin&com/facebook/react/ReactPlugin$apply$1(com/facebook/react/ReactPlugin$apply$1$1*com/facebook/react/ReactPlugin$apply$1$2$1&com/facebook/react/ReactPlugin$apply$2Kcom/facebook/react/ReactPlugin$configureCodegen$generateCodegenSchemaTask$1Mcom/facebook/react/ReactPlugin$configureCodegen$generateCodegenSchemaTask$1$1Ncom/facebook/react/ReactPlugin$configureCodegen$generateCodegenArtifactsTask$1Pcom/facebook/react/ReactPlugin$configureCodegen$generateCodegenArtifactsTask$1$11com/facebook/react/ReactPlugin$configureCodegen$1)com/facebook/react/ReactRootProjectPlugin1com/facebook/react/ReactRootProjectPlugin$apply$1&com/facebook/react/TaskConfigurationKtGcom/facebook/react/TaskConfigurationKt$configureReactTasks$bundleTask$1<com/facebook/react/TaskConfigurationKt$configureReactTasks$1<com/facebook/react/TaskConfigurationKt$configureReactTasks$21com/facebook/react/internal/PrivateReactExtension+com/facebook/react/model/ModelCodegenConfig2com/facebook/react/model/ModelCodegenConfigAndroid)com/facebook/react/model/ModelPackageJson*com/facebook/react/tasks/BundleHermesCTask7com/facebook/react/tasks/BundleHermesCTask$runCommand$14com/facebook/react/tasks/BundleHermesCTask$sources$15com/facebook/react/tasks/GenerateCodegenArtifactsTask2com/facebook/react/tasks/GenerateCodegenSchemaTaskAcom/facebook/react/tasks/GenerateCodegenSchemaTask$jsInputFiles$15com/facebook/react/tasks/internal/BuildCodegenCLITask?com/facebook/react/tasks/internal/BuildCodegenCLITask$CompanionBcom/facebook/react/tasks/internal/BuildCodegenCLITask$inputFiles$1Ccom/facebook/react/tasks/internal/BuildCodegenCLITask$outputFiles$12com/facebook/react/tasks/internal/PrepareBoostTask?com/facebook/react/tasks/internal/PrepareBoostTask$taskAction$11com/facebook/react/tasks/internal/PrepareGlogTask>com/facebook/react/tasks/internal/PrepareGlogTask$taskAction$1@com/facebook/react/tasks/internal/PrepareGlogTask$taskAction$1$1>com/facebook/react/tasks/internal/PrepareGlogTask$taskAction$2@com/facebook/react/tasks/internal/PrepareGlogTask$taskAction$2$10com/facebook/react/tasks/internal/PrepareJSCTaskDcom/facebook/react/tasks/internal/PrepareJSCTask$taskAction$jscAAR$1Ecom/facebook/react/tasks/internal/PrepareJSCTask$taskAction$soFiles$1Icom/facebook/react/tasks/internal/PrepareJSCTask$taskAction$headerFiles$1=com/facebook/react/tasks/internal/PrepareJSCTask$taskAction$1?com/facebook/react/tasks/internal/PrepareJSCTask$taskAction$1$1:com/facebook/react/tasks/internal/PreparePrefabHeadersTaskKcom/facebook/react/tasks/internal/PreparePrefabHeadersTask$taskAction$1$1$1@com/facebook/react/tasks/internal/utils/PrefabPreprocessingEntry-com/facebook/react/utils/AgpConfiguratorUtilsVcom/facebook/react/utils/AgpConfiguratorUtils$configureBuildConfigFieldsForLibraries$1Xcom/facebook/react/utils/AgpConfiguratorUtils$configureBuildConfigFieldsForLibraries$1$1Zcom/facebook/react/utils/AgpConfiguratorUtils$configureBuildConfigFieldsForLibraries$1$1$1Ncom/facebook/react/utils/AgpConfiguratorUtils$configureNamespaceForLibraries$1Pcom/facebook/react/utils/AgpConfiguratorUtils$configureNamespaceForLibraries$1$1Rcom/facebook/react/utils/AgpConfiguratorUtils$configureNamespaceForLibraries$1$1$1Ycom/facebook/react/utils/AgpConfiguratorUtils$configureBuildConfigFieldsForApp$action$1$1Jcom/facebook/react/utils/AgpConfiguratorUtils$configureDevPorts$action$1$1/com/facebook/react/utils/AgpConfiguratorUtilsKt,com/facebook/react/utils/BackwardCompatUtils(com/facebook/react/utils/DependencyUtils@com/facebook/react/utils/DependencyUtils$configureRepositories$1Dcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$1Fcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$1$1Fcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$1$2@com/facebook/react/utils/DependencyUtils$configureDependencies$1Bcom/facebook/react/utils/DependencyUtils$configureDependencies$1$1Dcom/facebook/react/utils/DependencyUtils$configureDependencies$1$1$1;com/facebook/react/utils/DependencyUtils$mavenRepoFromUrl$1;com/facebook/react/utils/DependencyUtils$mavenRepoFromURI$1$com/facebook/react/utils/FileUtilsKt-com/facebook/react/utils/JdkConfiguratorUtilsGcom/facebook/react/utils/JdkConfiguratorUtils$configureJavaToolChains$1Icom/facebook/react/utils/JdkConfiguratorUtils$configureJavaToolChains$1$1Icom/facebook/react/utils/JdkConfiguratorUtils$configureJavaToolChains$1$2Rcom/facebook/react/utils/JdkConfiguratorUtils$configureJavaToolChains$1$action$1$1"com/facebook/react/utils/JsonUtils0com/facebook/react/utils/KotlinStdlibCompatUtils-com/facebook/react/utils/NdkConfiguratorUtilsGcom/facebook/react/utils/NdkConfiguratorUtils$configureReactNativeNdk$1Icom/facebook/react/utils/NdkConfiguratorUtils$configureReactNativeNdk$1$1com/facebook/react/utils/Os"com/facebook/react/utils/PathUtils=com/facebook/react/utils/PathUtils$projectPathToLibraryName$1%com/facebook/react/utils/ProjectUtils&com/facebook/react/utils/PropertyUtils$com/facebook/react/utils/TaskUtilsKt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               