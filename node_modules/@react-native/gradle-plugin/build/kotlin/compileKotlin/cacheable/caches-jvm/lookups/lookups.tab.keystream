  CommonExtension com.android.build.api.dsl  isEnable "com.android.build.api.dsl.AbiSplit  srcDir 3com.android.build.api.dsl.AndroidSourceDirectorySet  java *com.android.build.api.dsl.AndroidSourceSet  buildConfig 'com.android.build.api.dsl.BuildFeatures  prefab 'com.android.build.api.dsl.BuildFeatures  path com.android.build.api.dsl.Cmake  	arguments $com.android.build.api.dsl.CmakeFlags  
buildFeatures )com.android.build.api.dsl.CommonExtension  compileOptions )com.android.build.api.dsl.CommonExtension  
defaultConfig )com.android.build.api.dsl.CommonExtension  externalNativeBuild )com.android.build.api.dsl.CommonExtension  	namespace )com.android.build.api.dsl.CommonExtension  
sourceSets )com.android.build.api.dsl.CommonExtension  splits )com.android.build.api.dsl.CommonExtension  sourceCompatibility (com.android.build.api.dsl.CompileOptions  targetCompatibility (com.android.build.api.dsl.CompileOptions  buildConfigField 'com.android.build.api.dsl.DefaultConfig  externalNativeBuild 'com.android.build.api.dsl.DefaultConfig  ndk 'com.android.build.api.dsl.DefaultConfig  resValue 'com.android.build.api.dsl.DefaultConfig  cmake -com.android.build.api.dsl.ExternalNativeBuild  cmake 2com.android.build.api.dsl.ExternalNativeBuildFlags  
abiFilters com.android.build.api.dsl.Ndk  abi  com.android.build.api.dsl.Splits  AndroidComponentsExtension com.android.build.api.variant  Variant com.android.build.api.variant  VariantSelector com.android.build.api.variant  apply 8com.android.build.api.variant.AndroidComponentsExtension  configureReactTasks 8com.android.build.api.variant.AndroidComponentsExtension  finalizeDsl 8com.android.build.api.variant.AndroidComponentsExtension  getAPPLY 8com.android.build.api.variant.AndroidComponentsExtension  getApply 8com.android.build.api.variant.AndroidComponentsExtension  getCONFIGUREReactTasks 8com.android.build.api.variant.AndroidComponentsExtension  getConfigureReactTasks 8com.android.build.api.variant.AndroidComponentsExtension  
onVariants 8com.android.build.api.variant.AndroidComponentsExtension  selector 8com.android.build.api.variant.AndroidComponentsExtension  excludes .com.android.build.api.variant.JniLibsPackaging  
pickFirsts .com.android.build.api.variant.JniLibsPackaging  jniLibs 'com.android.build.api.variant.Packaging  addGeneratedSourceDirectory 7com.android.build.api.variant.SourceDirectories.Layered  assets %com.android.build.api.variant.Sources  res %com.android.build.api.variant.Sources  name %com.android.build.api.variant.Variant  	packaging %com.android.build.api.variant.Variant  sources %com.android.build.api.variant.Variant  all -com.android.build.api.variant.VariantSelector  LibraryExtension com.android.build.gradle  
sourceSets )com.android.build.gradle.LibraryExtension  srcFile .com.android.build.gradle.api.AndroidSourceFile  manifest -com.android.build.gradle.api.AndroidSourceSet  	dependsOn /com.android.build.gradle.internal.tasks.factory  AndroidComponentsExtension com.facebook.react  Boolean com.facebook.react  BundleHermesCTask com.facebook.react  File com.facebook.react  GenerateCodegenArtifactsTask com.facebook.react  GenerateCodegenSchemaTask com.facebook.react  	JsonUtils com.facebook.react  Jvm com.facebook.react  PrivateReactExtension com.facebook.react  ReactExtension com.facebook.react  ReactPlugin com.facebook.react  ReactRootProjectPlugin com.facebook.react  String com.facebook.react  Suppress com.facebook.react  System com.facebook.react  Task com.facebook.react  any com.facebook.react  apply com.facebook.react  capitalizeCompat com.facebook.react  &configureBackwardCompatibilityReactMap com.facebook.react   configureBuildConfigFieldsForApp com.facebook.react  &configureBuildConfigFieldsForLibraries com.facebook.react  configureDependencies com.facebook.react  configureDevPorts com.facebook.react  configureJavaToolChains com.facebook.react  !configureJsEnginePackagingOptions com.facebook.react  configureNamespaceForLibraries com.facebook.react   configureNewArchPackagingOptions com.facebook.react  configureReactNativeNdk com.facebook.react  configureReactTasks com.facebook.react  configureRepositories com.facebook.react  	dependsOn com.facebook.react  detectedCliFile com.facebook.react  detectedEntryFile com.facebook.react  	emptyList com.facebook.react  equals com.facebook.react  exitProcess com.facebook.react  findPackageJsonFile com.facebook.react  
isNotEmpty com.facebook.react  java com.facebook.react  let com.facebook.react  listOf com.facebook.react  needsCodegenFromPackageJson com.facebook.react  projectPathToLibraryName com.facebook.react  readVersionAndGroupStrings com.facebook.react  &shouldWarnIfNewArchFlagIsSetInPrealpha com.facebook.react  toIntOrNull com.facebook.react  
trimIndent com.facebook.react  Boolean !com.facebook.react.ReactExtension  DirectoryProperty !com.facebook.react.ReactExtension  Inject !com.facebook.react.ReactExtension  ListProperty !com.facebook.react.ReactExtension  Project !com.facebook.react.ReactExtension  Property !com.facebook.react.ReactExtension  RegularFileProperty !com.facebook.react.ReactExtension  String !com.facebook.react.ReactExtension  bundleAssetName !com.facebook.react.ReactExtension  
bundleCommand !com.facebook.react.ReactExtension  bundleConfig !com.facebook.react.ReactExtension  cliFile !com.facebook.react.ReactExtension  
codegenDir !com.facebook.react.ReactExtension  codegenJavaPackageName !com.facebook.react.ReactExtension  debuggableVariants !com.facebook.react.ReactExtension  	emptyList !com.facebook.react.ReactExtension  enableHermesOnlyInVariants !com.facebook.react.ReactExtension  enableSoCleanup !com.facebook.react.ReactExtension  	entryFile !com.facebook.react.ReactExtension  extraPackagerArgs !com.facebook.react.ReactExtension  getEMPTYList !com.facebook.react.ReactExtension  getEmptyList !com.facebook.react.ReactExtension  	getLISTOf !com.facebook.react.ReactExtension  	getListOf !com.facebook.react.ReactExtension  getPROJECTPathToLibraryName !com.facebook.react.ReactExtension  getProjectPathToLibraryName !com.facebook.react.ReactExtension  
hermesCommand !com.facebook.react.ReactExtension  hermesFlags !com.facebook.react.ReactExtension  java !com.facebook.react.ReactExtension  	jsRootDir !com.facebook.react.ReactExtension  libraryName !com.facebook.react.ReactExtension  listOf !com.facebook.react.ReactExtension  nodeExecutableAndArgs !com.facebook.react.ReactExtension  objects !com.facebook.react.ReactExtension  projectPathToLibraryName !com.facebook.react.ReactExtension  reactNativeDir !com.facebook.react.ReactExtension  root !com.facebook.react.ReactExtension  AndroidComponentsExtension com.facebook.react.ReactPlugin  Boolean com.facebook.react.ReactPlugin  	Directory com.facebook.react.ReactPlugin  File com.facebook.react.ReactPlugin  GenerateCodegenArtifactsTask com.facebook.react.ReactPlugin  GenerateCodegenSchemaTask com.facebook.react.ReactPlugin  	JsonUtils com.facebook.react.ReactPlugin  Jvm com.facebook.react.ReactPlugin  PrivateReactExtension com.facebook.react.ReactPlugin  Project com.facebook.react.ReactPlugin  Provider com.facebook.react.ReactPlugin  ReactExtension com.facebook.react.ReactPlugin  Suppress com.facebook.react.ReactPlugin  Task com.facebook.react.ReactPlugin  apply com.facebook.react.ReactPlugin  checkIfNewArchFlagIsSet com.facebook.react.ReactPlugin  checkJvmVersion com.facebook.react.ReactPlugin  &configureBackwardCompatibilityReactMap com.facebook.react.ReactPlugin   configureBuildConfigFieldsForApp com.facebook.react.ReactPlugin  &configureBuildConfigFieldsForLibraries com.facebook.react.ReactPlugin  configureCodegen com.facebook.react.ReactPlugin  configureDependencies com.facebook.react.ReactPlugin  configureDevPorts com.facebook.react.ReactPlugin  configureJavaToolChains com.facebook.react.ReactPlugin  configureNamespaceForLibraries com.facebook.react.ReactPlugin  configureReactNativeNdk com.facebook.react.ReactPlugin  configureReactTasks com.facebook.react.ReactPlugin  configureRepositories com.facebook.react.ReactPlugin  	dependsOn com.facebook.react.ReactPlugin  exitProcess com.facebook.react.ReactPlugin  findPackageJsonFile com.facebook.react.ReactPlugin  getAPPLY com.facebook.react.ReactPlugin  getApply com.facebook.react.ReactPlugin  )getCONFIGUREBackwardCompatibilityReactMap com.facebook.react.ReactPlugin  #getCONFIGUREBuildConfigFieldsForApp com.facebook.react.ReactPlugin  )getCONFIGUREBuildConfigFieldsForLibraries com.facebook.react.ReactPlugin  getCONFIGUREDependencies com.facebook.react.ReactPlugin  getCONFIGUREDevPorts com.facebook.react.ReactPlugin  getCONFIGUREJavaToolChains com.facebook.react.ReactPlugin  !getCONFIGURENamespaceForLibraries com.facebook.react.ReactPlugin  getCONFIGUREReactNativeNdk com.facebook.react.ReactPlugin  getCONFIGUREReactTasks com.facebook.react.ReactPlugin  getCONFIGURERepositories com.facebook.react.ReactPlugin  )getConfigureBackwardCompatibilityReactMap com.facebook.react.ReactPlugin  #getConfigureBuildConfigFieldsForApp com.facebook.react.ReactPlugin  )getConfigureBuildConfigFieldsForLibraries com.facebook.react.ReactPlugin  getConfigureDependencies com.facebook.react.ReactPlugin  getConfigureDevPorts com.facebook.react.ReactPlugin  getConfigureJavaToolChains com.facebook.react.ReactPlugin  !getConfigureNamespaceForLibraries com.facebook.react.ReactPlugin  getConfigureReactNativeNdk com.facebook.react.ReactPlugin  getConfigureReactTasks com.facebook.react.ReactPlugin  getConfigureRepositories com.facebook.react.ReactPlugin  getDEPENDSOn com.facebook.react.ReactPlugin  getDependsOn com.facebook.react.ReactPlugin  getEXITProcess com.facebook.react.ReactPlugin  getExitProcess com.facebook.react.ReactPlugin  getFINDPackageJsonFile com.facebook.react.ReactPlugin  getFindPackageJsonFile com.facebook.react.ReactPlugin  getLET com.facebook.react.ReactPlugin  getLet com.facebook.react.ReactPlugin  getNEEDSCodegenFromPackageJson com.facebook.react.ReactPlugin  getNeedsCodegenFromPackageJson com.facebook.react.ReactPlugin  getREADVersionAndGroupStrings com.facebook.react.ReactPlugin  getReadVersionAndGroupStrings com.facebook.react.ReactPlugin  )getSHOULDWarnIfNewArchFlagIsSetInPrealpha com.facebook.react.ReactPlugin  )getShouldWarnIfNewArchFlagIsSetInPrealpha com.facebook.react.ReactPlugin  getTOIntOrNull com.facebook.react.ReactPlugin  
getTRIMIndent com.facebook.react.ReactPlugin  getToIntOrNull com.facebook.react.ReactPlugin  
getTrimIndent com.facebook.react.ReactPlugin  java com.facebook.react.ReactPlugin  let com.facebook.react.ReactPlugin  needsCodegenFromPackageJson com.facebook.react.ReactPlugin  readVersionAndGroupStrings com.facebook.react.ReactPlugin  &shouldWarnIfNewArchFlagIsSetInPrealpha com.facebook.react.ReactPlugin  toIntOrNull com.facebook.react.ReactPlugin  
trimIndent com.facebook.react.ReactPlugin  Project )com.facebook.react.ReactRootProjectPlugin  PrivateReactExtension com.facebook.react.internal  String com.facebook.react.internal  java com.facebook.react.internal  listOf com.facebook.react.internal  DirectoryProperty 1com.facebook.react.internal.PrivateReactExtension  Inject 1com.facebook.react.internal.PrivateReactExtension  ListProperty 1com.facebook.react.internal.PrivateReactExtension  Project 1com.facebook.react.internal.PrivateReactExtension  String 1com.facebook.react.internal.PrivateReactExtension  
codegenDir 1com.facebook.react.internal.PrivateReactExtension  	getLISTOf 1com.facebook.react.internal.PrivateReactExtension  	getListOf 1com.facebook.react.internal.PrivateReactExtension  java 1com.facebook.react.internal.PrivateReactExtension  listOf 1com.facebook.react.internal.PrivateReactExtension  nodeExecutableAndArgs 1com.facebook.react.internal.PrivateReactExtension  objects 1com.facebook.react.internal.PrivateReactExtension  reactNativeDir 1com.facebook.react.internal.PrivateReactExtension  root 1com.facebook.react.internal.PrivateReactExtension  Boolean com.facebook.react.model  ModelCodegenConfig com.facebook.react.model  ModelCodegenConfigAndroid com.facebook.react.model  ModelPackageJson com.facebook.react.model  String com.facebook.react.model  Boolean +com.facebook.react.model.ModelCodegenConfig  ModelCodegenConfigAndroid +com.facebook.react.model.ModelCodegenConfig  String +com.facebook.react.model.ModelCodegenConfig  android +com.facebook.react.model.ModelCodegenConfig  equals +com.facebook.react.model.ModelCodegenConfig  includesGeneratedCode +com.facebook.react.model.ModelCodegenConfig  	jsSrcsDir +com.facebook.react.model.ModelCodegenConfig  name +com.facebook.react.model.ModelCodegenConfig  String 2com.facebook.react.model.ModelCodegenConfigAndroid  javaPackageName 2com.facebook.react.model.ModelCodegenConfigAndroid  ModelCodegenConfig )com.facebook.react.model.ModelPackageJson  String )com.facebook.react.model.ModelPackageJson  
codegenConfig )com.facebook.react.model.ModelPackageJson  equals )com.facebook.react.model.ModelPackageJson  version )com.facebook.react.model.ModelPackageJson  Any com.facebook.react.tasks  Boolean com.facebook.react.tasks  BundleHermesCTask com.facebook.react.tasks  Exec com.facebook.react.tasks  File com.facebook.react.tasks  GenerateCodegenArtifactsTask com.facebook.react.tasks  GenerateCodegenSchemaTask com.facebook.react.tasks  Input com.facebook.react.tasks  	InputFile com.facebook.react.tasks  
InputFiles com.facebook.react.tasks  Internal com.facebook.react.tasks  	JsonUtils com.facebook.react.tasks  List com.facebook.react.tasks  Optional com.facebook.react.tasks  OutputDirectory com.facebook.react.tasks  
OutputFile com.facebook.react.tasks  Pair com.facebook.react.tasks  String com.facebook.react.tasks  
TaskAction com.facebook.react.tasks  apply com.facebook.react.tasks  
bundleCommand com.facebook.react.tasks  bundleConfig com.facebook.react.tasks  cliFile com.facebook.react.tasks  cliPath com.facebook.react.tasks  deleteRecursively com.facebook.react.tasks  detectOSAwareHermesCommand com.facebook.react.tasks  
devEnabled com.facebook.react.tasks  	entryFile com.facebook.react.tasks  extraPackagerArgs com.facebook.react.tasks  
minifyEnabled com.facebook.react.tasks  moveTo com.facebook.react.tasks  
mutableListOf com.facebook.react.tasks  nodeExecutableAndArgs com.facebook.react.tasks  resourcesDir com.facebook.react.tasks  to com.facebook.react.tasks  toTypedArray com.facebook.react.tasks  windowsAwareCommandLine com.facebook.react.tasks  Any *com.facebook.react.tasks.BundleHermesCTask  Boolean *com.facebook.react.tasks.BundleHermesCTask  ConfigurableFileTree *com.facebook.react.tasks.BundleHermesCTask  DirectoryProperty *com.facebook.react.tasks.BundleHermesCTask  File *com.facebook.react.tasks.BundleHermesCTask  Input *com.facebook.react.tasks.BundleHermesCTask  	InputFile *com.facebook.react.tasks.BundleHermesCTask  
InputFiles *com.facebook.react.tasks.BundleHermesCTask  Internal *com.facebook.react.tasks.BundleHermesCTask  List *com.facebook.react.tasks.BundleHermesCTask  ListProperty *com.facebook.react.tasks.BundleHermesCTask  Optional *com.facebook.react.tasks.BundleHermesCTask  OutputDirectory *com.facebook.react.tasks.BundleHermesCTask  Property *com.facebook.react.tasks.BundleHermesCTask  RegularFileProperty *com.facebook.react.tasks.BundleHermesCTask  String *com.facebook.react.tasks.BundleHermesCTask  
TaskAction *com.facebook.react.tasks.BundleHermesCTask  apply *com.facebook.react.tasks.BundleHermesCTask  bundleAssetName *com.facebook.react.tasks.BundleHermesCTask  
bundleCommand *com.facebook.react.tasks.BundleHermesCTask  bundleConfig *com.facebook.react.tasks.BundleHermesCTask  cliFile *com.facebook.react.tasks.BundleHermesCTask  cliPath *com.facebook.react.tasks.BundleHermesCTask  detectOSAwareHermesCommand *com.facebook.react.tasks.BundleHermesCTask  
devEnabled *com.facebook.react.tasks.BundleHermesCTask  	entryFile *com.facebook.react.tasks.BundleHermesCTask  extraPackagerArgs *com.facebook.react.tasks.BundleHermesCTask  getAPPLY *com.facebook.react.tasks.BundleHermesCTask  getApply *com.facebook.react.tasks.BundleHermesCTask  getBundleCommand *com.facebook.react.tasks.BundleHermesCTask  
getCLIPath *com.facebook.react.tasks.BundleHermesCTask  
getCliPath *com.facebook.react.tasks.BundleHermesCTask  getComposeSourceMapsCommand *com.facebook.react.tasks.BundleHermesCTask  getDetectOSAwareHermesCommand *com.facebook.react.tasks.BundleHermesCTask  getGROUP *com.facebook.react.tasks.BundleHermesCTask  getGroup *com.facebook.react.tasks.BundleHermesCTask  getHermescCommand *com.facebook.react.tasks.BundleHermesCTask  	getMOVETo *com.facebook.react.tasks.BundleHermesCTask  getMUTABLEListOf *com.facebook.react.tasks.BundleHermesCTask  	getMoveTo *com.facebook.react.tasks.BundleHermesCTask  getMutableListOf *com.facebook.react.tasks.BundleHermesCTask  
getPROJECT *com.facebook.react.tasks.BundleHermesCTask  
getProject *com.facebook.react.tasks.BundleHermesCTask  getTOTypedArray *com.facebook.react.tasks.BundleHermesCTask  getToTypedArray *com.facebook.react.tasks.BundleHermesCTask  getWINDOWSAwareCommandLine *com.facebook.react.tasks.BundleHermesCTask  getWindowsAwareCommandLine *com.facebook.react.tasks.BundleHermesCTask  group *com.facebook.react.tasks.BundleHermesCTask  
hermesCommand *com.facebook.react.tasks.BundleHermesCTask  
hermesEnabled *com.facebook.react.tasks.BundleHermesCTask  hermesFlags *com.facebook.react.tasks.BundleHermesCTask  jsBundleDir *com.facebook.react.tasks.BundleHermesCTask  jsIntermediateSourceMapsDir *com.facebook.react.tasks.BundleHermesCTask  jsSourceMapsDir *com.facebook.react.tasks.BundleHermesCTask  
minifyEnabled *com.facebook.react.tasks.BundleHermesCTask  moveTo *com.facebook.react.tasks.BundleHermesCTask  
mutableListOf *com.facebook.react.tasks.BundleHermesCTask  nodeExecutableAndArgs *com.facebook.react.tasks.BundleHermesCTask  project *com.facebook.react.tasks.BundleHermesCTask  reactNativeDir *com.facebook.react.tasks.BundleHermesCTask  resolveCompilerSourceMap *com.facebook.react.tasks.BundleHermesCTask  resolveOutputSourceMap *com.facebook.react.tasks.BundleHermesCTask  resolvePackagerSourceMapFile *com.facebook.react.tasks.BundleHermesCTask  resourcesDir *com.facebook.react.tasks.BundleHermesCTask  root *com.facebook.react.tasks.BundleHermesCTask  
runCommand *com.facebook.react.tasks.BundleHermesCTask  setGroup *com.facebook.react.tasks.BundleHermesCTask  
setProject *com.facebook.react.tasks.BundleHermesCTask  toTypedArray *com.facebook.react.tasks.BundleHermesCTask  windowsAwareCommandLine *com.facebook.react.tasks.BundleHermesCTask  	Directory 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  DirectoryProperty 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  Input 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  	InputFile 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  Internal 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  	JsonUtils 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  ListProperty 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  OutputDirectory 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  Pair 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  Property 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  Provider 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  RegularFile 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  RegularFileProperty 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  String 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  cliPath 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  codegenJavaPackageName 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  commandLine 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  	dependsOn 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  generatedSchemaFile 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  generatedSrcDir 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  
getCLIPath 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  
getCliPath 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  
getPROJECT 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  
getProject 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  getTO 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  getTOTypedArray 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  getTo 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  getToTypedArray 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  getWINDOWSAwareCommandLine 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  getWindowsAwareCommandLine 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  libraryName 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  nodeExecutableAndArgs 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  onlyIf 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  packageJsonFile 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  project 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  reactNativeDir 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  resolveTaskParameters 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  
setProject 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  setupCommandLine 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  to 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  toTypedArray 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  windowsAwareCommandLine 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  DirectoryProperty 2com.facebook.react.tasks.GenerateCodegenSchemaTask  Input 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
InputFiles 2com.facebook.react.tasks.GenerateCodegenSchemaTask  Internal 2com.facebook.react.tasks.GenerateCodegenSchemaTask  ListProperty 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
OutputFile 2com.facebook.react.tasks.GenerateCodegenSchemaTask  Provider 2com.facebook.react.tasks.GenerateCodegenSchemaTask  RegularFile 2com.facebook.react.tasks.GenerateCodegenSchemaTask  String 2com.facebook.react.tasks.GenerateCodegenSchemaTask  apply 2com.facebook.react.tasks.GenerateCodegenSchemaTask  cliPath 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
codegenDir 2com.facebook.react.tasks.GenerateCodegenSchemaTask  commandLine 2com.facebook.react.tasks.GenerateCodegenSchemaTask  deleteRecursively 2com.facebook.react.tasks.GenerateCodegenSchemaTask  generatedSchemaFile 2com.facebook.react.tasks.GenerateCodegenSchemaTask  generatedSrcDir 2com.facebook.react.tasks.GenerateCodegenSchemaTask  getAPPLY 2com.facebook.react.tasks.GenerateCodegenSchemaTask  getApply 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
getCLIPath 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
getCliPath 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
getPROJECT 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
getProject 2com.facebook.react.tasks.GenerateCodegenSchemaTask  getTOTypedArray 2com.facebook.react.tasks.GenerateCodegenSchemaTask  getToTypedArray 2com.facebook.react.tasks.GenerateCodegenSchemaTask  getWINDOWSAwareCommandLine 2com.facebook.react.tasks.GenerateCodegenSchemaTask  getWindowsAwareCommandLine 2com.facebook.react.tasks.GenerateCodegenSchemaTask  	jsRootDir 2com.facebook.react.tasks.GenerateCodegenSchemaTask  nodeExecutableAndArgs 2com.facebook.react.tasks.GenerateCodegenSchemaTask  onlyIf 2com.facebook.react.tasks.GenerateCodegenSchemaTask  project 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
setProject 2com.facebook.react.tasks.GenerateCodegenSchemaTask  setupCommandLine 2com.facebook.react.tasks.GenerateCodegenSchemaTask  toTypedArray 2com.facebook.react.tasks.GenerateCodegenSchemaTask  windowsAwareCommandLine 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
wipeOutputDir 2com.facebook.react.tasks.GenerateCodegenSchemaTask  BUILD_SCRIPT_PATH !com.facebook.react.tasks.internal  BuildCodegenCLITask !com.facebook.react.tasks.internal  DuplicatesStrategy !com.facebook.react.tasks.internal  Exec !com.facebook.react.tasks.internal  File !com.facebook.react.tasks.internal  Input !com.facebook.react.tasks.internal  
InputFiles !com.facebook.react.tasks.internal  Internal !com.facebook.react.tasks.internal  OutputDirectory !com.facebook.react.tasks.internal  OutputFiles !com.facebook.react.tasks.internal  PrepareBoostTask !com.facebook.react.tasks.internal  PrepareGlogTask !com.facebook.react.tasks.internal  PrepareJSCTask !com.facebook.react.tasks.internal  PreparePrefabHeadersTask !com.facebook.react.tasks.internal  
ReplaceTokens !com.facebook.react.tasks.internal  String !com.facebook.react.tasks.internal  
TaskAction !com.facebook.react.tasks.internal  apply !com.facebook.react.tasks.internal  boostVersion !com.facebook.react.tasks.internal  error !com.facebook.react.tasks.internal  forEach !com.facebook.react.tasks.internal  java !com.facebook.react.tasks.internal  mapOf !com.facebook.react.tasks.internal  removeSuffix !com.facebook.react.tasks.internal  to !com.facebook.react.tasks.internal  unixifyPath !com.facebook.react.tasks.internal  windowsAwareBashCommandLine !com.facebook.react.tasks.internal  BUILD_SCRIPT_PATH 5com.facebook.react.tasks.internal.BuildCodegenCLITask  DirectoryProperty 5com.facebook.react.tasks.internal.BuildCodegenCLITask  FileTree 5com.facebook.react.tasks.internal.BuildCodegenCLITask  
InputFiles 5com.facebook.react.tasks.internal.BuildCodegenCLITask  Internal 5com.facebook.react.tasks.internal.BuildCodegenCLITask  OutputFiles 5com.facebook.react.tasks.internal.BuildCodegenCLITask  Property 5com.facebook.react.tasks.internal.BuildCodegenCLITask  String 5com.facebook.react.tasks.internal.BuildCodegenCLITask  bashWindowsHome 5com.facebook.react.tasks.internal.BuildCodegenCLITask  
codegenDir 5com.facebook.react.tasks.internal.BuildCodegenCLITask  commandLine 5com.facebook.react.tasks.internal.BuildCodegenCLITask  
getPROJECT 5com.facebook.react.tasks.internal.BuildCodegenCLITask  
getProject 5com.facebook.react.tasks.internal.BuildCodegenCLITask  getUNIXIFYPath 5com.facebook.react.tasks.internal.BuildCodegenCLITask  getUnixifyPath 5com.facebook.react.tasks.internal.BuildCodegenCLITask  getWINDOWSAwareBashCommandLine 5com.facebook.react.tasks.internal.BuildCodegenCLITask  getWindowsAwareBashCommandLine 5com.facebook.react.tasks.internal.BuildCodegenCLITask  project 5com.facebook.react.tasks.internal.BuildCodegenCLITask  
setProject 5com.facebook.react.tasks.internal.BuildCodegenCLITask  unixifyPath 5com.facebook.react.tasks.internal.BuildCodegenCLITask  windowsAwareBashCommandLine 5com.facebook.react.tasks.internal.BuildCodegenCLITask  BUILD_SCRIPT_PATH ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  DirectoryProperty ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  FileTree ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  
InputFiles ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  Internal ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  OutputFiles ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  Property ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  String ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  getUNIXIFYPath ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  getUnixifyPath ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  getWINDOWSAwareBashCommandLine ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  getWindowsAwareBashCommandLine ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  unixifyPath ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  windowsAwareBashCommandLine ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  ConfigurableFileCollection 2com.facebook.react.tasks.internal.PrepareBoostTask  DirectoryProperty 2com.facebook.react.tasks.internal.PrepareBoostTask  File 2com.facebook.react.tasks.internal.PrepareBoostTask  Input 2com.facebook.react.tasks.internal.PrepareBoostTask  
InputFiles 2com.facebook.react.tasks.internal.PrepareBoostTask  OutputDirectory 2com.facebook.react.tasks.internal.PrepareBoostTask  Property 2com.facebook.react.tasks.internal.PrepareBoostTask  String 2com.facebook.react.tasks.internal.PrepareBoostTask  
TaskAction 2com.facebook.react.tasks.internal.PrepareBoostTask  apply 2com.facebook.react.tasks.internal.PrepareBoostTask  	boostPath 2com.facebook.react.tasks.internal.PrepareBoostTask  boostVersion 2com.facebook.react.tasks.internal.PrepareBoostTask  getAPPLY 2com.facebook.react.tasks.internal.PrepareBoostTask  getApply 2com.facebook.react.tasks.internal.PrepareBoostTask  
getPROJECT 2com.facebook.react.tasks.internal.PrepareBoostTask  
getProject 2com.facebook.react.tasks.internal.PrepareBoostTask  	outputDir 2com.facebook.react.tasks.internal.PrepareBoostTask  project 2com.facebook.react.tasks.internal.PrepareBoostTask  
setProject 2com.facebook.react.tasks.internal.PrepareBoostTask  ConfigurableFileCollection 1com.facebook.react.tasks.internal.PrepareGlogTask  DirectoryProperty 1com.facebook.react.tasks.internal.PrepareGlogTask  DuplicatesStrategy 1com.facebook.react.tasks.internal.PrepareGlogTask  File 1com.facebook.react.tasks.internal.PrepareGlogTask  Input 1com.facebook.react.tasks.internal.PrepareGlogTask  
InputFiles 1com.facebook.react.tasks.internal.PrepareGlogTask  OutputDirectory 1com.facebook.react.tasks.internal.PrepareGlogTask  Property 1com.facebook.react.tasks.internal.PrepareGlogTask  
ReplaceTokens 1com.facebook.react.tasks.internal.PrepareGlogTask  String 1com.facebook.react.tasks.internal.PrepareGlogTask  
TaskAction 1com.facebook.react.tasks.internal.PrepareGlogTask  apply 1com.facebook.react.tasks.internal.PrepareGlogTask  getAPPLY 1com.facebook.react.tasks.internal.PrepareGlogTask  getApply 1com.facebook.react.tasks.internal.PrepareGlogTask  getMAPOf 1com.facebook.react.tasks.internal.PrepareGlogTask  getMapOf 1com.facebook.react.tasks.internal.PrepareGlogTask  
getPROJECT 1com.facebook.react.tasks.internal.PrepareGlogTask  
getProject 1com.facebook.react.tasks.internal.PrepareGlogTask  getREMOVESuffix 1com.facebook.react.tasks.internal.PrepareGlogTask  getRemoveSuffix 1com.facebook.react.tasks.internal.PrepareGlogTask  getTO 1com.facebook.react.tasks.internal.PrepareGlogTask  getTo 1com.facebook.react.tasks.internal.PrepareGlogTask  glogPath 1com.facebook.react.tasks.internal.PrepareGlogTask  glogVersion 1com.facebook.react.tasks.internal.PrepareGlogTask  java 1com.facebook.react.tasks.internal.PrepareGlogTask  mapOf 1com.facebook.react.tasks.internal.PrepareGlogTask  	outputDir 1com.facebook.react.tasks.internal.PrepareGlogTask  project 1com.facebook.react.tasks.internal.PrepareGlogTask  removeSuffix 1com.facebook.react.tasks.internal.PrepareGlogTask  
setProject 1com.facebook.react.tasks.internal.PrepareGlogTask  to 1com.facebook.react.tasks.internal.PrepareGlogTask  DirectoryProperty 0com.facebook.react.tasks.internal.PrepareJSCTask  File 0com.facebook.react.tasks.internal.PrepareJSCTask  Input 0com.facebook.react.tasks.internal.PrepareJSCTask  OutputDirectory 0com.facebook.react.tasks.internal.PrepareJSCTask  Property 0com.facebook.react.tasks.internal.PrepareJSCTask  String 0com.facebook.react.tasks.internal.PrepareJSCTask  
TaskAction 0com.facebook.react.tasks.internal.PrepareJSCTask  error 0com.facebook.react.tasks.internal.PrepareJSCTask  getERROR 0com.facebook.react.tasks.internal.PrepareJSCTask  getError 0com.facebook.react.tasks.internal.PrepareJSCTask  
getPROJECT 0com.facebook.react.tasks.internal.PrepareJSCTask  
getProject 0com.facebook.react.tasks.internal.PrepareJSCTask  jscPackagePath 0com.facebook.react.tasks.internal.PrepareJSCTask  	outputDir 0com.facebook.react.tasks.internal.PrepareJSCTask  project 0com.facebook.react.tasks.internal.PrepareJSCTask  
setProject 0com.facebook.react.tasks.internal.PrepareJSCTask  DirectoryProperty :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  File :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  FileSystemOperations :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  Inject :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  Input :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  ListProperty :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  OutputDirectory :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  PrefabPreprocessingEntry :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  RegularFile :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  
TaskAction :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  fs :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  input :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  	outputDir :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  List 'com.facebook.react.tasks.internal.utils  Pair 'com.facebook.react.tasks.internal.utils  PrefabPreprocessingEntry 'com.facebook.react.tasks.internal.utils  String 'com.facebook.react.tasks.internal.utils  listOf 'com.facebook.react.tasks.internal.utils  List @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  Pair @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  String @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  
component1 @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  
component2 @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  	getLISTOf @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  	getListOf @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  listOf @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  Action com.facebook.react.utils  AgpConfiguratorUtils com.facebook.react.utils  AndroidComponentsExtension com.facebook.react.utils  Any com.facebook.react.utils  BackwardCompatUtils com.facebook.react.utils  Boolean com.facebook.react.utils  	Character com.facebook.react.utils  DEFAULT_DEV_SERVER_PORT com.facebook.react.utils  !DEFAULT_INTERNAL_PUBLISHING_GROUP com.facebook.react.utils  DependencyUtils com.facebook.react.utils  DocumentBuilderFactory com.facebook.react.utils  	Exception com.facebook.react.utils  File com.facebook.react.utils  Gson com.facebook.react.utils  HERMESC_BUILT_FROM_SOURCE_DIR com.facebook.react.utils  HERMESC_IN_REACT_NATIVE_DIR com.facebook.react.utils  HERMES_ENABLED com.facebook.react.utils  HERMES_FALLBACK com.facebook.react.utils  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT com.facebook.react.utils  INTERNAL_PUBLISHING_GROUP com.facebook.react.utils  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO com.facebook.react.utils  INTERNAL_USE_HERMES_NIGHTLY com.facebook.react.utils  INTERNAL_VERSION_NAME com.facebook.react.utils  JavaVersion com.facebook.react.utils  JdkConfiguratorUtils com.facebook.react.utils  	JsonUtils com.facebook.react.utils  JvmName com.facebook.react.utils  KotlinStdlibCompatUtils com.facebook.react.utils  KotlinTopLevelExtension com.facebook.react.utils  LibraryExtension com.facebook.react.utils  List com.facebook.react.utils  Locale com.facebook.react.utils  Map com.facebook.react.utils  ModelPackageJson com.facebook.react.utils  NEW_ARCH_ENABLED com.facebook.react.utils  NdkConfiguratorUtils com.facebook.react.utils  Os com.facebook.react.utils  Pair com.facebook.react.utils  ProjectUtils com.facebook.react.utils  
Properties com.facebook.react.utils  
PropertyUtils com.facebook.react.utils  REACT_NATIVE_ARCHITECTURES com.facebook.react.utils  Runtime com.facebook.react.utils  SCOPED_HERMES_ENABLED com.facebook.react.utils  SCOPED_NEW_ARCH_ENABLED com.facebook.react.utils  !SCOPED_REACT_NATIVE_ARCHITECTURES com.facebook.react.utils  String com.facebook.react.utils  Suppress com.facebook.react.utils  System com.facebook.react.utils  Triple com.facebook.react.utils  URI com.facebook.react.utils  arrayOf com.facebook.react.utils  bufferedReader com.facebook.react.utils  contains com.facebook.react.utils  copyTo com.facebook.react.utils  deleteRecursively com.facebook.react.utils  
detectCliFile com.facebook.react.utils  detectEntryFile com.facebook.react.utils  detectOSAwareHermesCommand com.facebook.react.utils  detectedCliFile com.facebook.react.utils  detectedEntryFile com.facebook.react.utils  detectedHermesCommand com.facebook.react.utils  
emptyArray com.facebook.react.utils  error com.facebook.react.utils  filter com.facebook.react.utils  findPackageJsonFile com.facebook.react.utils  forEach com.facebook.react.utils  getBuiltHermescFile com.facebook.react.utils  
getHermesCBin com.facebook.react.utils  getHermesOSBin com.facebook.react.utils  getPackageNameFromManifest com.facebook.react.utils  getReactNativeArchitectures com.facebook.react.utils  hasPropertySetToFalse com.facebook.react.utils  inputStream com.facebook.react.utils  isBlank com.facebook.react.utils  isNewArchEnabled com.facebook.react.utils  
isNotBlank com.facebook.react.utils  
isNotEmpty com.facebook.react.utils  
isNullOrBlank com.facebook.react.utils  
isNullOrEmpty com.facebook.react.utils  	isWindows com.facebook.react.utils  java com.facebook.react.utils  joinToString com.facebook.react.utils  let com.facebook.react.utils  listOf com.facebook.react.utils  lowercaseCompat com.facebook.react.utils  mapOf com.facebook.react.utils  mavenRepoFromURI com.facebook.react.utils  mavenRepoFromUrl com.facebook.react.utils  moveTo com.facebook.react.utils  
mutableListOf com.facebook.react.utils  needsCodegenFromPackageJson com.facebook.react.utils  none com.facebook.react.utils  orEmpty com.facebook.react.utils  plus com.facebook.react.utils  projectPathToLibraryName com.facebook.react.utils  reactNativeDir com.facebook.react.utils  readPackageJsonFile com.facebook.react.utils  readText com.facebook.react.utils  recreateDir com.facebook.react.utils  
relativeTo com.facebook.react.utils  replace com.facebook.react.utils  runCatching com.facebook.react.utils  (shouldEnableNewArchForReactNativeVersion com.facebook.react.utils  split com.facebook.react.utils  
startsWith com.facebook.react.utils  	substring com.facebook.react.utils  takeIf com.facebook.react.utils  to com.facebook.react.utils  	toBoolean com.facebook.react.utils  toBooleanStrictOrNullCompat com.facebook.react.utils  toList com.facebook.react.utils  toRegex com.facebook.react.utils  toString com.facebook.react.utils  trim com.facebook.react.utils  
trimIndent com.facebook.react.utils  use com.facebook.react.utils  windowsAwareBashCommandLine com.facebook.react.utils  windowsAwareCommandLine com.facebook.react.utils  with com.facebook.react.utils  Action -com.facebook.react.utils.AgpConfiguratorUtils  AndroidComponentsExtension -com.facebook.react.utils.AgpConfiguratorUtils  
AppliedPlugin -com.facebook.react.utils.AgpConfiguratorUtils  DEFAULT_DEV_SERVER_PORT -com.facebook.react.utils.AgpConfiguratorUtils  LibraryExtension -com.facebook.react.utils.AgpConfiguratorUtils  Project -com.facebook.react.utils.AgpConfiguratorUtils  ReactExtension -com.facebook.react.utils.AgpConfiguratorUtils   configureBuildConfigFieldsForApp -com.facebook.react.utils.AgpConfiguratorUtils  &configureBuildConfigFieldsForLibraries -com.facebook.react.utils.AgpConfiguratorUtils  configureDevPorts -com.facebook.react.utils.AgpConfiguratorUtils  configureNamespaceForLibraries -com.facebook.react.utils.AgpConfiguratorUtils  getGETPackageNameFromManifest -com.facebook.react.utils.AgpConfiguratorUtils  getGetPackageNameFromManifest -com.facebook.react.utils.AgpConfiguratorUtils  getISNewArchEnabled -com.facebook.react.utils.AgpConfiguratorUtils  getIsNewArchEnabled -com.facebook.react.utils.AgpConfiguratorUtils  getLET -com.facebook.react.utils.AgpConfiguratorUtils  getLet -com.facebook.react.utils.AgpConfiguratorUtils  getPackageNameFromManifest -com.facebook.react.utils.AgpConfiguratorUtils  	getTAKEIf -com.facebook.react.utils.AgpConfiguratorUtils  	getTakeIf -com.facebook.react.utils.AgpConfiguratorUtils  isHermesEnabled -com.facebook.react.utils.AgpConfiguratorUtils  isNewArchEnabled -com.facebook.react.utils.AgpConfiguratorUtils  java -com.facebook.react.utils.AgpConfiguratorUtils  let -com.facebook.react.utils.AgpConfiguratorUtils  takeIf -com.facebook.react.utils.AgpConfiguratorUtils  Any ,com.facebook.react.utils.BackwardCompatUtils  Map ,com.facebook.react.utils.BackwardCompatUtils  Project ,com.facebook.react.utils.BackwardCompatUtils  String ,com.facebook.react.utils.BackwardCompatUtils  Suppress ,com.facebook.react.utils.BackwardCompatUtils  &configureBackwardCompatibilityReactMap ,com.facebook.react.utils.BackwardCompatUtils  
getISNotEmpty ,com.facebook.react.utils.BackwardCompatUtils  
getIsNotEmpty ,com.facebook.react.utils.BackwardCompatUtils  getMAPOf ,com.facebook.react.utils.BackwardCompatUtils  getMapOf ,com.facebook.react.utils.BackwardCompatUtils  
getTRIMIndent ,com.facebook.react.utils.BackwardCompatUtils  
getTrimIndent ,com.facebook.react.utils.BackwardCompatUtils  
isNotEmpty ,com.facebook.react.utils.BackwardCompatUtils  mapOf ,com.facebook.react.utils.BackwardCompatUtils  
trimIndent ,com.facebook.react.utils.BackwardCompatUtils  !DEFAULT_INTERNAL_PUBLISHING_GROUP (com.facebook.react.utils.DependencyUtils  File (com.facebook.react.utils.DependencyUtils  INTERNAL_PUBLISHING_GROUP (com.facebook.react.utils.DependencyUtils  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO (com.facebook.react.utils.DependencyUtils  INTERNAL_USE_HERMES_NIGHTLY (com.facebook.react.utils.DependencyUtils  INTERNAL_VERSION_NAME (com.facebook.react.utils.DependencyUtils  List (com.facebook.react.utils.DependencyUtils  MavenArtifactRepository (com.facebook.react.utils.DependencyUtils  Pair (com.facebook.react.utils.DependencyUtils  Project (com.facebook.react.utils.DependencyUtils  
Properties (com.facebook.react.utils.DependencyUtils  String (com.facebook.react.utils.DependencyUtils  Triple (com.facebook.react.utils.DependencyUtils  URI (com.facebook.react.utils.DependencyUtils  configureDependencies (com.facebook.react.utils.DependencyUtils  configureRepositories (com.facebook.react.utils.DependencyUtils  contains (com.facebook.react.utils.DependencyUtils  getCONTAINS (com.facebook.react.utils.DependencyUtils  getContains (com.facebook.react.utils.DependencyUtils  getDependencySubstitutions (com.facebook.react.utils.DependencyUtils  getINPUTStream (com.facebook.react.utils.DependencyUtils  
getISBlank (com.facebook.react.utils.DependencyUtils  getInputStream (com.facebook.react.utils.DependencyUtils  
getIsBlank (com.facebook.react.utils.DependencyUtils  getMUTABLEListOf (com.facebook.react.utils.DependencyUtils  getMutableListOf (com.facebook.react.utils.DependencyUtils  
getOREmpty (com.facebook.react.utils.DependencyUtils  
getOrEmpty (com.facebook.react.utils.DependencyUtils  
getSTARTSWith (com.facebook.react.utils.DependencyUtils  
getStartsWith (com.facebook.react.utils.DependencyUtils  getTOBoolean (com.facebook.react.utils.DependencyUtils  getToBoolean (com.facebook.react.utils.DependencyUtils  getUSE (com.facebook.react.utils.DependencyUtils  getUse (com.facebook.react.utils.DependencyUtils  getWITH (com.facebook.react.utils.DependencyUtils  getWith (com.facebook.react.utils.DependencyUtils  inputStream (com.facebook.react.utils.DependencyUtils  isBlank (com.facebook.react.utils.DependencyUtils  mavenRepoFromURI (com.facebook.react.utils.DependencyUtils  mavenRepoFromUrl (com.facebook.react.utils.DependencyUtils  
mutableListOf (com.facebook.react.utils.DependencyUtils  orEmpty (com.facebook.react.utils.DependencyUtils  readVersionAndGroupStrings (com.facebook.react.utils.DependencyUtils  
startsWith (com.facebook.react.utils.DependencyUtils  	toBoolean (com.facebook.react.utils.DependencyUtils  use (com.facebook.react.utils.DependencyUtils  with (com.facebook.react.utils.DependencyUtils  Action -com.facebook.react.utils.JdkConfiguratorUtils  AndroidComponentsExtension -com.facebook.react.utils.JdkConfiguratorUtils  
AppliedPlugin -com.facebook.react.utils.JdkConfiguratorUtils  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT -com.facebook.react.utils.JdkConfiguratorUtils  JavaVersion -com.facebook.react.utils.JdkConfiguratorUtils  KotlinTopLevelExtension -com.facebook.react.utils.JdkConfiguratorUtils  Project -com.facebook.react.utils.JdkConfiguratorUtils  configureJavaToolChains -com.facebook.react.utils.JdkConfiguratorUtils  java -com.facebook.react.utils.JdkConfiguratorUtils  File "com.facebook.react.utils.JsonUtils  Gson "com.facebook.react.utils.JsonUtils  ModelPackageJson "com.facebook.react.utils.JsonUtils  bufferedReader "com.facebook.react.utils.JsonUtils  fromPackageJson "com.facebook.react.utils.JsonUtils  getBUFFEREDReader "com.facebook.react.utils.JsonUtils  getBufferedReader "com.facebook.react.utils.JsonUtils  getRUNCatching "com.facebook.react.utils.JsonUtils  getRunCatching "com.facebook.react.utils.JsonUtils  getUSE "com.facebook.react.utils.JsonUtils  getUse "com.facebook.react.utils.JsonUtils  
gsonConverter "com.facebook.react.utils.JsonUtils  java "com.facebook.react.utils.JsonUtils  runCatching "com.facebook.react.utils.JsonUtils  use "com.facebook.react.utils.JsonUtils  Boolean 0com.facebook.react.utils.KotlinStdlibCompatUtils  	Character 0com.facebook.react.utils.KotlinStdlibCompatUtils  Locale 0com.facebook.react.utils.KotlinStdlibCompatUtils  String 0com.facebook.react.utils.KotlinStdlibCompatUtils  Suppress 0com.facebook.react.utils.KotlinStdlibCompatUtils  capitalizeCompat 0com.facebook.react.utils.KotlinStdlibCompatUtils  getPLUS 0com.facebook.react.utils.KotlinStdlibCompatUtils  getPlus 0com.facebook.react.utils.KotlinStdlibCompatUtils  getSUBSTRING 0com.facebook.react.utils.KotlinStdlibCompatUtils  getSubstring 0com.facebook.react.utils.KotlinStdlibCompatUtils  
isNotEmpty 0com.facebook.react.utils.KotlinStdlibCompatUtils  java 0com.facebook.react.utils.KotlinStdlibCompatUtils  lowercaseCompat 0com.facebook.react.utils.KotlinStdlibCompatUtils  plus 0com.facebook.react.utils.KotlinStdlibCompatUtils  	substring 0com.facebook.react.utils.KotlinStdlibCompatUtils  toBooleanStrictOrNullCompat 0com.facebook.react.utils.KotlinStdlibCompatUtils  AndroidComponentsExtension -com.facebook.react.utils.NdkConfiguratorUtils  Boolean -com.facebook.react.utils.NdkConfiguratorUtils  File -com.facebook.react.utils.NdkConfiguratorUtils  List -com.facebook.react.utils.NdkConfiguratorUtils  Pair -com.facebook.react.utils.NdkConfiguratorUtils  Project -com.facebook.react.utils.NdkConfiguratorUtils  ReactExtension -com.facebook.react.utils.NdkConfiguratorUtils  String -com.facebook.react.utils.NdkConfiguratorUtils  Suppress -com.facebook.react.utils.NdkConfiguratorUtils  Variant -com.facebook.react.utils.NdkConfiguratorUtils  !configureJsEnginePackagingOptions -com.facebook.react.utils.NdkConfiguratorUtils   configureNewArchPackagingOptions -com.facebook.react.utils.NdkConfiguratorUtils  configureReactNativeNdk -com.facebook.react.utils.NdkConfiguratorUtils  getGETReactNativeArchitectures -com.facebook.react.utils.NdkConfiguratorUtils  getGetReactNativeArchitectures -com.facebook.react.utils.NdkConfiguratorUtils  getISNewArchEnabled -com.facebook.react.utils.NdkConfiguratorUtils  
getISNotEmpty -com.facebook.react.utils.NdkConfiguratorUtils  getIsNewArchEnabled -com.facebook.react.utils.NdkConfiguratorUtils  
getIsNotEmpty -com.facebook.react.utils.NdkConfiguratorUtils  	getLISTOf -com.facebook.react.utils.NdkConfiguratorUtils  	getListOf -com.facebook.react.utils.NdkConfiguratorUtils  getMUTABLEListOf -com.facebook.react.utils.NdkConfiguratorUtils  getMutableListOf -com.facebook.react.utils.NdkConfiguratorUtils  getNONE -com.facebook.react.utils.NdkConfiguratorUtils  getNone -com.facebook.react.utils.NdkConfiguratorUtils  getPackagingOptionsForVariant -com.facebook.react.utils.NdkConfiguratorUtils  getReactNativeArchitectures -com.facebook.react.utils.NdkConfiguratorUtils  
getSTARTSWith -com.facebook.react.utils.NdkConfiguratorUtils  
getStartsWith -com.facebook.react.utils.NdkConfiguratorUtils  getTO -com.facebook.react.utils.NdkConfiguratorUtils  getTo -com.facebook.react.utils.NdkConfiguratorUtils  isNewArchEnabled -com.facebook.react.utils.NdkConfiguratorUtils  
isNotEmpty -com.facebook.react.utils.NdkConfiguratorUtils  java -com.facebook.react.utils.NdkConfiguratorUtils  listOf -com.facebook.react.utils.NdkConfiguratorUtils  
mutableListOf -com.facebook.react.utils.NdkConfiguratorUtils  none -com.facebook.react.utils.NdkConfiguratorUtils  
startsWith -com.facebook.react.utils.NdkConfiguratorUtils  to -com.facebook.react.utils.NdkConfiguratorUtils  Boolean com.facebook.react.utils.Os  File com.facebook.react.utils.Os  String com.facebook.react.utils.Os  System com.facebook.react.utils.Os  cliPath com.facebook.react.utils.Os  contains com.facebook.react.utils.Os  getCONTAINS com.facebook.react.utils.Os  getContains com.facebook.react.utils.Os  getLET com.facebook.react.utils.Os  getLOWERCASECompat com.facebook.react.utils.Os  getLet com.facebook.react.utils.Os  getLowercaseCompat com.facebook.react.utils.Os  
getRELATIVETo com.facebook.react.utils.Os  
getREPLACE com.facebook.react.utils.Os  
getRelativeTo com.facebook.react.utils.Os  
getReplace com.facebook.react.utils.Os  
getSTARTSWith com.facebook.react.utils.Os  
getStartsWith com.facebook.react.utils.Os  isLinuxAmd64 com.facebook.react.utils.Os  isMac com.facebook.react.utils.Os  	isWindows com.facebook.react.utils.Os  let com.facebook.react.utils.Os  lowercaseCompat com.facebook.react.utils.Os  
relativeTo com.facebook.react.utils.Os  replace com.facebook.react.utils.Os  
startsWith com.facebook.react.utils.Os  unixifyPath com.facebook.react.utils.Os  Any %com.facebook.react.utils.ProjectUtils  Boolean %com.facebook.react.utils.ProjectUtils  DirectoryProperty %com.facebook.react.utils.ProjectUtils  File %com.facebook.react.utils.ProjectUtils  HERMES_ENABLED %com.facebook.react.utils.ProjectUtils  HERMES_FALLBACK %com.facebook.react.utils.ProjectUtils  	JsonUtils %com.facebook.react.utils.ProjectUtils  List %com.facebook.react.utils.ProjectUtils  Map %com.facebook.react.utils.ProjectUtils  ModelPackageJson %com.facebook.react.utils.ProjectUtils  NEW_ARCH_ENABLED %com.facebook.react.utils.ProjectUtils  Project %com.facebook.react.utils.ProjectUtils  REACT_NATIVE_ARCHITECTURES %com.facebook.react.utils.ProjectUtils  ReactExtension %com.facebook.react.utils.ProjectUtils  SCOPED_HERMES_ENABLED %com.facebook.react.utils.ProjectUtils  SCOPED_NEW_ARCH_ENABLED %com.facebook.react.utils.ProjectUtils  !SCOPED_REACT_NATIVE_ARCHITECTURES %com.facebook.react.utils.ProjectUtils  String %com.facebook.react.utils.ProjectUtils  Suppress %com.facebook.react.utils.ProjectUtils  contains %com.facebook.react.utils.ProjectUtils  filter %com.facebook.react.utils.ProjectUtils  getCONTAINS %com.facebook.react.utils.ProjectUtils  getContains %com.facebook.react.utils.ProjectUtils  	getFILTER %com.facebook.react.utils.ProjectUtils  	getFilter %com.facebook.react.utils.ProjectUtils  
getISNotBlank %com.facebook.react.utils.ProjectUtils  
getIsNotBlank %com.facebook.react.utils.ProjectUtils  getLOWERCASECompat %com.facebook.react.utils.ProjectUtils  getLowercaseCompat %com.facebook.react.utils.ProjectUtils  getMUTABLEListOf %com.facebook.react.utils.ProjectUtils  getMutableListOf %com.facebook.react.utils.ProjectUtils  getREADPackageJsonFile %com.facebook.react.utils.ProjectUtils  getReactNativeArchitectures %com.facebook.react.utils.ProjectUtils  getReadPackageJsonFile %com.facebook.react.utils.ProjectUtils  getSPLIT %com.facebook.react.utils.ProjectUtils  getSplit %com.facebook.react.utils.ProjectUtils  getTOBoolean %com.facebook.react.utils.ProjectUtils  getTOBooleanStrictOrNullCompat %com.facebook.react.utils.ProjectUtils  
getTORegex %com.facebook.react.utils.ProjectUtils  getTOString %com.facebook.react.utils.ProjectUtils  getToBoolean %com.facebook.react.utils.ProjectUtils  getToBooleanStrictOrNullCompat %com.facebook.react.utils.ProjectUtils  
getToRegex %com.facebook.react.utils.ProjectUtils  getToString %com.facebook.react.utils.ProjectUtils  hasPropertySetToFalse %com.facebook.react.utils.ProjectUtils  isHermesEnabled %com.facebook.react.utils.ProjectUtils  isNewArchEnabled %com.facebook.react.utils.ProjectUtils  
isNotBlank %com.facebook.react.utils.ProjectUtils  lowercaseCompat %com.facebook.react.utils.ProjectUtils  
mutableListOf %com.facebook.react.utils.ProjectUtils  needsCodegenFromPackageJson %com.facebook.react.utils.ProjectUtils  reactNativeDir %com.facebook.react.utils.ProjectUtils  readPackageJsonFile %com.facebook.react.utils.ProjectUtils  (shouldEnableNewArchForReactNativeVersion %com.facebook.react.utils.ProjectUtils  &shouldWarnIfNewArchFlagIsSetInPrealpha %com.facebook.react.utils.ProjectUtils  split %com.facebook.react.utils.ProjectUtils  	toBoolean %com.facebook.react.utils.ProjectUtils  toBooleanStrictOrNullCompat %com.facebook.react.utils.ProjectUtils  toRegex %com.facebook.react.utils.ProjectUtils  toString %com.facebook.react.utils.ProjectUtils  !DEFAULT_INTERNAL_PUBLISHING_GROUP &com.facebook.react.utils.PropertyUtils  HERMES_ENABLED &com.facebook.react.utils.PropertyUtils  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT &com.facebook.react.utils.PropertyUtils  INTERNAL_PUBLISHING_GROUP &com.facebook.react.utils.PropertyUtils  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO &com.facebook.react.utils.PropertyUtils  INTERNAL_USE_HERMES_NIGHTLY &com.facebook.react.utils.PropertyUtils  INTERNAL_VERSION_NAME &com.facebook.react.utils.PropertyUtils  NEW_ARCH_ENABLED &com.facebook.react.utils.PropertyUtils  REACT_NATIVE_ARCHITECTURES &com.facebook.react.utils.PropertyUtils  SCOPED_HERMES_ENABLED &com.facebook.react.utils.PropertyUtils  SCOPED_NEW_ARCH_ENABLED &com.facebook.react.utils.PropertyUtils  !SCOPED_REACT_NATIVE_ARCHITECTURES &com.facebook.react.utils.PropertyUtils  Gson com.google.gson  fromJson com.google.gson.Gson  BufferedReader java.io  File java.io  FileInputStream java.io  InputStream java.io  Serializable java.io  getREADText java.io.BufferedReader  getReadText java.io.BufferedReader  getUSE java.io.BufferedReader  getUse java.io.BufferedReader  readText java.io.BufferedReader  use java.io.BufferedReader  File java.io.File  absolutePath java.io.File  apply java.io.File  boostVersion java.io.File  bufferedReader java.io.File  
canonicalPath java.io.File  cliPath java.io.File  copyTo java.io.File  delete java.io.File  deleteRecursively java.io.File  equals java.io.File  exists java.io.File  getABSOLUTEPath java.io.File  getAPPLY java.io.File  getAbsolutePath java.io.File  getApply java.io.File  getBOOSTVersion java.io.File  getBUFFEREDReader java.io.File  getBoostVersion java.io.File  getBufferedReader java.io.File  getCANONICALPath java.io.File  
getCLIPath java.io.File  	getCOPYTo java.io.File  getCanonicalPath java.io.File  
getCliPath java.io.File  	getCopyTo java.io.File  getDELETERecursively java.io.File  getDeleteRecursively java.io.File  getINPUTStream java.io.File  getISWindows java.io.File  getInputStream java.io.File  getIsWindows java.io.File  getLET java.io.File  getLet java.io.File  	getMOVETo java.io.File  	getMoveTo java.io.File  
getPARENTFile java.io.File  getPATH java.io.File  
getParentFile java.io.File  getPath java.io.File  
getRELATIVETo java.io.File  
getRelativeTo java.io.File  	getTAKEIf java.io.File  	getTakeIf java.io.File  inputStream java.io.File  	isWindows java.io.File  let java.io.File  mkdirs java.io.File  moveTo java.io.File  
parentFile java.io.File  path java.io.File  
relativeTo java.io.File  renameTo java.io.File  
separatorChar java.io.File  setAbsolutePath java.io.File  setCanonicalPath java.io.File  
setParentFile java.io.File  setPath java.io.File  takeIf java.io.File  toURI java.io.File  getUSE java.io.FileInputStream  getUse java.io.FileInputStream  use java.io.FileInputStream  bufferedReader java.io.InputStream  getBUFFEREDReader java.io.InputStream  getBufferedReader java.io.InputStream  getUSE java.io.InputStream  getUse java.io.InputStream  use java.io.InputStream  readText java.io.Reader  use java.io.Reader  Action 	java.lang  AndroidComponentsExtension 	java.lang  BUILD_SCRIPT_PATH 	java.lang  Boolean 	java.lang  BundleHermesCTask 	java.lang  	Character 	java.lang  Class 	java.lang  DEFAULT_DEV_SERVER_PORT 	java.lang  !DEFAULT_INTERNAL_PUBLISHING_GROUP 	java.lang  DocumentBuilderFactory 	java.lang  DuplicatesStrategy 	java.lang  File 	java.lang  GenerateCodegenArtifactsTask 	java.lang  GenerateCodegenSchemaTask 	java.lang  Gson 	java.lang  HERMES_ENABLED 	java.lang  HERMES_FALLBACK 	java.lang  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT 	java.lang  INTERNAL_PUBLISHING_GROUP 	java.lang  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO 	java.lang  INTERNAL_USE_HERMES_NIGHTLY 	java.lang  INTERNAL_VERSION_NAME 	java.lang  JavaVersion 	java.lang  	JsonUtils 	java.lang  Jvm 	java.lang  KotlinTopLevelExtension 	java.lang  LibraryExtension 	java.lang  Locale 	java.lang  ModelPackageJson 	java.lang  NEW_ARCH_ENABLED 	java.lang  Pair 	java.lang  PrivateReactExtension 	java.lang  Process 	java.lang  
Properties 	java.lang  REACT_NATIVE_ARCHITECTURES 	java.lang  ReactExtension 	java.lang  
ReplaceTokens 	java.lang  Runtime 	java.lang  SCOPED_HERMES_ENABLED 	java.lang  SCOPED_NEW_ARCH_ENABLED 	java.lang  !SCOPED_REACT_NATIVE_ARCHITECTURES 	java.lang  String 	java.lang  System 	java.lang  Task 	java.lang  Triple 	java.lang  URI 	java.lang  any 	java.lang  apply 	java.lang  arrayOf 	java.lang  boostVersion 	java.lang  bufferedReader 	java.lang  
bundleCommand 	java.lang  bundleConfig 	java.lang  capitalizeCompat 	java.lang  cliFile 	java.lang  cliPath 	java.lang  &configureBackwardCompatibilityReactMap 	java.lang   configureBuildConfigFieldsForApp 	java.lang  &configureBuildConfigFieldsForLibraries 	java.lang  configureDependencies 	java.lang  configureDevPorts 	java.lang  configureJavaToolChains 	java.lang  !configureJsEnginePackagingOptions 	java.lang  configureNamespaceForLibraries 	java.lang   configureNewArchPackagingOptions 	java.lang  configureReactNativeNdk 	java.lang  configureReactTasks 	java.lang  configureRepositories 	java.lang  contains 	java.lang  copyTo 	java.lang  deleteRecursively 	java.lang  	dependsOn 	java.lang  detectOSAwareHermesCommand 	java.lang  detectedCliFile 	java.lang  detectedEntryFile 	java.lang  
devEnabled 	java.lang  
emptyArray 	java.lang  	emptyList 	java.lang  	entryFile 	java.lang  equals 	java.lang  error 	java.lang  exitProcess 	java.lang  extraPackagerArgs 	java.lang  filter 	java.lang  findPackageJsonFile 	java.lang  forEach 	java.lang  getPackageNameFromManifest 	java.lang  getReactNativeArchitectures 	java.lang  hasPropertySetToFalse 	java.lang  inputStream 	java.lang  isBlank 	java.lang  isNewArchEnabled 	java.lang  
isNotBlank 	java.lang  
isNotEmpty 	java.lang  
isNullOrBlank 	java.lang  
isNullOrEmpty 	java.lang  	isWindows 	java.lang  java 	java.lang  joinToString 	java.lang  let 	java.lang  listOf 	java.lang  lowercaseCompat 	java.lang  mapOf 	java.lang  
minifyEnabled 	java.lang  moveTo 	java.lang  
mutableListOf 	java.lang  needsCodegenFromPackageJson 	java.lang  nodeExecutableAndArgs 	java.lang  none 	java.lang  orEmpty 	java.lang  plus 	java.lang  projectPathToLibraryName 	java.lang  reactNativeDir 	java.lang  readPackageJsonFile 	java.lang  readText 	java.lang  readVersionAndGroupStrings 	java.lang  
relativeTo 	java.lang  removeSuffix 	java.lang  replace 	java.lang  resourcesDir 	java.lang  runCatching 	java.lang  (shouldEnableNewArchForReactNativeVersion 	java.lang  &shouldWarnIfNewArchFlagIsSetInPrealpha 	java.lang  split 	java.lang  
startsWith 	java.lang  	substring 	java.lang  takeIf 	java.lang  to 	java.lang  	toBoolean 	java.lang  toBooleanStrictOrNullCompat 	java.lang  toIntOrNull 	java.lang  toList 	java.lang  toRegex 	java.lang  toString 	java.lang  toTypedArray 	java.lang  trim 	java.lang  
trimIndent 	java.lang  unixifyPath 	java.lang  use 	java.lang  windowsAwareBashCommandLine 	java.lang  windowsAwareCommandLine 	java.lang  with 	java.lang  toUpperCase java.lang.Character  getINPUTStream java.lang.Process  getInputStream java.lang.Process  inputStream java.lang.Process  setInputStream java.lang.Process  exec java.lang.Runtime  
getRuntime java.lang.Runtime  Locale java.lang.String  toLowerCase java.lang.String  getProperty java.lang.System  getenv java.lang.System  URI java.net  create java.net.URI  !DEFAULT_INTERNAL_PUBLISHING_GROUP 	java.util  File 	java.util  INTERNAL_PUBLISHING_GROUP 	java.util  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO 	java.util  INTERNAL_USE_HERMES_NIGHTLY 	java.util  INTERNAL_VERSION_NAME 	java.util  Locale 	java.util  Pair 	java.util  
Properties 	java.util  Triple 	java.util  URI 	java.util  contains 	java.util  forEach 	java.util  inputStream 	java.util  isBlank 	java.util  
isNotEmpty 	java.util  mapOf 	java.util  mavenRepoFromURI 	java.util  mavenRepoFromUrl 	java.util  
mutableListOf 	java.util  orEmpty 	java.util  
startsWith 	java.util  	toBoolean 	java.util  
trimIndent 	java.util  use 	java.util  with 	java.util  get java.util.Dictionary  load java.util.Dictionary  get java.util.Hashtable  load java.util.Hashtable  ROOT java.util.Locale  get java.util.Properties  load java.util.Properties  Inject javax.inject  DocumentBuilder javax.xml.parsers  DocumentBuilderFactory javax.xml.parsers  parse !javax.xml.parsers.DocumentBuilder  newDocumentBuilder (javax.xml.parsers.DocumentBuilderFactory  newInstance (javax.xml.parsers.DocumentBuilderFactory  Action kotlin  AndroidComponentsExtension kotlin  Any kotlin  Array kotlin  BUILD_SCRIPT_PATH kotlin  Boolean kotlin  BundleHermesCTask kotlin  Char kotlin  	Character kotlin  DEFAULT_DEV_SERVER_PORT kotlin  !DEFAULT_INTERNAL_PUBLISHING_GROUP kotlin  DocumentBuilderFactory kotlin  DuplicatesStrategy kotlin  	Exception kotlin  File kotlin  	Function1 kotlin  GenerateCodegenArtifactsTask kotlin  GenerateCodegenSchemaTask kotlin  Gson kotlin  HERMES_ENABLED kotlin  HERMES_FALLBACK kotlin  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT kotlin  INTERNAL_PUBLISHING_GROUP kotlin  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO kotlin  INTERNAL_USE_HERMES_NIGHTLY kotlin  INTERNAL_VERSION_NAME kotlin  Int kotlin  JavaVersion kotlin  	JsonUtils kotlin  Jvm kotlin  JvmName kotlin  KotlinTopLevelExtension kotlin  LibraryExtension kotlin  Locale kotlin  ModelPackageJson kotlin  NEW_ARCH_ENABLED kotlin  Nothing kotlin  Pair kotlin  PrivateReactExtension kotlin  
Properties kotlin  REACT_NATIVE_ARCHITECTURES kotlin  ReactExtension kotlin  
ReplaceTokens kotlin  Result kotlin  Runtime kotlin  SCOPED_HERMES_ENABLED kotlin  SCOPED_NEW_ARCH_ENABLED kotlin  !SCOPED_REACT_NATIVE_ARCHITECTURES kotlin  String kotlin  Suppress kotlin  System kotlin  Task kotlin  Triple kotlin  URI kotlin  Unit kotlin  any kotlin  apply kotlin  arrayOf kotlin  boostVersion kotlin  bufferedReader kotlin  
bundleCommand kotlin  bundleConfig kotlin  capitalizeCompat kotlin  cliFile kotlin  cliPath kotlin  &configureBackwardCompatibilityReactMap kotlin   configureBuildConfigFieldsForApp kotlin  &configureBuildConfigFieldsForLibraries kotlin  configureDependencies kotlin  configureDevPorts kotlin  configureJavaToolChains kotlin  !configureJsEnginePackagingOptions kotlin  configureNamespaceForLibraries kotlin   configureNewArchPackagingOptions kotlin  configureReactNativeNdk kotlin  configureReactTasks kotlin  configureRepositories kotlin  contains kotlin  copyTo kotlin  deleteRecursively kotlin  	dependsOn kotlin  detectOSAwareHermesCommand kotlin  detectedCliFile kotlin  detectedEntryFile kotlin  
devEnabled kotlin  
emptyArray kotlin  	emptyList kotlin  	entryFile kotlin  equals kotlin  error kotlin  exitProcess kotlin  extraPackagerArgs kotlin  filter kotlin  findPackageJsonFile kotlin  forEach kotlin  getPackageNameFromManifest kotlin  getReactNativeArchitectures kotlin  hasPropertySetToFalse kotlin  inputStream kotlin  isBlank kotlin  isNewArchEnabled kotlin  
isNotBlank kotlin  
isNotEmpty kotlin  
isNullOrBlank kotlin  
isNullOrEmpty kotlin  	isWindows kotlin  java kotlin  joinToString kotlin  let kotlin  listOf kotlin  lowercaseCompat kotlin  mapOf kotlin  
minifyEnabled kotlin  moveTo kotlin  
mutableListOf kotlin  needsCodegenFromPackageJson kotlin  nodeExecutableAndArgs kotlin  none kotlin  orEmpty kotlin  plus kotlin  projectPathToLibraryName kotlin  reactNativeDir kotlin  readPackageJsonFile kotlin  readText kotlin  readVersionAndGroupStrings kotlin  
relativeTo kotlin  removeSuffix kotlin  replace kotlin  resourcesDir kotlin  runCatching kotlin  (shouldEnableNewArchForReactNativeVersion kotlin  &shouldWarnIfNewArchFlagIsSetInPrealpha kotlin  split kotlin  
startsWith kotlin  	substring kotlin  takeIf kotlin  to kotlin  	toBoolean kotlin  toBooleanStrictOrNullCompat kotlin  toIntOrNull kotlin  toList kotlin  toRegex kotlin  toString kotlin  toTypedArray kotlin  trim kotlin  
trimIndent kotlin  unixifyPath kotlin  use kotlin  windowsAwareBashCommandLine kotlin  windowsAwareCommandLine kotlin  with kotlin  getLOWERCASECompat 
kotlin.Any  getLowercaseCompat 
kotlin.Any  getTOString 
kotlin.Any  getToString 
kotlin.Any  	getTOList kotlin.Array  	getToList kotlin.Array  	Companion kotlin.Boolean  getPLUS kotlin.Char  getPlus kotlin.Char  
component1 kotlin.Pair  
component2 kotlin.Pair  first kotlin.Pair  second kotlin.Pair  	getOrNull 
kotlin.Result  	Companion 
kotlin.String  getCAPITALIZECompat 
kotlin.String  getCONTAINS 
kotlin.String  getCapitalizeCompat 
kotlin.String  getContains 
kotlin.String  	getEQUALS 
kotlin.String  	getEquals 
kotlin.String  
getISBlank 
kotlin.String  
getISNotBlank 
kotlin.String  
getISNotEmpty 
kotlin.String  getISNullOrBlank 
kotlin.String  getISNullOrEmpty 
kotlin.String  
getIsBlank 
kotlin.String  
getIsNotBlank 
kotlin.String  
getIsNotEmpty 
kotlin.String  getIsNullOrBlank 
kotlin.String  getIsNullOrEmpty 
kotlin.String  getLET 
kotlin.String  getLOWERCASECompat 
kotlin.String  getLet 
kotlin.String  getLowercaseCompat 
kotlin.String  
getOREmpty 
kotlin.String  
getOrEmpty 
kotlin.String  getPLUS 
kotlin.String  getPlus 
kotlin.String  getREMOVESuffix 
kotlin.String  
getREPLACE 
kotlin.String  getRemoveSuffix 
kotlin.String  
getReplace 
kotlin.String  getSPLIT 
kotlin.String  
getSTARTSWith 
kotlin.String  getSUBSTRING 
kotlin.String  getSplit 
kotlin.String  
getStartsWith 
kotlin.String  getSubstring 
kotlin.String  getTO 
kotlin.String  getTOBoolean 
kotlin.String  getTOBooleanStrictOrNullCompat 
kotlin.String  getTOIntOrNull 
kotlin.String  
getTORegex 
kotlin.String  getTRIM 
kotlin.String  
getTRIMIndent 
kotlin.String  getTo 
kotlin.String  getToBoolean 
kotlin.String  getToBooleanStrictOrNullCompat 
kotlin.String  getToIntOrNull 
kotlin.String  
getToRegex 
kotlin.String  getTrim 
kotlin.String  
getTrimIndent 
kotlin.String  getUNIXIFYPath 
kotlin.String  getUnixifyPath 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrBlank 
kotlin.String  
isNullOrEmpty 
kotlin.String  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  Action kotlin.annotation  AndroidComponentsExtension kotlin.annotation  BUILD_SCRIPT_PATH kotlin.annotation  Boolean kotlin.annotation  BundleHermesCTask kotlin.annotation  	Character kotlin.annotation  DEFAULT_DEV_SERVER_PORT kotlin.annotation  !DEFAULT_INTERNAL_PUBLISHING_GROUP kotlin.annotation  DocumentBuilderFactory kotlin.annotation  DuplicatesStrategy kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  GenerateCodegenArtifactsTask kotlin.annotation  GenerateCodegenSchemaTask kotlin.annotation  Gson kotlin.annotation  HERMES_ENABLED kotlin.annotation  HERMES_FALLBACK kotlin.annotation  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT kotlin.annotation  INTERNAL_PUBLISHING_GROUP kotlin.annotation  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO kotlin.annotation  INTERNAL_USE_HERMES_NIGHTLY kotlin.annotation  INTERNAL_VERSION_NAME kotlin.annotation  JavaVersion kotlin.annotation  	JsonUtils kotlin.annotation  Jvm kotlin.annotation  JvmName kotlin.annotation  KotlinTopLevelExtension kotlin.annotation  LibraryExtension kotlin.annotation  Locale kotlin.annotation  ModelPackageJson kotlin.annotation  NEW_ARCH_ENABLED kotlin.annotation  Pair kotlin.annotation  PrivateReactExtension kotlin.annotation  
Properties kotlin.annotation  REACT_NATIVE_ARCHITECTURES kotlin.annotation  ReactExtension kotlin.annotation  
ReplaceTokens kotlin.annotation  Runtime kotlin.annotation  SCOPED_HERMES_ENABLED kotlin.annotation  SCOPED_NEW_ARCH_ENABLED kotlin.annotation  !SCOPED_REACT_NATIVE_ARCHITECTURES kotlin.annotation  String kotlin.annotation  System kotlin.annotation  Task kotlin.annotation  Triple kotlin.annotation  URI kotlin.annotation  any kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  boostVersion kotlin.annotation  bufferedReader kotlin.annotation  
bundleCommand kotlin.annotation  bundleConfig kotlin.annotation  capitalizeCompat kotlin.annotation  cliFile kotlin.annotation  cliPath kotlin.annotation  &configureBackwardCompatibilityReactMap kotlin.annotation   configureBuildConfigFieldsForApp kotlin.annotation  &configureBuildConfigFieldsForLibraries kotlin.annotation  configureDependencies kotlin.annotation  configureDevPorts kotlin.annotation  configureJavaToolChains kotlin.annotation  !configureJsEnginePackagingOptions kotlin.annotation  configureNamespaceForLibraries kotlin.annotation   configureNewArchPackagingOptions kotlin.annotation  configureReactNativeNdk kotlin.annotation  configureReactTasks kotlin.annotation  configureRepositories kotlin.annotation  contains kotlin.annotation  copyTo kotlin.annotation  deleteRecursively kotlin.annotation  	dependsOn kotlin.annotation  detectOSAwareHermesCommand kotlin.annotation  detectedCliFile kotlin.annotation  detectedEntryFile kotlin.annotation  
devEnabled kotlin.annotation  
emptyArray kotlin.annotation  	emptyList kotlin.annotation  	entryFile kotlin.annotation  equals kotlin.annotation  error kotlin.annotation  exitProcess kotlin.annotation  extraPackagerArgs kotlin.annotation  filter kotlin.annotation  findPackageJsonFile kotlin.annotation  forEach kotlin.annotation  getPackageNameFromManifest kotlin.annotation  getReactNativeArchitectures kotlin.annotation  hasPropertySetToFalse kotlin.annotation  inputStream kotlin.annotation  isBlank kotlin.annotation  isNewArchEnabled kotlin.annotation  
isNotBlank kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrBlank kotlin.annotation  
isNullOrEmpty kotlin.annotation  	isWindows kotlin.annotation  java kotlin.annotation  joinToString kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  lowercaseCompat kotlin.annotation  mapOf kotlin.annotation  
minifyEnabled kotlin.annotation  moveTo kotlin.annotation  
mutableListOf kotlin.annotation  needsCodegenFromPackageJson kotlin.annotation  nodeExecutableAndArgs kotlin.annotation  none kotlin.annotation  orEmpty kotlin.annotation  plus kotlin.annotation  projectPathToLibraryName kotlin.annotation  reactNativeDir kotlin.annotation  readPackageJsonFile kotlin.annotation  readText kotlin.annotation  readVersionAndGroupStrings kotlin.annotation  
relativeTo kotlin.annotation  removeSuffix kotlin.annotation  replace kotlin.annotation  resourcesDir kotlin.annotation  runCatching kotlin.annotation  (shouldEnableNewArchForReactNativeVersion kotlin.annotation  &shouldWarnIfNewArchFlagIsSetInPrealpha kotlin.annotation  split kotlin.annotation  
startsWith kotlin.annotation  	substring kotlin.annotation  takeIf kotlin.annotation  to kotlin.annotation  	toBoolean kotlin.annotation  toBooleanStrictOrNullCompat kotlin.annotation  toIntOrNull kotlin.annotation  toList kotlin.annotation  toRegex kotlin.annotation  toString kotlin.annotation  toTypedArray kotlin.annotation  trim kotlin.annotation  
trimIndent kotlin.annotation  unixifyPath kotlin.annotation  use kotlin.annotation  windowsAwareBashCommandLine kotlin.annotation  windowsAwareCommandLine kotlin.annotation  with kotlin.annotation  Action kotlin.collections  AndroidComponentsExtension kotlin.collections  BUILD_SCRIPT_PATH kotlin.collections  Boolean kotlin.collections  BundleHermesCTask kotlin.collections  	Character kotlin.collections  DEFAULT_DEV_SERVER_PORT kotlin.collections  !DEFAULT_INTERNAL_PUBLISHING_GROUP kotlin.collections  DocumentBuilderFactory kotlin.collections  DuplicatesStrategy kotlin.collections  	Exception kotlin.collections  File kotlin.collections  GenerateCodegenArtifactsTask kotlin.collections  GenerateCodegenSchemaTask kotlin.collections  Gson kotlin.collections  HERMES_ENABLED kotlin.collections  HERMES_FALLBACK kotlin.collections  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT kotlin.collections  INTERNAL_PUBLISHING_GROUP kotlin.collections  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO kotlin.collections  INTERNAL_USE_HERMES_NIGHTLY kotlin.collections  INTERNAL_VERSION_NAME kotlin.collections  JavaVersion kotlin.collections  	JsonUtils kotlin.collections  Jvm kotlin.collections  JvmName kotlin.collections  KotlinTopLevelExtension kotlin.collections  LibraryExtension kotlin.collections  List kotlin.collections  Locale kotlin.collections  Map kotlin.collections  ModelPackageJson kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  NEW_ARCH_ENABLED kotlin.collections  Pair kotlin.collections  PrivateReactExtension kotlin.collections  
Properties kotlin.collections  REACT_NATIVE_ARCHITECTURES kotlin.collections  ReactExtension kotlin.collections  
ReplaceTokens kotlin.collections  Runtime kotlin.collections  SCOPED_HERMES_ENABLED kotlin.collections  SCOPED_NEW_ARCH_ENABLED kotlin.collections  !SCOPED_REACT_NATIVE_ARCHITECTURES kotlin.collections  String kotlin.collections  System kotlin.collections  Task kotlin.collections  Triple kotlin.collections  URI kotlin.collections  any kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  boostVersion kotlin.collections  bufferedReader kotlin.collections  
bundleCommand kotlin.collections  bundleConfig kotlin.collections  capitalizeCompat kotlin.collections  cliFile kotlin.collections  cliPath kotlin.collections  &configureBackwardCompatibilityReactMap kotlin.collections   configureBuildConfigFieldsForApp kotlin.collections  &configureBuildConfigFieldsForLibraries kotlin.collections  configureDependencies kotlin.collections  configureDevPorts kotlin.collections  configureJavaToolChains kotlin.collections  !configureJsEnginePackagingOptions kotlin.collections  configureNamespaceForLibraries kotlin.collections   configureNewArchPackagingOptions kotlin.collections  configureReactNativeNdk kotlin.collections  configureReactTasks kotlin.collections  configureRepositories kotlin.collections  contains kotlin.collections  copyTo kotlin.collections  deleteRecursively kotlin.collections  	dependsOn kotlin.collections  detectOSAwareHermesCommand kotlin.collections  detectedCliFile kotlin.collections  detectedEntryFile kotlin.collections  
devEnabled kotlin.collections  
emptyArray kotlin.collections  	emptyList kotlin.collections  	entryFile kotlin.collections  equals kotlin.collections  error kotlin.collections  exitProcess kotlin.collections  extraPackagerArgs kotlin.collections  filter kotlin.collections  findPackageJsonFile kotlin.collections  forEach kotlin.collections  getPackageNameFromManifest kotlin.collections  getReactNativeArchitectures kotlin.collections  hasPropertySetToFalse kotlin.collections  inputStream kotlin.collections  isBlank kotlin.collections  isNewArchEnabled kotlin.collections  
isNotBlank kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrBlank kotlin.collections  
isNullOrEmpty kotlin.collections  	isWindows kotlin.collections  java kotlin.collections  joinToString kotlin.collections  let kotlin.collections  listOf kotlin.collections  lowercaseCompat kotlin.collections  mapOf kotlin.collections  
minifyEnabled kotlin.collections  moveTo kotlin.collections  
mutableListOf kotlin.collections  needsCodegenFromPackageJson kotlin.collections  nodeExecutableAndArgs kotlin.collections  none kotlin.collections  orEmpty kotlin.collections  plus kotlin.collections  projectPathToLibraryName kotlin.collections  reactNativeDir kotlin.collections  readPackageJsonFile kotlin.collections  readText kotlin.collections  readVersionAndGroupStrings kotlin.collections  
relativeTo kotlin.collections  removeSuffix kotlin.collections  replace kotlin.collections  resourcesDir kotlin.collections  runCatching kotlin.collections  (shouldEnableNewArchForReactNativeVersion kotlin.collections  &shouldWarnIfNewArchFlagIsSetInPrealpha kotlin.collections  split kotlin.collections  
startsWith kotlin.collections  	substring kotlin.collections  takeIf kotlin.collections  to kotlin.collections  	toBoolean kotlin.collections  toBooleanStrictOrNullCompat kotlin.collections  toIntOrNull kotlin.collections  toList kotlin.collections  toRegex kotlin.collections  toString kotlin.collections  toTypedArray kotlin.collections  trim kotlin.collections  
trimIndent kotlin.collections  unixifyPath kotlin.collections  use kotlin.collections  windowsAwareBashCommandLine kotlin.collections  windowsAwareCommandLine kotlin.collections  with kotlin.collections  	getFILTER kotlin.collections.List  	getFilter kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getJOINToString kotlin.collections.List  getJoinToString kotlin.collections.List  getPLUS kotlin.collections.List  getPlus kotlin.collections.List  
isNotEmpty kotlin.collections.List  
getISNotEmpty kotlin.collections.Map  
getIsNotEmpty kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  getANY kotlin.collections.MutableList  getAPPLY kotlin.collections.MutableList  getAny kotlin.collections.MutableList  getApply kotlin.collections.MutableList  getBUNDLECommand kotlin.collections.MutableList  getBUNDLEConfig kotlin.collections.MutableList  getBundleCommand kotlin.collections.MutableList  getBundleConfig kotlin.collections.MutableList  
getCLIFile kotlin.collections.MutableList  
getCLIPath kotlin.collections.MutableList  
getCliFile kotlin.collections.MutableList  
getCliPath kotlin.collections.MutableList  
getDEVEnabled kotlin.collections.MutableList  
getDevEnabled kotlin.collections.MutableList  getENTRYFile kotlin.collections.MutableList  getEXTRAPackagerArgs kotlin.collections.MutableList  getEntryFile kotlin.collections.MutableList  getExtraPackagerArgs kotlin.collections.MutableList  
getISNotEmpty kotlin.collections.MutableList  
getIsNotEmpty kotlin.collections.MutableList  getMINIFYEnabled kotlin.collections.MutableList  getMinifyEnabled kotlin.collections.MutableList  getNODEExecutableAndArgs kotlin.collections.MutableList  getNONE kotlin.collections.MutableList  getNodeExecutableAndArgs kotlin.collections.MutableList  getNone kotlin.collections.MutableList  getRESOURCESDir kotlin.collections.MutableList  getResourcesDir kotlin.collections.MutableList  getTO kotlin.collections.MutableList  getTOTypedArray kotlin.collections.MutableList  getTo kotlin.collections.MutableList  getToTypedArray kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  Action kotlin.comparisons  AndroidComponentsExtension kotlin.comparisons  BUILD_SCRIPT_PATH kotlin.comparisons  Boolean kotlin.comparisons  BundleHermesCTask kotlin.comparisons  	Character kotlin.comparisons  DEFAULT_DEV_SERVER_PORT kotlin.comparisons  !DEFAULT_INTERNAL_PUBLISHING_GROUP kotlin.comparisons  DocumentBuilderFactory kotlin.comparisons  DuplicatesStrategy kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  GenerateCodegenArtifactsTask kotlin.comparisons  GenerateCodegenSchemaTask kotlin.comparisons  Gson kotlin.comparisons  HERMES_ENABLED kotlin.comparisons  HERMES_FALLBACK kotlin.comparisons  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT kotlin.comparisons  INTERNAL_PUBLISHING_GROUP kotlin.comparisons  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO kotlin.comparisons  INTERNAL_USE_HERMES_NIGHTLY kotlin.comparisons  INTERNAL_VERSION_NAME kotlin.comparisons  JavaVersion kotlin.comparisons  	JsonUtils kotlin.comparisons  Jvm kotlin.comparisons  JvmName kotlin.comparisons  KotlinTopLevelExtension kotlin.comparisons  LibraryExtension kotlin.comparisons  Locale kotlin.comparisons  ModelPackageJson kotlin.comparisons  NEW_ARCH_ENABLED kotlin.comparisons  Pair kotlin.comparisons  PrivateReactExtension kotlin.comparisons  
Properties kotlin.comparisons  REACT_NATIVE_ARCHITECTURES kotlin.comparisons  ReactExtension kotlin.comparisons  
ReplaceTokens kotlin.comparisons  Runtime kotlin.comparisons  SCOPED_HERMES_ENABLED kotlin.comparisons  SCOPED_NEW_ARCH_ENABLED kotlin.comparisons  !SCOPED_REACT_NATIVE_ARCHITECTURES kotlin.comparisons  String kotlin.comparisons  System kotlin.comparisons  Task kotlin.comparisons  Triple kotlin.comparisons  URI kotlin.comparisons  any kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  boostVersion kotlin.comparisons  bufferedReader kotlin.comparisons  
bundleCommand kotlin.comparisons  bundleConfig kotlin.comparisons  capitalizeCompat kotlin.comparisons  cliFile kotlin.comparisons  cliPath kotlin.comparisons  &configureBackwardCompatibilityReactMap kotlin.comparisons   configureBuildConfigFieldsForApp kotlin.comparisons  &configureBuildConfigFieldsForLibraries kotlin.comparisons  configureDependencies kotlin.comparisons  configureDevPorts kotlin.comparisons  configureJavaToolChains kotlin.comparisons  !configureJsEnginePackagingOptions kotlin.comparisons  configureNamespaceForLibraries kotlin.comparisons   configureNewArchPackagingOptions kotlin.comparisons  configureReactNativeNdk kotlin.comparisons  configureReactTasks kotlin.comparisons  configureRepositories kotlin.comparisons  contains kotlin.comparisons  copyTo kotlin.comparisons  deleteRecursively kotlin.comparisons  	dependsOn kotlin.comparisons  detectOSAwareHermesCommand kotlin.comparisons  detectedCliFile kotlin.comparisons  detectedEntryFile kotlin.comparisons  
devEnabled kotlin.comparisons  
emptyArray kotlin.comparisons  	emptyList kotlin.comparisons  	entryFile kotlin.comparisons  equals kotlin.comparisons  error kotlin.comparisons  exitProcess kotlin.comparisons  extraPackagerArgs kotlin.comparisons  filter kotlin.comparisons  findPackageJsonFile kotlin.comparisons  forEach kotlin.comparisons  getPackageNameFromManifest kotlin.comparisons  getReactNativeArchitectures kotlin.comparisons  hasPropertySetToFalse kotlin.comparisons  inputStream kotlin.comparisons  isBlank kotlin.comparisons  isNewArchEnabled kotlin.comparisons  
isNotBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrBlank kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  	isWindows kotlin.comparisons  java kotlin.comparisons  joinToString kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  lowercaseCompat kotlin.comparisons  mapOf kotlin.comparisons  
minifyEnabled kotlin.comparisons  moveTo kotlin.comparisons  
mutableListOf kotlin.comparisons  needsCodegenFromPackageJson kotlin.comparisons  nodeExecutableAndArgs kotlin.comparisons  none kotlin.comparisons  orEmpty kotlin.comparisons  plus kotlin.comparisons  projectPathToLibraryName kotlin.comparisons  reactNativeDir kotlin.comparisons  readPackageJsonFile kotlin.comparisons  readText kotlin.comparisons  readVersionAndGroupStrings kotlin.comparisons  
relativeTo kotlin.comparisons  removeSuffix kotlin.comparisons  replace kotlin.comparisons  resourcesDir kotlin.comparisons  runCatching kotlin.comparisons  (shouldEnableNewArchForReactNativeVersion kotlin.comparisons  &shouldWarnIfNewArchFlagIsSetInPrealpha kotlin.comparisons  split kotlin.comparisons  
startsWith kotlin.comparisons  	substring kotlin.comparisons  takeIf kotlin.comparisons  to kotlin.comparisons  	toBoolean kotlin.comparisons  toBooleanStrictOrNullCompat kotlin.comparisons  toIntOrNull kotlin.comparisons  toList kotlin.comparisons  toRegex kotlin.comparisons  toString kotlin.comparisons  toTypedArray kotlin.comparisons  trim kotlin.comparisons  
trimIndent kotlin.comparisons  unixifyPath kotlin.comparisons  use kotlin.comparisons  windowsAwareBashCommandLine kotlin.comparisons  windowsAwareCommandLine kotlin.comparisons  with kotlin.comparisons  Action 	kotlin.io  AndroidComponentsExtension 	kotlin.io  BUILD_SCRIPT_PATH 	kotlin.io  Boolean 	kotlin.io  BundleHermesCTask 	kotlin.io  	Character 	kotlin.io  DEFAULT_DEV_SERVER_PORT 	kotlin.io  !DEFAULT_INTERNAL_PUBLISHING_GROUP 	kotlin.io  DocumentBuilderFactory 	kotlin.io  DuplicatesStrategy 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  GenerateCodegenArtifactsTask 	kotlin.io  GenerateCodegenSchemaTask 	kotlin.io  Gson 	kotlin.io  HERMES_ENABLED 	kotlin.io  HERMES_FALLBACK 	kotlin.io  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT 	kotlin.io  INTERNAL_PUBLISHING_GROUP 	kotlin.io  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO 	kotlin.io  INTERNAL_USE_HERMES_NIGHTLY 	kotlin.io  INTERNAL_VERSION_NAME 	kotlin.io  JavaVersion 	kotlin.io  	JsonUtils 	kotlin.io  Jvm 	kotlin.io  JvmName 	kotlin.io  KotlinTopLevelExtension 	kotlin.io  LibraryExtension 	kotlin.io  Locale 	kotlin.io  ModelPackageJson 	kotlin.io  NEW_ARCH_ENABLED 	kotlin.io  Pair 	kotlin.io  PrivateReactExtension 	kotlin.io  
Properties 	kotlin.io  REACT_NATIVE_ARCHITECTURES 	kotlin.io  ReactExtension 	kotlin.io  
ReplaceTokens 	kotlin.io  Runtime 	kotlin.io  SCOPED_HERMES_ENABLED 	kotlin.io  SCOPED_NEW_ARCH_ENABLED 	kotlin.io  !SCOPED_REACT_NATIVE_ARCHITECTURES 	kotlin.io  String 	kotlin.io  System 	kotlin.io  Task 	kotlin.io  Triple 	kotlin.io  URI 	kotlin.io  any 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  boostVersion 	kotlin.io  bufferedReader 	kotlin.io  
bundleCommand 	kotlin.io  bundleConfig 	kotlin.io  capitalizeCompat 	kotlin.io  cliFile 	kotlin.io  cliPath 	kotlin.io  &configureBackwardCompatibilityReactMap 	kotlin.io   configureBuildConfigFieldsForApp 	kotlin.io  &configureBuildConfigFieldsForLibraries 	kotlin.io  configureDependencies 	kotlin.io  configureDevPorts 	kotlin.io  configureJavaToolChains 	kotlin.io  !configureJsEnginePackagingOptions 	kotlin.io  configureNamespaceForLibraries 	kotlin.io   configureNewArchPackagingOptions 	kotlin.io  configureReactNativeNdk 	kotlin.io  configureReactTasks 	kotlin.io  configureRepositories 	kotlin.io  contains 	kotlin.io  copyTo 	kotlin.io  deleteRecursively 	kotlin.io  	dependsOn 	kotlin.io  detectOSAwareHermesCommand 	kotlin.io  detectedCliFile 	kotlin.io  detectedEntryFile 	kotlin.io  
devEnabled 	kotlin.io  
emptyArray 	kotlin.io  	emptyList 	kotlin.io  	entryFile 	kotlin.io  equals 	kotlin.io  error 	kotlin.io  exitProcess 	kotlin.io  extraPackagerArgs 	kotlin.io  filter 	kotlin.io  findPackageJsonFile 	kotlin.io  forEach 	kotlin.io  getPackageNameFromManifest 	kotlin.io  getReactNativeArchitectures 	kotlin.io  hasPropertySetToFalse 	kotlin.io  inputStream 	kotlin.io  isBlank 	kotlin.io  isNewArchEnabled 	kotlin.io  
isNotBlank 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrBlank 	kotlin.io  
isNullOrEmpty 	kotlin.io  	isWindows 	kotlin.io  java 	kotlin.io  joinToString 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  lowercaseCompat 	kotlin.io  mapOf 	kotlin.io  
minifyEnabled 	kotlin.io  moveTo 	kotlin.io  
mutableListOf 	kotlin.io  needsCodegenFromPackageJson 	kotlin.io  nodeExecutableAndArgs 	kotlin.io  none 	kotlin.io  orEmpty 	kotlin.io  plus 	kotlin.io  projectPathToLibraryName 	kotlin.io  reactNativeDir 	kotlin.io  readPackageJsonFile 	kotlin.io  readText 	kotlin.io  readVersionAndGroupStrings 	kotlin.io  
relativeTo 	kotlin.io  removeSuffix 	kotlin.io  replace 	kotlin.io  resourcesDir 	kotlin.io  runCatching 	kotlin.io  (shouldEnableNewArchForReactNativeVersion 	kotlin.io  &shouldWarnIfNewArchFlagIsSetInPrealpha 	kotlin.io  split 	kotlin.io  
startsWith 	kotlin.io  	substring 	kotlin.io  takeIf 	kotlin.io  to 	kotlin.io  	toBoolean 	kotlin.io  toBooleanStrictOrNullCompat 	kotlin.io  toIntOrNull 	kotlin.io  toList 	kotlin.io  toRegex 	kotlin.io  toString 	kotlin.io  toTypedArray 	kotlin.io  trim 	kotlin.io  
trimIndent 	kotlin.io  unixifyPath 	kotlin.io  use 	kotlin.io  windowsAwareBashCommandLine 	kotlin.io  windowsAwareCommandLine 	kotlin.io  with 	kotlin.io  Action 
kotlin.jvm  AndroidComponentsExtension 
kotlin.jvm  BUILD_SCRIPT_PATH 
kotlin.jvm  Boolean 
kotlin.jvm  BundleHermesCTask 
kotlin.jvm  	Character 
kotlin.jvm  DEFAULT_DEV_SERVER_PORT 
kotlin.jvm  !DEFAULT_INTERNAL_PUBLISHING_GROUP 
kotlin.jvm  DocumentBuilderFactory 
kotlin.jvm  DuplicatesStrategy 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  GenerateCodegenArtifactsTask 
kotlin.jvm  GenerateCodegenSchemaTask 
kotlin.jvm  Gson 
kotlin.jvm  HERMES_ENABLED 
kotlin.jvm  HERMES_FALLBACK 
kotlin.jvm  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT 
kotlin.jvm  INTERNAL_PUBLISHING_GROUP 
kotlin.jvm  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO 
kotlin.jvm  INTERNAL_USE_HERMES_NIGHTLY 
kotlin.jvm  INTERNAL_VERSION_NAME 
kotlin.jvm  JavaVersion 
kotlin.jvm  	JsonUtils 
kotlin.jvm  Jvm 
kotlin.jvm  JvmName 
kotlin.jvm  KotlinTopLevelExtension 
kotlin.jvm  LibraryExtension 
kotlin.jvm  Locale 
kotlin.jvm  ModelPackageJson 
kotlin.jvm  NEW_ARCH_ENABLED 
kotlin.jvm  Pair 
kotlin.jvm  PrivateReactExtension 
kotlin.jvm  
Properties 
kotlin.jvm  REACT_NATIVE_ARCHITECTURES 
kotlin.jvm  ReactExtension 
kotlin.jvm  
ReplaceTokens 
kotlin.jvm  Runtime 
kotlin.jvm  SCOPED_HERMES_ENABLED 
kotlin.jvm  SCOPED_NEW_ARCH_ENABLED 
kotlin.jvm  !SCOPED_REACT_NATIVE_ARCHITECTURES 
kotlin.jvm  String 
kotlin.jvm  System 
kotlin.jvm  Task 
kotlin.jvm  Triple 
kotlin.jvm  URI 
kotlin.jvm  any 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  boostVersion 
kotlin.jvm  bufferedReader 
kotlin.jvm  
bundleCommand 
kotlin.jvm  bundleConfig 
kotlin.jvm  capitalizeCompat 
kotlin.jvm  cliFile 
kotlin.jvm  cliPath 
kotlin.jvm  &configureBackwardCompatibilityReactMap 
kotlin.jvm   configureBuildConfigFieldsForApp 
kotlin.jvm  &configureBuildConfigFieldsForLibraries 
kotlin.jvm  configureDependencies 
kotlin.jvm  configureDevPorts 
kotlin.jvm  configureJavaToolChains 
kotlin.jvm  !configureJsEnginePackagingOptions 
kotlin.jvm  configureNamespaceForLibraries 
kotlin.jvm   configureNewArchPackagingOptions 
kotlin.jvm  configureReactNativeNdk 
kotlin.jvm  configureReactTasks 
kotlin.jvm  configureRepositories 
kotlin.jvm  contains 
kotlin.jvm  copyTo 
kotlin.jvm  deleteRecursively 
kotlin.jvm  	dependsOn 
kotlin.jvm  detectOSAwareHermesCommand 
kotlin.jvm  detectedCliFile 
kotlin.jvm  detectedEntryFile 
kotlin.jvm  
devEnabled 
kotlin.jvm  
emptyArray 
kotlin.jvm  	emptyList 
kotlin.jvm  	entryFile 
kotlin.jvm  equals 
kotlin.jvm  error 
kotlin.jvm  exitProcess 
kotlin.jvm  extraPackagerArgs 
kotlin.jvm  filter 
kotlin.jvm  findPackageJsonFile 
kotlin.jvm  forEach 
kotlin.jvm  getPackageNameFromManifest 
kotlin.jvm  getReactNativeArchitectures 
kotlin.jvm  hasPropertySetToFalse 
kotlin.jvm  inputStream 
kotlin.jvm  isBlank 
kotlin.jvm  isNewArchEnabled 
kotlin.jvm  
isNotBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrBlank 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  	isWindows 
kotlin.jvm  java 
kotlin.jvm  joinToString 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  lowercaseCompat 
kotlin.jvm  mapOf 
kotlin.jvm  
minifyEnabled 
kotlin.jvm  moveTo 
kotlin.jvm  
mutableListOf 
kotlin.jvm  needsCodegenFromPackageJson 
kotlin.jvm  nodeExecutableAndArgs 
kotlin.jvm  none 
kotlin.jvm  orEmpty 
kotlin.jvm  plus 
kotlin.jvm  projectPathToLibraryName 
kotlin.jvm  reactNativeDir 
kotlin.jvm  readPackageJsonFile 
kotlin.jvm  readText 
kotlin.jvm  readVersionAndGroupStrings 
kotlin.jvm  
relativeTo 
kotlin.jvm  removeSuffix 
kotlin.jvm  replace 
kotlin.jvm  resourcesDir 
kotlin.jvm  runCatching 
kotlin.jvm  (shouldEnableNewArchForReactNativeVersion 
kotlin.jvm  &shouldWarnIfNewArchFlagIsSetInPrealpha 
kotlin.jvm  split 
kotlin.jvm  
startsWith 
kotlin.jvm  	substring 
kotlin.jvm  takeIf 
kotlin.jvm  to 
kotlin.jvm  	toBoolean 
kotlin.jvm  toBooleanStrictOrNullCompat 
kotlin.jvm  toIntOrNull 
kotlin.jvm  toList 
kotlin.jvm  toRegex 
kotlin.jvm  toString 
kotlin.jvm  toTypedArray 
kotlin.jvm  trim 
kotlin.jvm  
trimIndent 
kotlin.jvm  unixifyPath 
kotlin.jvm  use 
kotlin.jvm  windowsAwareBashCommandLine 
kotlin.jvm  windowsAwareCommandLine 
kotlin.jvm  with 
kotlin.jvm  Action 
kotlin.ranges  AndroidComponentsExtension 
kotlin.ranges  BUILD_SCRIPT_PATH 
kotlin.ranges  Boolean 
kotlin.ranges  BundleHermesCTask 
kotlin.ranges  	Character 
kotlin.ranges  DEFAULT_DEV_SERVER_PORT 
kotlin.ranges  !DEFAULT_INTERNAL_PUBLISHING_GROUP 
kotlin.ranges  DocumentBuilderFactory 
kotlin.ranges  DuplicatesStrategy 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  GenerateCodegenArtifactsTask 
kotlin.ranges  GenerateCodegenSchemaTask 
kotlin.ranges  Gson 
kotlin.ranges  HERMES_ENABLED 
kotlin.ranges  HERMES_FALLBACK 
kotlin.ranges  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT 
kotlin.ranges  INTERNAL_PUBLISHING_GROUP 
kotlin.ranges  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO 
kotlin.ranges  INTERNAL_USE_HERMES_NIGHTLY 
kotlin.ranges  INTERNAL_VERSION_NAME 
kotlin.ranges  JavaVersion 
kotlin.ranges  	JsonUtils 
kotlin.ranges  Jvm 
kotlin.ranges  JvmName 
kotlin.ranges  KotlinTopLevelExtension 
kotlin.ranges  LibraryExtension 
kotlin.ranges  Locale 
kotlin.ranges  ModelPackageJson 
kotlin.ranges  NEW_ARCH_ENABLED 
kotlin.ranges  Pair 
kotlin.ranges  PrivateReactExtension 
kotlin.ranges  
Properties 
kotlin.ranges  REACT_NATIVE_ARCHITECTURES 
kotlin.ranges  ReactExtension 
kotlin.ranges  
ReplaceTokens 
kotlin.ranges  Runtime 
kotlin.ranges  SCOPED_HERMES_ENABLED 
kotlin.ranges  SCOPED_NEW_ARCH_ENABLED 
kotlin.ranges  !SCOPED_REACT_NATIVE_ARCHITECTURES 
kotlin.ranges  String 
kotlin.ranges  System 
kotlin.ranges  Task 
kotlin.ranges  Triple 
kotlin.ranges  URI 
kotlin.ranges  any 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  boostVersion 
kotlin.ranges  bufferedReader 
kotlin.ranges  
bundleCommand 
kotlin.ranges  bundleConfig 
kotlin.ranges  capitalizeCompat 
kotlin.ranges  cliFile 
kotlin.ranges  cliPath 
kotlin.ranges  &configureBackwardCompatibilityReactMap 
kotlin.ranges   configureBuildConfigFieldsForApp 
kotlin.ranges  &configureBuildConfigFieldsForLibraries 
kotlin.ranges  configureDependencies 
kotlin.ranges  configureDevPorts 
kotlin.ranges  configureJavaToolChains 
kotlin.ranges  !configureJsEnginePackagingOptions 
kotlin.ranges  configureNamespaceForLibraries 
kotlin.ranges   configureNewArchPackagingOptions 
kotlin.ranges  configureReactNativeNdk 
kotlin.ranges  configureReactTasks 
kotlin.ranges  configureRepositories 
kotlin.ranges  contains 
kotlin.ranges  copyTo 
kotlin.ranges  deleteRecursively 
kotlin.ranges  	dependsOn 
kotlin.ranges  detectOSAwareHermesCommand 
kotlin.ranges  detectedCliFile 
kotlin.ranges  detectedEntryFile 
kotlin.ranges  
devEnabled 
kotlin.ranges  
emptyArray 
kotlin.ranges  	emptyList 
kotlin.ranges  	entryFile 
kotlin.ranges  equals 
kotlin.ranges  error 
kotlin.ranges  exitProcess 
kotlin.ranges  extraPackagerArgs 
kotlin.ranges  filter 
kotlin.ranges  findPackageJsonFile 
kotlin.ranges  forEach 
kotlin.ranges  getPackageNameFromManifest 
kotlin.ranges  getReactNativeArchitectures 
kotlin.ranges  hasPropertySetToFalse 
kotlin.ranges  inputStream 
kotlin.ranges  isBlank 
kotlin.ranges  isNewArchEnabled 
kotlin.ranges  
isNotBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrBlank 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  	isWindows 
kotlin.ranges  java 
kotlin.ranges  joinToString 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  lowercaseCompat 
kotlin.ranges  mapOf 
kotlin.ranges  
minifyEnabled 
kotlin.ranges  moveTo 
kotlin.ranges  
mutableListOf 
kotlin.ranges  needsCodegenFromPackageJson 
kotlin.ranges  nodeExecutableAndArgs 
kotlin.ranges  none 
kotlin.ranges  orEmpty 
kotlin.ranges  plus 
kotlin.ranges  projectPathToLibraryName 
kotlin.ranges  reactNativeDir 
kotlin.ranges  readPackageJsonFile 
kotlin.ranges  readText 
kotlin.ranges  readVersionAndGroupStrings 
kotlin.ranges  
relativeTo 
kotlin.ranges  removeSuffix 
kotlin.ranges  replace 
kotlin.ranges  resourcesDir 
kotlin.ranges  runCatching 
kotlin.ranges  (shouldEnableNewArchForReactNativeVersion 
kotlin.ranges  &shouldWarnIfNewArchFlagIsSetInPrealpha 
kotlin.ranges  split 
kotlin.ranges  
startsWith 
kotlin.ranges  	substring 
kotlin.ranges  takeIf 
kotlin.ranges  to 
kotlin.ranges  	toBoolean 
kotlin.ranges  toBooleanStrictOrNullCompat 
kotlin.ranges  toIntOrNull 
kotlin.ranges  toList 
kotlin.ranges  toRegex 
kotlin.ranges  toString 
kotlin.ranges  toTypedArray 
kotlin.ranges  trim 
kotlin.ranges  
trimIndent 
kotlin.ranges  unixifyPath 
kotlin.ranges  use 
kotlin.ranges  windowsAwareBashCommandLine 
kotlin.ranges  windowsAwareCommandLine 
kotlin.ranges  with 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  Action kotlin.sequences  AndroidComponentsExtension kotlin.sequences  BUILD_SCRIPT_PATH kotlin.sequences  Boolean kotlin.sequences  BundleHermesCTask kotlin.sequences  	Character kotlin.sequences  DEFAULT_DEV_SERVER_PORT kotlin.sequences  !DEFAULT_INTERNAL_PUBLISHING_GROUP kotlin.sequences  DocumentBuilderFactory kotlin.sequences  DuplicatesStrategy kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  GenerateCodegenArtifactsTask kotlin.sequences  GenerateCodegenSchemaTask kotlin.sequences  Gson kotlin.sequences  HERMES_ENABLED kotlin.sequences  HERMES_FALLBACK kotlin.sequences  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT kotlin.sequences  INTERNAL_PUBLISHING_GROUP kotlin.sequences  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO kotlin.sequences  INTERNAL_USE_HERMES_NIGHTLY kotlin.sequences  INTERNAL_VERSION_NAME kotlin.sequences  JavaVersion kotlin.sequences  	JsonUtils kotlin.sequences  Jvm kotlin.sequences  JvmName kotlin.sequences  KotlinTopLevelExtension kotlin.sequences  LibraryExtension kotlin.sequences  Locale kotlin.sequences  ModelPackageJson kotlin.sequences  NEW_ARCH_ENABLED kotlin.sequences  Pair kotlin.sequences  PrivateReactExtension kotlin.sequences  
Properties kotlin.sequences  REACT_NATIVE_ARCHITECTURES kotlin.sequences  ReactExtension kotlin.sequences  
ReplaceTokens kotlin.sequences  Runtime kotlin.sequences  SCOPED_HERMES_ENABLED kotlin.sequences  SCOPED_NEW_ARCH_ENABLED kotlin.sequences  !SCOPED_REACT_NATIVE_ARCHITECTURES kotlin.sequences  String kotlin.sequences  System kotlin.sequences  Task kotlin.sequences  Triple kotlin.sequences  URI kotlin.sequences  any kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  boostVersion kotlin.sequences  bufferedReader kotlin.sequences  
bundleCommand kotlin.sequences  bundleConfig kotlin.sequences  capitalizeCompat kotlin.sequences  cliFile kotlin.sequences  cliPath kotlin.sequences  &configureBackwardCompatibilityReactMap kotlin.sequences   configureBuildConfigFieldsForApp kotlin.sequences  &configureBuildConfigFieldsForLibraries kotlin.sequences  configureDependencies kotlin.sequences  configureDevPorts kotlin.sequences  configureJavaToolChains kotlin.sequences  !configureJsEnginePackagingOptions kotlin.sequences  configureNamespaceForLibraries kotlin.sequences   configureNewArchPackagingOptions kotlin.sequences  configureReactNativeNdk kotlin.sequences  configureReactTasks kotlin.sequences  configureRepositories kotlin.sequences  contains kotlin.sequences  copyTo kotlin.sequences  deleteRecursively kotlin.sequences  	dependsOn kotlin.sequences  detectOSAwareHermesCommand kotlin.sequences  detectedCliFile kotlin.sequences  detectedEntryFile kotlin.sequences  
devEnabled kotlin.sequences  
emptyArray kotlin.sequences  	emptyList kotlin.sequences  	entryFile kotlin.sequences  equals kotlin.sequences  error kotlin.sequences  exitProcess kotlin.sequences  extraPackagerArgs kotlin.sequences  filter kotlin.sequences  findPackageJsonFile kotlin.sequences  forEach kotlin.sequences  getPackageNameFromManifest kotlin.sequences  getReactNativeArchitectures kotlin.sequences  hasPropertySetToFalse kotlin.sequences  inputStream kotlin.sequences  isBlank kotlin.sequences  isNewArchEnabled kotlin.sequences  
isNotBlank kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrBlank kotlin.sequences  
isNullOrEmpty kotlin.sequences  	isWindows kotlin.sequences  java kotlin.sequences  joinToString kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  lowercaseCompat kotlin.sequences  mapOf kotlin.sequences  
minifyEnabled kotlin.sequences  moveTo kotlin.sequences  
mutableListOf kotlin.sequences  needsCodegenFromPackageJson kotlin.sequences  nodeExecutableAndArgs kotlin.sequences  none kotlin.sequences  orEmpty kotlin.sequences  plus kotlin.sequences  projectPathToLibraryName kotlin.sequences  reactNativeDir kotlin.sequences  readPackageJsonFile kotlin.sequences  readText kotlin.sequences  readVersionAndGroupStrings kotlin.sequences  
relativeTo kotlin.sequences  removeSuffix kotlin.sequences  replace kotlin.sequences  resourcesDir kotlin.sequences  runCatching kotlin.sequences  (shouldEnableNewArchForReactNativeVersion kotlin.sequences  &shouldWarnIfNewArchFlagIsSetInPrealpha kotlin.sequences  split kotlin.sequences  
startsWith kotlin.sequences  	substring kotlin.sequences  takeIf kotlin.sequences  to kotlin.sequences  	toBoolean kotlin.sequences  toBooleanStrictOrNullCompat kotlin.sequences  toIntOrNull kotlin.sequences  toList kotlin.sequences  toRegex kotlin.sequences  toString kotlin.sequences  toTypedArray kotlin.sequences  trim kotlin.sequences  
trimIndent kotlin.sequences  unixifyPath kotlin.sequences  use kotlin.sequences  windowsAwareBashCommandLine kotlin.sequences  windowsAwareCommandLine kotlin.sequences  with kotlin.sequences  exitProcess 
kotlin.system  Action kotlin.text  AndroidComponentsExtension kotlin.text  BUILD_SCRIPT_PATH kotlin.text  Boolean kotlin.text  BundleHermesCTask kotlin.text  	Character kotlin.text  DEFAULT_DEV_SERVER_PORT kotlin.text  !DEFAULT_INTERNAL_PUBLISHING_GROUP kotlin.text  DocumentBuilderFactory kotlin.text  DuplicatesStrategy kotlin.text  	Exception kotlin.text  File kotlin.text  GenerateCodegenArtifactsTask kotlin.text  GenerateCodegenSchemaTask kotlin.text  Gson kotlin.text  HERMES_ENABLED kotlin.text  HERMES_FALLBACK kotlin.text  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT kotlin.text  INTERNAL_PUBLISHING_GROUP kotlin.text  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO kotlin.text  INTERNAL_USE_HERMES_NIGHTLY kotlin.text  INTERNAL_VERSION_NAME kotlin.text  JavaVersion kotlin.text  	JsonUtils kotlin.text  Jvm kotlin.text  JvmName kotlin.text  KotlinTopLevelExtension kotlin.text  LibraryExtension kotlin.text  Locale kotlin.text  MatchResult kotlin.text  ModelPackageJson kotlin.text  NEW_ARCH_ENABLED kotlin.text  Pair kotlin.text  PrivateReactExtension kotlin.text  
Properties kotlin.text  REACT_NATIVE_ARCHITECTURES kotlin.text  ReactExtension kotlin.text  Regex kotlin.text  
ReplaceTokens kotlin.text  Runtime kotlin.text  SCOPED_HERMES_ENABLED kotlin.text  SCOPED_NEW_ARCH_ENABLED kotlin.text  !SCOPED_REACT_NATIVE_ARCHITECTURES kotlin.text  String kotlin.text  System kotlin.text  Task kotlin.text  Triple kotlin.text  URI kotlin.text  any kotlin.text  apply kotlin.text  arrayOf kotlin.text  boostVersion kotlin.text  bufferedReader kotlin.text  
bundleCommand kotlin.text  bundleConfig kotlin.text  capitalizeCompat kotlin.text  cliFile kotlin.text  cliPath kotlin.text  &configureBackwardCompatibilityReactMap kotlin.text   configureBuildConfigFieldsForApp kotlin.text  &configureBuildConfigFieldsForLibraries kotlin.text  configureDependencies kotlin.text  configureDevPorts kotlin.text  configureJavaToolChains kotlin.text  !configureJsEnginePackagingOptions kotlin.text  configureNamespaceForLibraries kotlin.text   configureNewArchPackagingOptions kotlin.text  configureReactNativeNdk kotlin.text  configureReactTasks kotlin.text  configureRepositories kotlin.text  contains kotlin.text  copyTo kotlin.text  deleteRecursively kotlin.text  	dependsOn kotlin.text  detectOSAwareHermesCommand kotlin.text  detectedCliFile kotlin.text  detectedEntryFile kotlin.text  
devEnabled kotlin.text  
emptyArray kotlin.text  	emptyList kotlin.text  	entryFile kotlin.text  equals kotlin.text  error kotlin.text  exitProcess kotlin.text  extraPackagerArgs kotlin.text  filter kotlin.text  findPackageJsonFile kotlin.text  forEach kotlin.text  getPackageNameFromManifest kotlin.text  getReactNativeArchitectures kotlin.text  hasPropertySetToFalse kotlin.text  inputStream kotlin.text  isBlank kotlin.text  isNewArchEnabled kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  
isNullOrEmpty kotlin.text  	isWindows kotlin.text  java kotlin.text  joinToString kotlin.text  let kotlin.text  listOf kotlin.text  lowercaseCompat kotlin.text  mapOf kotlin.text  
minifyEnabled kotlin.text  moveTo kotlin.text  
mutableListOf kotlin.text  needsCodegenFromPackageJson kotlin.text  nodeExecutableAndArgs kotlin.text  none kotlin.text  orEmpty kotlin.text  plus kotlin.text  projectPathToLibraryName kotlin.text  reactNativeDir kotlin.text  readPackageJsonFile kotlin.text  readText kotlin.text  readVersionAndGroupStrings kotlin.text  
relativeTo kotlin.text  removeSuffix kotlin.text  replace kotlin.text  resourcesDir kotlin.text  runCatching kotlin.text  (shouldEnableNewArchForReactNativeVersion kotlin.text  &shouldWarnIfNewArchFlagIsSetInPrealpha kotlin.text  split kotlin.text  
startsWith kotlin.text  	substring kotlin.text  takeIf kotlin.text  to kotlin.text  	toBoolean kotlin.text  toBooleanStrictOrNullCompat kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  toRegex kotlin.text  toString kotlin.text  toTypedArray kotlin.text  trim kotlin.text  
trimIndent kotlin.text  unixifyPath kotlin.text  use kotlin.text  windowsAwareBashCommandLine kotlin.text  windowsAwareCommandLine kotlin.text  with kotlin.text  equals kotlin.text.MatchResult  groupValues kotlin.text.MatchResult  find kotlin.text.Regex  
ReplaceTokens org.apache.tools.ant.filters  Action org.gradle.api  DefaultTask org.gradle.api  JavaVersion org.gradle.api  Plugin org.gradle.api  Project org.gradle.api  Task org.gradle.api  <SAM-CONSTRUCTOR> org.gradle.api.Action  Any org.gradle.api.DefaultTask  BUILD_SCRIPT_PATH org.gradle.api.DefaultTask  Boolean org.gradle.api.DefaultTask  ConfigurableFileCollection org.gradle.api.DefaultTask  ConfigurableFileTree org.gradle.api.DefaultTask  	Directory org.gradle.api.DefaultTask  DirectoryProperty org.gradle.api.DefaultTask  DuplicatesStrategy org.gradle.api.DefaultTask  File org.gradle.api.DefaultTask  FileSystemOperations org.gradle.api.DefaultTask  FileTree org.gradle.api.DefaultTask  Inject org.gradle.api.DefaultTask  Input org.gradle.api.DefaultTask  	InputFile org.gradle.api.DefaultTask  
InputFiles org.gradle.api.DefaultTask  Internal org.gradle.api.DefaultTask  	JsonUtils org.gradle.api.DefaultTask  List org.gradle.api.DefaultTask  ListProperty org.gradle.api.DefaultTask  Optional org.gradle.api.DefaultTask  OutputDirectory org.gradle.api.DefaultTask  
OutputFile org.gradle.api.DefaultTask  OutputFiles org.gradle.api.DefaultTask  Pair org.gradle.api.DefaultTask  PrefabPreprocessingEntry org.gradle.api.DefaultTask  Property org.gradle.api.DefaultTask  Provider org.gradle.api.DefaultTask  RegularFile org.gradle.api.DefaultTask  RegularFileProperty org.gradle.api.DefaultTask  
ReplaceTokens org.gradle.api.DefaultTask  String org.gradle.api.DefaultTask  
TaskAction org.gradle.api.DefaultTask  apply org.gradle.api.DefaultTask  boostVersion org.gradle.api.DefaultTask  
bundleCommand org.gradle.api.DefaultTask  bundleConfig org.gradle.api.DefaultTask  cliFile org.gradle.api.DefaultTask  cliPath org.gradle.api.DefaultTask  commandLine org.gradle.api.DefaultTask  deleteRecursively org.gradle.api.DefaultTask  	dependsOn org.gradle.api.DefaultTask  detectOSAwareHermesCommand org.gradle.api.DefaultTask  
devEnabled org.gradle.api.DefaultTask  	entryFile org.gradle.api.DefaultTask  error org.gradle.api.DefaultTask  exec org.gradle.api.DefaultTask  extraPackagerArgs org.gradle.api.DefaultTask  getBundleCommand org.gradle.api.DefaultTask  getComposeSourceMapsCommand org.gradle.api.DefaultTask  getHermescCommand org.gradle.api.DefaultTask  java org.gradle.api.DefaultTask  jsBundleDir org.gradle.api.DefaultTask  mapOf org.gradle.api.DefaultTask  
minifyEnabled org.gradle.api.DefaultTask  moveTo org.gradle.api.DefaultTask  
mutableListOf org.gradle.api.DefaultTask  nodeExecutableAndArgs org.gradle.api.DefaultTask  onlyIf org.gradle.api.DefaultTask  removeSuffix org.gradle.api.DefaultTask  resolveCompilerSourceMap org.gradle.api.DefaultTask  resolveOutputSourceMap org.gradle.api.DefaultTask  resolvePackagerSourceMapFile org.gradle.api.DefaultTask  resolveTaskParameters org.gradle.api.DefaultTask  resourcesDir org.gradle.api.DefaultTask  
runCommand org.gradle.api.DefaultTask  setupCommandLine org.gradle.api.DefaultTask  to org.gradle.api.DefaultTask  toTypedArray org.gradle.api.DefaultTask  unixifyPath org.gradle.api.DefaultTask  windowsAwareBashCommandLine org.gradle.api.DefaultTask  windowsAwareCommandLine org.gradle.api.DefaultTask  
wipeOutputDir org.gradle.api.DefaultTask  
VERSION_17 org.gradle.api.JavaVersion  getMAJORVersion org.gradle.api.JavaVersion  getMajorVersion org.gradle.api.JavaVersion  majorVersion org.gradle.api.JavaVersion  setMajorVersion org.gradle.api.JavaVersion  	getByName )org.gradle.api.NamedDomainObjectContainer  BundleHermesCTask org.gradle.api.Project  File org.gradle.api.Project  HERMES_ENABLED org.gradle.api.Project  HERMES_FALLBACK org.gradle.api.Project  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO org.gradle.api.Project  NEW_ARCH_ENABLED org.gradle.api.Project  REACT_NATIVE_ARCHITECTURES org.gradle.api.Project  SCOPED_HERMES_ENABLED org.gradle.api.Project  SCOPED_NEW_ARCH_ENABLED org.gradle.api.Project  !SCOPED_REACT_NATIVE_ARCHITECTURES org.gradle.api.Project  System org.gradle.api.Project  URI org.gradle.api.Project  
afterEvaluate org.gradle.api.Project  allprojects org.gradle.api.Project  any org.gradle.api.Project  capitalizeCompat org.gradle.api.Project  configurations org.gradle.api.Project  !configureJsEnginePackagingOptions org.gradle.api.Project   configureNewArchPackagingOptions org.gradle.api.Project  configureReactTasks org.gradle.api.Project  copy org.gradle.api.Project  detectedCliFile org.gradle.api.Project  detectedEntryFile org.gradle.api.Project  equals org.gradle.api.Project  evaluationDependsOn org.gradle.api.Project  exec org.gradle.api.Project  
extensions org.gradle.api.Project  file org.gradle.api.Project  fileTree org.gradle.api.Project  filter org.gradle.api.Project  findProperty org.gradle.api.Project  getANY org.gradle.api.Project  getAny org.gradle.api.Project  getCAPITALIZECompat org.gradle.api.Project  getCONFIGURATIONS org.gradle.api.Project  $getCONFIGUREJsEnginePackagingOptions org.gradle.api.Project  #getCONFIGURENewArchPackagingOptions org.gradle.api.Project  getCONFIGUREReactTasks org.gradle.api.Project  getCapitalizeCompat org.gradle.api.Project  getConfigurations org.gradle.api.Project  $getConfigureJsEnginePackagingOptions org.gradle.api.Project  #getConfigureNewArchPackagingOptions org.gradle.api.Project  getConfigureReactTasks org.gradle.api.Project  getDETECTEDCliFile org.gradle.api.Project  getDETECTEDEntryFile org.gradle.api.Project  getDetectedCliFile org.gradle.api.Project  getDetectedEntryFile org.gradle.api.Project  	getEQUALS org.gradle.api.Project  
getEXTENSIONS org.gradle.api.Project  	getEquals org.gradle.api.Project  
getExtensions org.gradle.api.Project  	getFILTER org.gradle.api.Project  	getFilter org.gradle.api.Project  getGETReactNativeArchitectures org.gradle.api.Project  getGetReactNativeArchitectures org.gradle.api.Project  getHASPropertySetToFalse org.gradle.api.Project  getHasPropertySetToFalse org.gradle.api.Project  getISHermesEnabled org.gradle.api.Project  getISNewArchEnabled org.gradle.api.Project  
getISNotBlank org.gradle.api.Project  
getISNotEmpty org.gradle.api.Project  getIsHermesEnabled org.gradle.api.Project  getIsNewArchEnabled org.gradle.api.Project  
getIsNotBlank org.gradle.api.Project  
getIsNotEmpty org.gradle.api.Project  	getLAYOUT org.gradle.api.Project  	getLOGGER org.gradle.api.Project  getLOWERCASECompat org.gradle.api.Project  	getLayout org.gradle.api.Project  	getLogger org.gradle.api.Project  getLowercaseCompat org.gradle.api.Project  getMAVENRepoFromURI org.gradle.api.Project  getMAVENRepoFromUrl org.gradle.api.Project  getMUTABLEListOf org.gradle.api.Project  getMavenRepoFromURI org.gradle.api.Project  getMavenRepoFromUrl org.gradle.api.Project  getMutableListOf org.gradle.api.Project  getNAME org.gradle.api.Project  getNEEDSCodegenFromPackageJson org.gradle.api.Project  getName org.gradle.api.Project  getNeedsCodegenFromPackageJson org.gradle.api.Project  
getOBJECTS org.gradle.api.Project  
getObjects org.gradle.api.Project  getPATH org.gradle.api.Project  getPLUGINManager org.gradle.api.Project  
getPROJECT org.gradle.api.Project  
getPROJECTDir org.gradle.api.Project  
getPROPERTIES org.gradle.api.Project  getPath org.gradle.api.Project  getPluginManager org.gradle.api.Project  
getProject org.gradle.api.Project  
getProjectDir org.gradle.api.Project  
getProperties org.gradle.api.Project  getREACTNativeDir org.gradle.api.Project  getREADPackageJsonFile org.gradle.api.Project  getREPOSITORIES org.gradle.api.Project  getROOTProject org.gradle.api.Project  getReactNativeArchitectures org.gradle.api.Project  getReactNativeDir org.gradle.api.Project  getReadPackageJsonFile org.gradle.api.Project  getRepositories org.gradle.api.Project  getRootProject org.gradle.api.Project  +getSHOULDEnableNewArchForReactNativeVersion org.gradle.api.Project  )getSHOULDWarnIfNewArchFlagIsSetInPrealpha org.gradle.api.Project  getSPLIT org.gradle.api.Project  +getShouldEnableNewArchForReactNativeVersion org.gradle.api.Project  )getShouldWarnIfNewArchFlagIsSetInPrealpha org.gradle.api.Project  getSplit org.gradle.api.Project  getTASKS org.gradle.api.Project  getTOBoolean org.gradle.api.Project  getTOBooleanStrictOrNullCompat org.gradle.api.Project  getTOString org.gradle.api.Project  getTasks org.gradle.api.Project  getToBoolean org.gradle.api.Project  getToBooleanStrictOrNullCompat org.gradle.api.Project  getToString org.gradle.api.Project  hasProperty org.gradle.api.Project  hasPropertySetToFalse org.gradle.api.Project  isHermesEnabled org.gradle.api.Project  isNewArchEnabled org.gradle.api.Project  
isNotBlank org.gradle.api.Project  
isNotEmpty org.gradle.api.Project  java org.gradle.api.Project  layout org.gradle.api.Project  logger org.gradle.api.Project  lowercaseCompat org.gradle.api.Project  mavenRepoFromURI org.gradle.api.Project  mavenRepoFromUrl org.gradle.api.Project  
mutableListOf org.gradle.api.Project  name org.gradle.api.Project  needsCodegenFromPackageJson org.gradle.api.Project  objects org.gradle.api.Project  path org.gradle.api.Project  
pluginManager org.gradle.api.Project  project org.gradle.api.Project  
projectDir org.gradle.api.Project  
properties org.gradle.api.Project  property org.gradle.api.Project  reactNativeDir org.gradle.api.Project  readPackageJsonFile org.gradle.api.Project  repositories org.gradle.api.Project  rootProject org.gradle.api.Project  setConfigurations org.gradle.api.Project  
setExtensions org.gradle.api.Project  	setLayout org.gradle.api.Project  	setLogger org.gradle.api.Project  setName org.gradle.api.Project  
setObjects org.gradle.api.Project  setPath org.gradle.api.Project  setPluginManager org.gradle.api.Project  
setProject org.gradle.api.Project  
setProjectDir org.gradle.api.Project  
setProperties org.gradle.api.Project  setRepositories org.gradle.api.Project  setRootProject org.gradle.api.Project  setTasks org.gradle.api.Project  (shouldEnableNewArchForReactNativeVersion org.gradle.api.Project  &shouldWarnIfNewArchFlagIsSetInPrealpha org.gradle.api.Project  split org.gradle.api.Project  subprojects org.gradle.api.Project  tasks org.gradle.api.Project  	toBoolean org.gradle.api.Project  toBooleanStrictOrNullCompat org.gradle.api.Project  toString org.gradle.api.Project  zipTree org.gradle.api.Project  
Configuration org.gradle.api.artifacts  DependencySubstitutions org.gradle.api.artifacts  ResolutionStrategy org.gradle.api.artifacts  getRESOLUTIONStrategy &org.gradle.api.artifacts.Configuration  getResolutionStrategy &org.gradle.api.artifacts.Configuration  resolutionStrategy &org.gradle.api.artifacts.Configuration  setResolutionStrategy &org.gradle.api.artifacts.Configuration  all /org.gradle.api.artifacts.ConfigurationContainer  Substitution 0org.gradle.api.artifacts.DependencySubstitutions  module 0org.gradle.api.artifacts.DependencySubstitutions  
substitute 0org.gradle.api.artifacts.DependencySubstitutions  because =org.gradle.api.artifacts.DependencySubstitutions.Substitution  using =org.gradle.api.artifacts.DependencySubstitutions.Substitution  dependencySubstitution +org.gradle.api.artifacts.ResolutionStrategy  force +org.gradle.api.artifacts.ResolutionStrategy  ComponentSelector "org.gradle.api.artifacts.component  RepositoryHandler org.gradle.api.artifacts.dsl  google .org.gradle.api.artifacts.dsl.RepositoryHandler  maven .org.gradle.api.artifacts.dsl.RepositoryHandler  mavenCentral .org.gradle.api.artifacts.dsl.RepositoryHandler  MavenArtifactRepository %org.gradle.api.artifacts.repositories  RepositoryContentDescriptor %org.gradle.api.artifacts.repositories  content =org.gradle.api.artifacts.repositories.MavenArtifactRepository  getURL =org.gradle.api.artifacts.repositories.MavenArtifactRepository  getUrl =org.gradle.api.artifacts.repositories.MavenArtifactRepository  setUrl =org.gradle.api.artifacts.repositories.MavenArtifactRepository  url =org.gradle.api.artifacts.repositories.MavenArtifactRepository  excludeGroup Aorg.gradle.api.artifacts.repositories.RepositoryContentDescriptor  
excludeModule Aorg.gradle.api.artifacts.repositories.RepositoryContentDescriptor  ConfigurableFileCollection org.gradle.api.file  ConfigurableFileTree org.gradle.api.file  ContentFilterable org.gradle.api.file  CopySpec org.gradle.api.file  	Directory org.gradle.api.file  DirectoryProperty org.gradle.api.file  DuplicatesStrategy org.gradle.api.file  FileCopyDetails org.gradle.api.file  FileSystemOperations org.gradle.api.file  FileTree org.gradle.api.file  
ProjectLayout org.gradle.api.file  RegularFile org.gradle.api.file  RegularFileProperty org.gradle.api.file  exclude (org.gradle.api.file.ConfigurableFileTree  include (org.gradle.api.file.ConfigurableFileTree  matching (org.gradle.api.file.ConfigurableFileTree  duplicatesStrategy org.gradle.api.file.CopySpec  eachFile org.gradle.api.file.CopySpec  exclude org.gradle.api.file.CopySpec  
filesMatching org.gradle.api.file.CopySpec  from org.gradle.api.file.CopySpec  getDUPLICATESStrategy org.gradle.api.file.CopySpec  getDuplicatesStrategy org.gradle.api.file.CopySpec  getINCLUDEEmptyDirs org.gradle.api.file.CopySpec  getIncludeEmptyDirs org.gradle.api.file.CopySpec  include org.gradle.api.file.CopySpec  includeEmptyDirs org.gradle.api.file.CopySpec  into org.gradle.api.file.CopySpec  setDuplicatesStrategy org.gradle.api.file.CopySpec  setIncludeEmptyDirs org.gradle.api.file.CopySpec  asFile org.gradle.api.file.Directory  dir org.gradle.api.file.Directory  	getASFile org.gradle.api.file.Directory  	getAsFile org.gradle.api.file.Directory  	setAsFile org.gradle.api.file.Directory  asFile %org.gradle.api.file.DirectoryProperty  
convention %org.gradle.api.file.DirectoryProperty  dir %org.gradle.api.file.DirectoryProperty  file %org.gradle.api.file.DirectoryProperty  get %org.gradle.api.file.DirectoryProperty  	getASFile %org.gradle.api.file.DirectoryProperty  	getAsFile %org.gradle.api.file.DirectoryProperty  set %org.gradle.api.file.DirectoryProperty  	setAsFile %org.gradle.api.file.DirectoryProperty  WARN &org.gradle.api.file.DuplicatesStrategy  filter #org.gradle.api.file.FileCopyDetails  getNAME #org.gradle.api.file.FileCopyDetails  getName #org.gradle.api.file.FileCopyDetails  getPATH #org.gradle.api.file.FileCopyDetails  getPath #org.gradle.api.file.FileCopyDetails  name #org.gradle.api.file.FileCopyDetails  path #org.gradle.api.file.FileCopyDetails  setName #org.gradle.api.file.FileCopyDetails  setPath #org.gradle.api.file.FileCopyDetails  copy (org.gradle.api.file.FileSystemOperations  
getSINGLEFile org.gradle.api.file.FileTree  
getSingleFile org.gradle.api.file.FileTree  matching org.gradle.api.file.FileTree  
setSingleFile org.gradle.api.file.FileTree  
singleFile org.gradle.api.file.FileTree  buildDirectory !org.gradle.api.file.ProjectLayout  getBUILDDirectory !org.gradle.api.file.ProjectLayout  getBuildDirectory !org.gradle.api.file.ProjectLayout  getPROJECTDirectory !org.gradle.api.file.ProjectLayout  getProjectDirectory !org.gradle.api.file.ProjectLayout  projectDirectory !org.gradle.api.file.ProjectLayout  setBuildDirectory !org.gradle.api.file.ProjectLayout  setProjectDirectory !org.gradle.api.file.ProjectLayout  asFile org.gradle.api.file.RegularFile  	getASFile org.gradle.api.file.RegularFile  	getAsFile org.gradle.api.file.RegularFile  	setAsFile org.gradle.api.file.RegularFile  asFile 'org.gradle.api.file.RegularFileProperty  
convention 'org.gradle.api.file.RegularFileProperty  get 'org.gradle.api.file.RegularFileProperty  	getASFile 'org.gradle.api.file.RegularFileProperty  	getAsFile 'org.gradle.api.file.RegularFileProperty  getISPresent 'org.gradle.api.file.RegularFileProperty  getIsPresent 'org.gradle.api.file.RegularFileProperty  	getORNull 'org.gradle.api.file.RegularFileProperty  	getOrNull 'org.gradle.api.file.RegularFileProperty  	isPresent 'org.gradle.api.file.RegularFileProperty  orNull 'org.gradle.api.file.RegularFileProperty  set 'org.gradle.api.file.RegularFileProperty  	setAsFile 'org.gradle.api.file.RegularFileProperty  	setOrNull 'org.gradle.api.file.RegularFileProperty  
setPresent 'org.gradle.api.file.RegularFileProperty  Any $org.gradle.api.internal.AbstractTask  BUILD_SCRIPT_PATH $org.gradle.api.internal.AbstractTask  Boolean $org.gradle.api.internal.AbstractTask  ConfigurableFileCollection $org.gradle.api.internal.AbstractTask  ConfigurableFileTree $org.gradle.api.internal.AbstractTask  	Directory $org.gradle.api.internal.AbstractTask  DirectoryProperty $org.gradle.api.internal.AbstractTask  DuplicatesStrategy $org.gradle.api.internal.AbstractTask  File $org.gradle.api.internal.AbstractTask  FileSystemOperations $org.gradle.api.internal.AbstractTask  FileTree $org.gradle.api.internal.AbstractTask  Inject $org.gradle.api.internal.AbstractTask  Input $org.gradle.api.internal.AbstractTask  	InputFile $org.gradle.api.internal.AbstractTask  
InputFiles $org.gradle.api.internal.AbstractTask  Internal $org.gradle.api.internal.AbstractTask  	JsonUtils $org.gradle.api.internal.AbstractTask  List $org.gradle.api.internal.AbstractTask  ListProperty $org.gradle.api.internal.AbstractTask  Optional $org.gradle.api.internal.AbstractTask  OutputDirectory $org.gradle.api.internal.AbstractTask  
OutputFile $org.gradle.api.internal.AbstractTask  OutputFiles $org.gradle.api.internal.AbstractTask  Pair $org.gradle.api.internal.AbstractTask  PrefabPreprocessingEntry $org.gradle.api.internal.AbstractTask  Property $org.gradle.api.internal.AbstractTask  Provider $org.gradle.api.internal.AbstractTask  RegularFile $org.gradle.api.internal.AbstractTask  RegularFileProperty $org.gradle.api.internal.AbstractTask  
ReplaceTokens $org.gradle.api.internal.AbstractTask  String $org.gradle.api.internal.AbstractTask  
TaskAction $org.gradle.api.internal.AbstractTask  apply $org.gradle.api.internal.AbstractTask  boostVersion $org.gradle.api.internal.AbstractTask  
bundleCommand $org.gradle.api.internal.AbstractTask  bundleConfig $org.gradle.api.internal.AbstractTask  cliFile $org.gradle.api.internal.AbstractTask  cliPath $org.gradle.api.internal.AbstractTask  commandLine $org.gradle.api.internal.AbstractTask  deleteRecursively $org.gradle.api.internal.AbstractTask  	dependsOn $org.gradle.api.internal.AbstractTask  detectOSAwareHermesCommand $org.gradle.api.internal.AbstractTask  
devEnabled $org.gradle.api.internal.AbstractTask  	entryFile $org.gradle.api.internal.AbstractTask  error $org.gradle.api.internal.AbstractTask  exec $org.gradle.api.internal.AbstractTask  extraPackagerArgs $org.gradle.api.internal.AbstractTask  getBundleCommand $org.gradle.api.internal.AbstractTask  getComposeSourceMapsCommand $org.gradle.api.internal.AbstractTask  getHermescCommand $org.gradle.api.internal.AbstractTask  java $org.gradle.api.internal.AbstractTask  jsBundleDir $org.gradle.api.internal.AbstractTask  mapOf $org.gradle.api.internal.AbstractTask  
minifyEnabled $org.gradle.api.internal.AbstractTask  moveTo $org.gradle.api.internal.AbstractTask  
mutableListOf $org.gradle.api.internal.AbstractTask  nodeExecutableAndArgs $org.gradle.api.internal.AbstractTask  onlyIf $org.gradle.api.internal.AbstractTask  removeSuffix $org.gradle.api.internal.AbstractTask  resolveCompilerSourceMap $org.gradle.api.internal.AbstractTask  resolveOutputSourceMap $org.gradle.api.internal.AbstractTask  resolvePackagerSourceMapFile $org.gradle.api.internal.AbstractTask  resolveTaskParameters $org.gradle.api.internal.AbstractTask  resourcesDir $org.gradle.api.internal.AbstractTask  
runCommand $org.gradle.api.internal.AbstractTask  setupCommandLine $org.gradle.api.internal.AbstractTask  to $org.gradle.api.internal.AbstractTask  toTypedArray $org.gradle.api.internal.AbstractTask  unixifyPath $org.gradle.api.internal.AbstractTask  windowsAwareBashCommandLine $org.gradle.api.internal.AbstractTask  windowsAwareCommandLine $org.gradle.api.internal.AbstractTask  
wipeOutputDir $org.gradle.api.internal.AbstractTask  BUILD_SCRIPT_PATH &org.gradle.api.internal.ConventionTask  	Directory &org.gradle.api.internal.ConventionTask  DirectoryProperty &org.gradle.api.internal.ConventionTask  FileTree &org.gradle.api.internal.ConventionTask  Input &org.gradle.api.internal.ConventionTask  	InputFile &org.gradle.api.internal.ConventionTask  
InputFiles &org.gradle.api.internal.ConventionTask  Internal &org.gradle.api.internal.ConventionTask  	JsonUtils &org.gradle.api.internal.ConventionTask  ListProperty &org.gradle.api.internal.ConventionTask  OutputDirectory &org.gradle.api.internal.ConventionTask  
OutputFile &org.gradle.api.internal.ConventionTask  OutputFiles &org.gradle.api.internal.ConventionTask  Pair &org.gradle.api.internal.ConventionTask  Property &org.gradle.api.internal.ConventionTask  Provider &org.gradle.api.internal.ConventionTask  RegularFile &org.gradle.api.internal.ConventionTask  RegularFileProperty &org.gradle.api.internal.ConventionTask  String &org.gradle.api.internal.ConventionTask  apply &org.gradle.api.internal.ConventionTask  cliPath &org.gradle.api.internal.ConventionTask  commandLine &org.gradle.api.internal.ConventionTask  deleteRecursively &org.gradle.api.internal.ConventionTask  	dependsOn &org.gradle.api.internal.ConventionTask  exec &org.gradle.api.internal.ConventionTask  onlyIf &org.gradle.api.internal.ConventionTask  resolveTaskParameters &org.gradle.api.internal.ConventionTask  setupCommandLine &org.gradle.api.internal.ConventionTask  to &org.gradle.api.internal.ConventionTask  toTypedArray &org.gradle.api.internal.ConventionTask  unixifyPath &org.gradle.api.internal.ConventionTask  windowsAwareBashCommandLine &org.gradle.api.internal.ConventionTask  windowsAwareCommandLine &org.gradle.api.internal.ConventionTask  
wipeOutputDir &org.gradle.api.internal.ConventionTask  error org.gradle.api.logging.Logger  warn org.gradle.api.logging.Logger  
ObjectFactory org.gradle.api.model  directoryProperty "org.gradle.api.model.ObjectFactory  fileProperty "org.gradle.api.model.ObjectFactory  listProperty "org.gradle.api.model.ObjectFactory  property "org.gradle.api.model.ObjectFactory  
AppliedPlugin org.gradle.api.plugins  create )org.gradle.api.plugins.ExtensionContainer  extraProperties )org.gradle.api.plugins.ExtensionContainer  
findByType )org.gradle.api.plugins.ExtensionContainer  	getByType )org.gradle.api.plugins.ExtensionContainer  getEXTRAProperties )org.gradle.api.plugins.ExtensionContainer  getExtraProperties )org.gradle.api.plugins.ExtensionContainer  setExtraProperties )org.gradle.api.plugins.ExtensionContainer  get /org.gradle.api.plugins.ExtraPropertiesExtension  has /org.gradle.api.plugins.ExtraPropertiesExtension  set /org.gradle.api.plugins.ExtraPropertiesExtension  
withPlugin $org.gradle.api.plugins.PluginManager  ListProperty org.gradle.api.provider  Property org.gradle.api.provider  Provider org.gradle.api.provider  
convention $org.gradle.api.provider.ListProperty  get $org.gradle.api.provider.ListProperty  set $org.gradle.api.provider.ListProperty  
convention  org.gradle.api.provider.Property  get  org.gradle.api.provider.Property  getISPresent  org.gradle.api.provider.Property  getIsPresent  org.gradle.api.provider.Property  	getORNull  org.gradle.api.provider.Property  	getOrNull  org.gradle.api.provider.Property  	isPresent  org.gradle.api.provider.Property  orNull  org.gradle.api.provider.Property  set  org.gradle.api.provider.Property  	setOrNull  org.gradle.api.provider.Property  
setPresent  org.gradle.api.provider.Property  get  org.gradle.api.provider.Provider  	getORNull  org.gradle.api.provider.Provider  	getOrNull  org.gradle.api.provider.Provider  orNull  org.gradle.api.provider.Provider  	setOrNull  org.gradle.api.provider.Provider  addAll #org.gradle.api.provider.SetProperty  <SAM-CONSTRUCTOR> org.gradle.api.specs.Spec  BUILD_SCRIPT_PATH org.gradle.api.tasks  DuplicatesStrategy org.gradle.api.tasks  Exec org.gradle.api.tasks  File org.gradle.api.tasks  Input org.gradle.api.tasks  	InputFile org.gradle.api.tasks  
InputFiles org.gradle.api.tasks  Internal org.gradle.api.tasks  Optional org.gradle.api.tasks  OutputDirectory org.gradle.api.tasks  
OutputFile org.gradle.api.tasks  OutputFiles org.gradle.api.tasks  
ReplaceTokens org.gradle.api.tasks  
TaskAction org.gradle.api.tasks  
TaskContainer org.gradle.api.tasks  TaskProvider org.gradle.api.tasks  
WorkResult org.gradle.api.tasks  apply org.gradle.api.tasks  boostVersion org.gradle.api.tasks  
bundleCommand org.gradle.api.tasks  bundleConfig org.gradle.api.tasks  cliFile org.gradle.api.tasks  cliPath org.gradle.api.tasks  deleteRecursively org.gradle.api.tasks  detectOSAwareHermesCommand org.gradle.api.tasks  
devEnabled org.gradle.api.tasks  	entryFile org.gradle.api.tasks  error org.gradle.api.tasks  extraPackagerArgs org.gradle.api.tasks  java org.gradle.api.tasks  mapOf org.gradle.api.tasks  
minifyEnabled org.gradle.api.tasks  moveTo org.gradle.api.tasks  
mutableListOf org.gradle.api.tasks  nodeExecutableAndArgs org.gradle.api.tasks  removeSuffix org.gradle.api.tasks  resourcesDir org.gradle.api.tasks  to org.gradle.api.tasks  toTypedArray org.gradle.api.tasks  unixifyPath org.gradle.api.tasks  windowsAwareBashCommandLine org.gradle.api.tasks  windowsAwareCommandLine org.gradle.api.tasks  BUILD_SCRIPT_PATH %org.gradle.api.tasks.AbstractExecTask  	Directory %org.gradle.api.tasks.AbstractExecTask  DirectoryProperty %org.gradle.api.tasks.AbstractExecTask  FileTree %org.gradle.api.tasks.AbstractExecTask  Input %org.gradle.api.tasks.AbstractExecTask  	InputFile %org.gradle.api.tasks.AbstractExecTask  
InputFiles %org.gradle.api.tasks.AbstractExecTask  Internal %org.gradle.api.tasks.AbstractExecTask  	JsonUtils %org.gradle.api.tasks.AbstractExecTask  ListProperty %org.gradle.api.tasks.AbstractExecTask  OutputDirectory %org.gradle.api.tasks.AbstractExecTask  
OutputFile %org.gradle.api.tasks.AbstractExecTask  OutputFiles %org.gradle.api.tasks.AbstractExecTask  Pair %org.gradle.api.tasks.AbstractExecTask  Property %org.gradle.api.tasks.AbstractExecTask  Provider %org.gradle.api.tasks.AbstractExecTask  RegularFile %org.gradle.api.tasks.AbstractExecTask  RegularFileProperty %org.gradle.api.tasks.AbstractExecTask  String %org.gradle.api.tasks.AbstractExecTask  apply %org.gradle.api.tasks.AbstractExecTask  cliPath %org.gradle.api.tasks.AbstractExecTask  commandLine %org.gradle.api.tasks.AbstractExecTask  deleteRecursively %org.gradle.api.tasks.AbstractExecTask  	dependsOn %org.gradle.api.tasks.AbstractExecTask  exec %org.gradle.api.tasks.AbstractExecTask  onlyIf %org.gradle.api.tasks.AbstractExecTask  resolveTaskParameters %org.gradle.api.tasks.AbstractExecTask  setupCommandLine %org.gradle.api.tasks.AbstractExecTask  to %org.gradle.api.tasks.AbstractExecTask  toTypedArray %org.gradle.api.tasks.AbstractExecTask  unixifyPath %org.gradle.api.tasks.AbstractExecTask  windowsAwareBashCommandLine %org.gradle.api.tasks.AbstractExecTask  windowsAwareCommandLine %org.gradle.api.tasks.AbstractExecTask  
wipeOutputDir %org.gradle.api.tasks.AbstractExecTask  BUILD_SCRIPT_PATH org.gradle.api.tasks.Exec  	Directory org.gradle.api.tasks.Exec  DirectoryProperty org.gradle.api.tasks.Exec  FileTree org.gradle.api.tasks.Exec  Input org.gradle.api.tasks.Exec  	InputFile org.gradle.api.tasks.Exec  
InputFiles org.gradle.api.tasks.Exec  Internal org.gradle.api.tasks.Exec  	JsonUtils org.gradle.api.tasks.Exec  ListProperty org.gradle.api.tasks.Exec  OutputDirectory org.gradle.api.tasks.Exec  
OutputFile org.gradle.api.tasks.Exec  OutputFiles org.gradle.api.tasks.Exec  Pair org.gradle.api.tasks.Exec  Property org.gradle.api.tasks.Exec  Provider org.gradle.api.tasks.Exec  RegularFile org.gradle.api.tasks.Exec  RegularFileProperty org.gradle.api.tasks.Exec  String org.gradle.api.tasks.Exec  apply org.gradle.api.tasks.Exec  cliPath org.gradle.api.tasks.Exec  commandLine org.gradle.api.tasks.Exec  deleteRecursively org.gradle.api.tasks.Exec  	dependsOn org.gradle.api.tasks.Exec  exec org.gradle.api.tasks.Exec  onlyIf org.gradle.api.tasks.Exec  resolveTaskParameters org.gradle.api.tasks.Exec  setupCommandLine org.gradle.api.tasks.Exec  to org.gradle.api.tasks.Exec  toTypedArray org.gradle.api.tasks.Exec  unixifyPath org.gradle.api.tasks.Exec  windowsAwareBashCommandLine org.gradle.api.tasks.Exec  windowsAwareCommandLine org.gradle.api.tasks.Exec  
wipeOutputDir org.gradle.api.tasks.Exec  named "org.gradle.api.tasks.TaskContainer  register "org.gradle.api.tasks.TaskContainer  	dependsOn !org.gradle.api.tasks.TaskProvider  getDEPENDSOn !org.gradle.api.tasks.TaskProvider  getDependsOn !org.gradle.api.tasks.TaskProvider  PatternFilterable org.gradle.api.tasks.util  include +org.gradle.api.tasks.util.PatternFilterable  Jvm org.gradle.internal.jvm  current org.gradle.internal.jvm.Jvm  getJAVAVersion org.gradle.internal.jvm.Jvm  getJavaVersion org.gradle.internal.jvm.Jvm  javaVersion org.gradle.internal.jvm.Jvm  setJavaVersion org.gradle.internal.jvm.Jvm  
ExecResult org.gradle.process  ExecSpec org.gradle.process  ProcessForkOptions org.gradle.process  commandLine org.gradle.process.ExecSpec  
workingDir org.gradle.process.ExecSpec  KotlinTopLevelExtension org.jetbrains.kotlin.gradle.dsl  jvmToolchain 7org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtension  Document org.w3c.dom  Element org.w3c.dom  Node org.w3c.dom  getElementsByTagName org.w3c.dom.Document  getAttribute org.w3c.dom.Element  item org.w3c.dom.NodeList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  