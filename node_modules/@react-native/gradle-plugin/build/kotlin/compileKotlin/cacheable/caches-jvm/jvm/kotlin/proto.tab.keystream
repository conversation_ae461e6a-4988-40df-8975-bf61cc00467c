!com/facebook/react/ReactExtensioncom/facebook/react/ReactPlugin)com/facebook/react/ReactRootProjectPlugin&com/facebook/react/TaskConfigurationKt1com/facebook/react/internal/PrivateReactExtension+com/facebook/react/model/ModelCodegenConfig2com/facebook/react/model/ModelCodegenConfigAndroid)com/facebook/react/model/ModelPackageJson*com/facebook/react/tasks/BundleHermesCTask5com/facebook/react/tasks/GenerateCodegenArtifactsTask2com/facebook/react/tasks/GenerateCodegenSchemaTask5com/facebook/react/tasks/internal/BuildCodegenCLITask?com/facebook/react/tasks/internal/BuildCodegenCLITask$Companion2com/facebook/react/tasks/internal/PrepareBoostTask1com/facebook/react/tasks/internal/PrepareGlogTask0com/facebook/react/tasks/internal/PrepareJSCTask:com/facebook/react/tasks/internal/PreparePrefabHeadersTask@com/facebook/react/tasks/internal/utils/PrefabPreprocessingEntry-com/facebook/react/utils/AgpConfiguratorUtils/com/facebook/react/utils/AgpConfiguratorUtilsKt,com/facebook/react/utils/BackwardCompatUtils(com/facebook/react/utils/DependencyUtils$com/facebook/react/utils/FileUtilsKt-com/facebook/react/utils/JdkConfiguratorUtils"com/facebook/react/utils/JsonUtils0com/facebook/react/utils/KotlinStdlibCompatUtils-com/facebook/react/utils/NdkConfiguratorUtilscom/facebook/react/utils/Os"com/facebook/react/utils/PathUtils%com/facebook/react/utils/ProjectUtils&com/facebook/react/utils/PropertyUtils$com/facebook/react/utils/TaskUtilsKt.kotlin_module                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              