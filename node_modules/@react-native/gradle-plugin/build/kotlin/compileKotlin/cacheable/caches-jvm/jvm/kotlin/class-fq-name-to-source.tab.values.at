/ Header Record For PersistentHashMapValueStorage5 4src/main/kotlin/com/facebook/react/ReactExtension.kt2 1src/main/kotlin/com/facebook/react/ReactPlugin.kt= <src/main/kotlin/com/facebook/react/ReactRootProjectPlugin.ktE Dsrc/main/kotlin/com/facebook/react/internal/PrivateReactExtension.kt? >src/main/kotlin/com/facebook/react/model/ModelCodegenConfig.ktF Esrc/main/kotlin/com/facebook/react/model/ModelCodegenConfigAndroid.kt= <src/main/kotlin/com/facebook/react/model/ModelPackageJson.kt> =src/main/kotlin/com/facebook/react/tasks/BundleHermesCTask.ktI Hsrc/main/kotlin/com/facebook/react/tasks/GenerateCodegenArtifactsTask.ktF Esrc/main/kotlin/com/facebook/react/tasks/GenerateCodegenSchemaTask.ktI Hsrc/main/kotlin/com/facebook/react/tasks/internal/BuildCodegenCLITask.ktI Hsrc/main/kotlin/com/facebook/react/tasks/internal/BuildCodegenCLITask.ktF Esrc/main/kotlin/com/facebook/react/tasks/internal/PrepareBoostTask.ktE Dsrc/main/kotlin/com/facebook/react/tasks/internal/PrepareGlogTask.ktD Csrc/main/kotlin/com/facebook/react/tasks/internal/PrepareJSCTask.ktN Msrc/main/kotlin/com/facebook/react/tasks/internal/PreparePrefabHeadersTask.ktT Ssrc/main/kotlin/com/facebook/react/tasks/internal/utils/PrefabPreprocessingEntry.ktA @src/main/kotlin/com/facebook/react/utils/AgpConfiguratorUtils.kt@ ?src/main/kotlin/com/facebook/react/utils/BackwardCompatUtils.kt< ;src/main/kotlin/com/facebook/react/utils/DependencyUtils.ktA @src/main/kotlin/com/facebook/react/utils/JdkConfiguratorUtils.kt6 5src/main/kotlin/com/facebook/react/utils/JsonUtils.ktD Csrc/main/kotlin/com/facebook/react/utils/KotlinStdlibCompatUtils.ktA @src/main/kotlin/com/facebook/react/utils/NdkConfiguratorUtils.kt/ .src/main/kotlin/com/facebook/react/utils/Os.kt9 8src/main/kotlin/com/facebook/react/utils/ProjectUtils.kt: 9src/main/kotlin/com/facebook/react/utils/PropertyUtils.kt