<dependencies>
  <compile
      roots="com.facebook.react:react-android:0.74.1:release@aar,com.google.android.exoplayer:exoplayer:2.13.3@aar,com.google.android.exoplayer:exoplayer-ui:2.13.3@aar,androidx.media:media:1.2.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity:1.6.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar,androidx.core:core-ktx:1.9.0@aar,androidx.core:core:1.9.0@aar,androidx.core:core:1.9.0@aar,androidx.lifecycle:lifecycle-runtime:2.5.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.savedstate:savedstate:1.2.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-common:2.5.1@jar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.annotation:annotation:1.3.0@jar,com.google.android.exoplayer:extension-okhttp:2.13.3@aar,com.facebook.fresco:imagepipeline-okhttp3:3.1.3@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.squareup.okhttp3:okhttp:4.9.2@jar,androidx.annotation:annotation-experimental:1.3.0@aar,com.squareup.okio:okio:2.9.0@jar,com.facebook.fresco:fresco:3.1.3@aar,com.facebook.fresco:middleware:3.1.3@aar,com.facebook.fresco:ui-common:3.1.3@aar,com.facebook.fresco:fbcore:3.1.3@aar,com.facebook.fresco:imagepipeline:3.1.3@aar,com.facebook.fresco:imagepipeline-base:3.1.3@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar,org.jetbrains:annotations:13.0@jar,com.google.android.exoplayer:exoplayer-core:2.13.3@aar,com.google.android.exoplayer:exoplayer-common:2.13.3@aar,com.google.guava:guava:27.1-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.tracing:tracing:1.1.0@aar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fbjni:fbjni:0.6.0@aar,com.facebook.soloader:soloader:0.10.5@aar,com.facebook.soloader:nativeloader:0.10.5@jar,com.facebook.soloader:annotation:0.10.5@jar,com.facebook.fresco:drawee:3.1.3@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.fresco:imagepipeline-native:3.1.3@aar,com.facebook.fresco:memory-type-ashmem:3.1.3@aar,com.facebook.fresco:memory-type-native:3.1.3@aar,com.facebook.fresco:memory-type-java:3.1.3@aar,com.facebook.fresco:nativeimagefilters:3.1.3@aar,com.facebook.fresco:nativeimagetranscoder:3.1.3@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,javax.inject:javax.inject:1@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.android.exoplayer:exoplayer-extractor:2.13.3@aar,com.google.android.exoplayer:exoplayer-dash:2.13.3@aar,com.google.android.exoplayer:exoplayer-hls:2.13.3@aar,com.google.android.exoplayer:exoplayer-smoothstreaming:2.13.3@aar,com.google.android.exoplayer:exoplayer-transformer:2.13.3@aar">
    <dependency
        name="com.facebook.react:react-android:0.74.1:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-ui:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-ui"/>
    <dependency
        name="androidx.media:media:1.2.1@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.6.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.9.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.core:core:1.9.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.annotation:annotation:1.3.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="com.google.android.exoplayer:extension-okhttp:2.13.3@aar"
        simpleName="com.google.android.exoplayer:extension-okhttp"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.1.3@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.squareup.okio:okio:2.9.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="com.facebook.fresco:fresco:3.1.3@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:middleware:3.1.3@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.1.3@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.1.3@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.1.3@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.1.3@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-core:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-core"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-common:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-common"/>
    <dependency
        name="com.google.guava:guava:27.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.tracing:tracing:1.1.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.6.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.soloader:soloader:0.10.5@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.10.5@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.facebook.soloader:annotation:0.10.5@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.facebook.fresco:drawee:3.1.3@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.1.3@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.1.3@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.1.3@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.1.3@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.1.3@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.1.3@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-extractor:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-extractor"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-dash:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-dash"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-hls:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-hls"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-smoothstreaming"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-transformer:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-transformer"/>
  </compile>
  <package
      roots="com.facebook.react:react-android:0.74.1:release@aar,com.google.android.exoplayer:exoplayer:2.13.3@aar,com.google.android.exoplayer:extension-okhttp:2.13.3@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity:1.6.0@aar,androidx.emoji2:emoji2-views-helper:1.2.0@aar,androidx.emoji2:emoji2:1.2.0@aar,androidx.lifecycle:lifecycle-process:2.4.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.1.0@aar,com.google.android.exoplayer:exoplayer-dash:2.13.3@aar,com.google.android.exoplayer:exoplayer-hls:2.13.3@aar,com.google.android.exoplayer:exoplayer-smoothstreaming:2.13.3@aar,com.google.android.exoplayer:exoplayer-transformer:2.13.3@aar,com.google.android.exoplayer:exoplayer-ui:2.13.3@aar,com.google.android.exoplayer:exoplayer-core:2.13.3@aar,com.google.android.exoplayer:exoplayer-extractor:2.13.3@aar,com.google.android.exoplayer:exoplayer-common:2.13.3@aar,androidx.autofill:autofill:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,com.facebook.fresco:fresco:3.1.3@aar,com.facebook.fresco:imagepipeline-okhttp3:3.1.3@aar,com.facebook.fresco:drawee:3.1.3@aar,com.facebook.fresco:nativeimagefilters:3.1.3@aar,com.facebook.fresco:memory-type-native:3.1.3@aar,com.facebook.fresco:memory-type-java:3.1.3@aar,com.facebook.fresco:imagepipeline-native:3.1.3@aar,com.facebook.fresco:memory-type-ashmem:3.1.3@aar,com.facebook.fresco:imagepipeline:3.1.3@aar,com.facebook.fresco:nativeimagetranscoder:3.1.3@aar,com.facebook.fresco:imagepipeline-base:3.1.3@aar,com.facebook.fresco:middleware:3.1.3@aar,com.facebook.fresco:ui-common:3.1.3@aar,com.facebook.fresco:soloader:3.1.3@aar,com.facebook.fresco:fbcore:3.1.3@aar,androidx.media:media:1.2.1@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar,androidx.core:core-ktx:1.9.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.9.0@aar,androidx.core:core:1.9.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.lifecycle:lifecycle-runtime:2.5.1@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.savedstate:savedstate:1.2.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.concurrent:concurrent-futures:1.0.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.lifecycle:lifecycle-common:2.5.1@jar,androidx.annotation:annotation:1.3.0@jar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.squareup.okhttp3:okhttp:4.9.2@jar,com.facebook.fbjni:fbjni:0.6.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.facebook.soloader:soloader:0.10.5@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.squareup.okio:okio:2.9.0@jar,javax.inject:javax.inject:1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21@jar,androidx.annotation:annotation-experimental:1.3.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar,com.facebook.soloader:nativeloader:0.10.5@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.soloader:annotation:0.10.5@jar,org.jetbrains:annotations:13.0@jar,com.google.guava:guava:27.1-android@jar,com.parse.bolts:bolts-tasks:1.4.0@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar">
    <dependency
        name="com.facebook.react:react-android:0.74.1:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer"/>
    <dependency
        name="com.google.android.exoplayer:extension-okhttp:2.13.3@aar"
        simpleName="com.google.android.exoplayer:extension-okhttp"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.6.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.4.1@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.1.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-dash:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-dash"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-hls:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-hls"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-smoothstreaming"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-transformer:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-transformer"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-ui:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-ui"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-core:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-core"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-extractor:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-extractor"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-common:2.13.3@aar"
        simpleName="com.google.android.exoplayer:exoplayer-common"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="com.facebook.fresco:fresco:3.1.3@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.1.3@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:drawee:3.1.3@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.1.3@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.1.3@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.1.3@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.1.3@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.1.3@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.1.3@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.1.3@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.1.3@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="com.facebook.fresco:middleware:3.1.3@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.1.3@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:soloader:3.1.3@aar"
        simpleName="com.facebook.fresco:soloader"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.1.3@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="androidx.media:media:1.2.1@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.9.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.9.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.0.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.annotation:annotation:1.3.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.6.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.facebook.soloader:soloader:0.10.5@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.squareup.okio:okio:2.9.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.10.5@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.soloader:annotation:0.10.5@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:guava:27.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.parse.bolts:bolts-tasks:1.4.0@jar"
        simpleName="com.parse.bolts:bolts-tasks"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
