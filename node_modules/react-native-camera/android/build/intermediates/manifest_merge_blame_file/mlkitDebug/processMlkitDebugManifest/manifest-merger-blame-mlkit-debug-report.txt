1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="org.reactnative.camera" >
4
5    <uses-sdk android:minSdkVersion="23" />
6
7    <uses-permission android:name="android.permission.CAMERA" />
7-->/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:2:3-63
7-->/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:2:20-60
8
9    <uses-feature
9-->/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:4:3-83
10        android:name="android.hardware.camera"
10-->/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:4:17-55
11        android:required="false" />
11-->/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:4:56-80
12    <uses-feature
12-->/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:5:3-93
13        android:name="android.hardware.camera.autofocus"
13-->/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:5:17-65
14        android:required="false" />
14-->/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:5:66-90
15
16</manifest>
