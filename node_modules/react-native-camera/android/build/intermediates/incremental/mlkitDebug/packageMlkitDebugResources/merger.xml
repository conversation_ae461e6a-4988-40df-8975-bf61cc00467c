<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/res"><file name="texture_view" path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/res/layout-v14/texture_view.xml" qualifiers="v14" type="layout"/><file name="surface_view" path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/res/layout/surface_view.xml" qualifiers="" type="layout"/><file path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/res/values/public.xml" qualifiers=""><public name="facing" type="attr"/><public name="aspectRatio" type="attr"/><public name="autoFocus" type="attr"/><public name="flash" type="attr"/><public name="Widget.CameraView" type="style"/></file><file path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/res/values/styles.xml" qualifiers=""><style name="Widget.CameraView" parent="android:Widget">
        <item name="android:adjustViewBounds">false</item>
        <item name="facing">back</item>
        <item name="aspectRatio">4:3</item>
        <item name="autoFocus">true</item>
        <item name="flash">auto</item>
    </style></file><file path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/res/values/attrs.xml" qualifiers=""><declare-styleable name="CameraView">
        
        <attr name="android:adjustViewBounds"/>
        
        <attr format="enum" name="facing">
            
            <enum name="back" value="0"/>
            
            <enum name="front" value="1"/>
        </attr>
        
        <attr format="string" name="aspectRatio"/>
        
        <attr format="boolean" name="autoFocus"/>
        
        <attr format="enum" name="flash">
            
            <enum name="off" value="0"/>
            
            <enum name="on" value="1"/>
            
            <enum name="torch" value="2"/>
            
            <enum name="auto" value="3"/>
            
            <enum name="redEye" value="4"/>
        </attr>
    </declare-styleable></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="mlkit$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/mlkit/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="mlkit" generated-set="mlkit$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/mlkit/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/build/generated/res/resValues/mlkit/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/build/generated/res/resValues/mlkit/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/mlkitDebug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant" generated-set="variant$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/mlkitDebug/res"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="CameraView">
        
        <attr name="android:adjustViewBounds"/>
        
        <attr format="enum" name="facing">
            
            <enum name="back" value="0"/>
            
            <enum name="front" value="1"/>
        </attr>
        
        <attr format="string" name="aspectRatio"/>
        
        <attr format="boolean" name="autoFocus"/>
        
        <attr format="enum" name="flash">
            
            <enum name="off" value="0"/>
            
            <enum name="on" value="1"/>
            
            <enum name="torch" value="2"/>
            
            <enum name="auto" value="3"/>
            
            <enum name="redEye" value="4"/>
        </attr>
    </declare-styleable></configuration></mergedItems></merger>