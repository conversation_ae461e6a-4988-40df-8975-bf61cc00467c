<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Widget.CameraView" parent="android:Widget">
        <item name="android:adjustViewBounds">false</item>
        <item name="facing">back</item>
        <item name="aspectRatio">4:3</item>
        <item name="autoFocus">true</item>
        <item name="flash">auto</item>
    </style>
    <declare-styleable name="CameraView">
        
        <attr name="android:adjustViewBounds"/>
        
        <attr format="enum" name="facing">
            <!-- The camera device faces the opposite direction as the device's screen. -->
            <enum name="back" value="0"/>
            <!-- The camera device faces the same direction as the device's screen. -->
            <enum name="front" value="1"/>
        </attr>
        
        <attr format="string" name="aspectRatio"/>
        
        <attr format="boolean" name="autoFocus"/>
        
        <attr format="enum" name="flash">
            <!-- Flash will not be fired. -->
            <enum name="off" value="0"/>
            <!--
              Flash will always be fired during snapshot.
              The flash may also be fired during preview or auto-focus depending on the driver.
            -->
            <enum name="on" value="1"/>
            <!--
              Constant emission of light during preview, auto-focus and snapshot.
              This can also be used for video recording.
            -->
            <enum name="torch" value="2"/>
            <!--
              Flash will be fired automatically when required.
              The flash may be fired during preview, auto-focus, or snapshot depending on the
              driver.
            -->
            <enum name="auto" value="3"/>
            <!--
              Flash will be fired in red-eye reduction mode.
            -->
            <enum name="redEye" value="4"/>
        </attr>
    </declare-styleable>
</resources>