<variant
    name="generalRelease"
    package="org.reactnative.camera"
    minSdkVersion="23"
    targetSdkVersion="34"
    mergedManifest="build/intermediates/merged_manifest/generalRelease/processGeneralReleaseManifest/AndroidManifest.xml"
    proguardFiles="build/intermediates/default_proguard_files/global/proguard-android.txt-8.4.0"
    partialResultsDir="build/intermediates/lint_vital_partial_results/generalRelease/lintVitalAnalyzeGeneralRelease/out"
    desugaredMethodsFiles="/Users/<USER>/.gradle/caches/transforms-4/98d1e43d0e67512b150a16919edf7e80/transformed/D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/general/java:src/release/java:src/generalRelease/java:src/main/kotlin:src/general/kotlin:src/release/kotlin:src/generalRelease/kotlin"
        resDirectories="src/main/res:src/general/res:src/release/res:src/generalRelease/res"
        assetsDirectories="src/main/assets:src/general/assets:src/release/assets:src/generalRelease/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build/intermediates/javac/generalRelease/compileGeneralReleaseJavaWithJavac/classes:build/intermediates/compile_r_class_jar/generalRelease/generateGeneralReleaseRFile/R.jar"
      type="MAIN"
      applicationId="org.reactnative.camera"
      generatedSourceFolders="build/generated/ap_generated_sources/generalRelease/out:build/generated/source/buildConfig/general/release"
      generatedResourceFolders="build/generated/res/resValues/general/release"
      desugaredMethodsFiles="/Users/<USER>/.gradle/caches/transforms-4/98d1e43d0e67512b150a16919edf7e80/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
