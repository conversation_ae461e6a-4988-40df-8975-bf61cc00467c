-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:1:1-6:12
INJECTED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:1:1-6:12
	package
		ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:1:70-102
		INJECTED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:2:3-63
	android:name
		ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:2:20-60
uses-feature#android.hardware.camera
ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:4:3-83
	android:required
		ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:4:56-80
	android:name
		ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:4:17-55
uses-feature#android.hardware.camera.autofocus
ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:5:3-93
	android:required
		ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:5:66-90
	android:name
		ADDED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml:5:17-65
uses-sdk
INJECTED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Projects/ModrkClient/node_modules/react-native-camera/android/src/main/AndroidManifest.xml
