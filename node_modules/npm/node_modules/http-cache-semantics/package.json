{"name": "http-cache-semantics", "version": "4.1.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": "https://github.com/kornelski/http-cache-semantics.git", "main": "index.js", "scripts": {"test": "mocha"}, "files": ["index.js"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://kornel.ski/)", "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "^5.13.0", "eslint-plugin-prettier": "^3.0.1", "husky": "^0.14.3", "lint-staged": "^8.1.3", "mocha": "^5.1.0", "prettier": "^1.14.3", "prettier-eslint-cli": "^4.7.1"}}