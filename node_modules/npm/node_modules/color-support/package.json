{"name": "color-support", "version": "1.1.3", "description": "A module which will endeavor to guess your terminal's level of color support.", "main": "index.js", "browser": "browser.js", "bin": "bin.js", "devDependencies": {"tap": "^10.3.3"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/color-support.git"}, "keywords": ["terminal", "color", "support", "xterm", "truecolor", "256"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "files": ["browser.js", "index.js", "bin.js"]}