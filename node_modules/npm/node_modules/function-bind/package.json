{"name": "function-bind", "version": "1.1.1", "description": "Implementation of Function.prototype.bind", "keywords": ["function", "bind", "shim", "es5"], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/Raynos/function-bind.git", "main": "index", "homepage": "https://github.com/Raynos/function-bind", "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "covert": "^1.1.0", "eslint": "^4.5.0", "jscs": "^3.0.7", "tape": "^4.8.0"}, "license": "MIT", "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npm run coverage -- --quiet", "tests-only": "node test", "coverage": "covert test/*.js", "lint": "npm run jscs && npm run eslint", "jscs": "jscs *.js */*.js", "eslint": "eslint *.js */*.js"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}