{"name": "common-ancestor-path", "version": "1.0.1", "files": ["index.js"], "description": "Find the common ancestor of 2 or more paths on Windows or Unix", "repository": {"type": "git", "url": "git+https://github.com/isaacs/common-ancestor-path"}, "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.7"}}