{"name": "columnify", "version": "1.6.0", "description": "Render data in text columns. Supports in-column text-wrap.", "main": "columnify.js", "scripts": {"pretest": "npm prune", "test": "make prepublish && tape test/*.js | tap-spec", "bench": "npm test && node bench", "prepublish": "make prepublish"}, "babel": {"presets": ["es2015"]}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.3.13", "chalk": "^1.1.1", "tap-spec": "^5.0.0", "tape": "^4.4.0"}, "repository": {"type": "git", "url": "git://github.com/timoxley/columnify.git"}, "keywords": ["column", "text", "ansi", "console", "terminal", "wrap", "table"], "bugs": {"url": "https://github.com/timoxley/columnify/issues"}, "homepage": "https://github.com/timoxley/columnify", "engines": {"node": ">=8.0.0"}, "dependencies": {"strip-ansi": "^6.0.1", "wcwidth": "^1.0.0"}, "directories": {"test": "test"}}