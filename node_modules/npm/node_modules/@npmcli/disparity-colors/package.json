{"name": "@npmcli/disparity-colors", "version": "2.0.0", "main": "lib/index.js", "files": ["bin/", "lib/"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "description": "Colorizes unified diff output", "repository": {"type": "git", "url": "https://github.com/npm/disparity-colors.git"}, "keywords": ["disparity", "npm", "npmcli", "diff", "char", "unified", "multiline", "string", "color", "ansi", "terminal", "cli", "tty"], "author": "GitHub Inc.", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://ruyadorno.com", "twitter": "ruyadorno"}], "license": "ISC", "scripts": {"lint": "eslint \"**/*.js\"", "pretest": "npm run lint", "test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "tap": {"check-coverage": true}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "tap": "^16.0.1"}, "dependencies": {"ansi-styles": "^4.3.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2"}}