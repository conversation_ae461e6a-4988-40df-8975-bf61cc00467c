{"name": "@npmcli/query", "version": "1.1.1", "description": "npm query parser and tools", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "posttest": "npm run lint"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://ruyadorno.com", "twitter": "ruyadorno"}], "keywords": ["ast", "npm", "npmcli", "parser", "postcss", "postcss-selector-parser", "query"], "author": "GitHub Inc.", "license": "ISC", "files": ["bin/", "lib/"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "tap": "^16.2.0"}, "dependencies": {"npm-package-arg": "^9.1.0", "postcss-selector-parser": "^6.0.10", "semver": "^7.3.7"}, "repository": {"type": "git", "url": "https://github.com/npm/query.git"}}