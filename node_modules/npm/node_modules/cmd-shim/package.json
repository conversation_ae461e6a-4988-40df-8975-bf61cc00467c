{"name": "cmd-shim", "version": "5.0.0", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "prepublishOnly": "git push origin --follow-tags", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "main": "lib/index.js", "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "author": "GitHub Inc.", "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2"}}