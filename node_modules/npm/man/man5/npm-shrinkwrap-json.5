.TH "NPM\-SHRINKWRAP\.JSON" "5" "August 2022" "" ""
.SH "NAME"
\fBnpm-shrinkwrap.json\fR \- A publishable lockfile
.SS Description
.P
\fBnpm\-shrinkwrap\.json\fP is a file created by npm help \fBnpm
shrinkwrap\fP\|\. It is identical to
\fBpackage\-lock\.json\fP, with one major caveat: Unlike \fBpackage\-lock\.json\fP,
\fBnpm\-shrinkwrap\.json\fP may be included when publishing a package\.
.P
The recommended use\-case for \fBnpm\-shrinkwrap\.json\fP is applications deployed
through the publishing process on the registry: for example, daemons and
command\-line tools intended as global installs or \fBdevDependencies\fP\|\. It's
strongly discouraged for library authors to publish this file, since that
would prevent end users from having control over transitive dependency
updates\.
.P
If both \fBpackage\-lock\.json\fP and \fBnpm\-shrinkwrap\.json\fP are present in a
package root, \fBnpm\-shrinkwrap\.json\fP will be preferred over the
\fBpackage\-lock\.json\fP file\.
.P
For full details and description of the \fBnpm\-shrinkwrap\.json\fP file format,
refer to the manual page for
npm help package\-lock\.json\.
.SS See also
.RS 0
.IP \(bu 2
npm help shrinkwrap
.IP \(bu 2
npm help package\-lock\.json
.IP \(bu 2
npm help package\.json
.IP \(bu 2
npm help install

.RE
