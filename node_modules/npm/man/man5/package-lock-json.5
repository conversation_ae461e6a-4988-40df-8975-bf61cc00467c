.TH "PACKAGE\-LOCK\.JSON" "5" "August 2022" "" ""
.SH "NAME"
\fBpackage-lock.json\fR \- A manifestation of the manifest
.SS Description
.P
\fBpackage\-lock\.json\fP is automatically generated for any operations where npm
modifies either the \fBnode_modules\fP tree, or \fBpackage\.json\fP\|\. It describes the
exact tree that was generated, such that subsequent installs are able to
generate identical trees, regardless of intermediate dependency updates\.
.P
This file is intended to be committed into source repositories, and serves
various purposes:
.RS 0
.IP \(bu 2
Describe a single representation of a dependency tree such that
teammates, deployments, and continuous integration are guaranteed to
install exactly the same dependencies\.
.IP \(bu 2
Provide a facility for users to "time\-travel" to previous states of
\fBnode_modules\fP without having to commit the directory itself\.
.IP \(bu 2
Facilitate greater visibility of tree changes through readable source
control diffs\.
.IP \(bu 2
Optimize the installation process by allowing npm to skip repeated
metadata resolutions for previously\-installed packages\.
.IP \(bu 2
As of npm v7, lockfiles include enough information to gain a complete
picture of the package tree, reducing the need to read \fBpackage\.json\fP
files, and allowing for significant performance improvements\.

.RE
.SS \fBpackage\-lock\.json\fP vs \fBnpm\-shrinkwrap\.json\fP
.P
Both of these files have the same format, and perform similar functions in
the root of a project\.
.P
The difference is that \fBpackage\-lock\.json\fP cannot be published, and it will 
be ignored if found in any place other than the root project\.
.P
In contrast, npm help npm\-shrinkwrap\.json allows
publication, and defines the dependency tree from the point encountered\.
This is not recommended unless deploying a CLI tool or otherwise using the
publication process for producing production packages\.
.P
If both \fBpackage\-lock\.json\fP and \fBnpm\-shrinkwrap\.json\fP are present in the
root of a project, \fBnpm\-shrinkwrap\.json\fP will take precedence and
\fBpackage\-lock\.json\fP will be ignored\.
.SS Hidden Lockfiles
.P
In order to avoid processing the \fBnode_modules\fP folder repeatedly, npm as
of v7 uses a "hidden" lockfile present in
\fBnode_modules/\.package\-lock\.json\fP\|\.  This contains information about the
tree, and is used in lieu of reading the entire \fBnode_modules\fP hierarchy
provided that the following conditions are met:
.RS 0
.IP \(bu 2
All package folders it references exist in the \fBnode_modules\fP hierarchy\.
.IP \(bu 2
No package folders exist in the \fBnode_modules\fP hierarchy that are not
listed in the lockfile\.
.IP \(bu 2
The modified time of the file is at least as recent as all of the package
folders it references\.

.RE
.P
That is, the hidden lockfile will only be relevant if it was created as
part of the most recent update to the package tree\.  If another CLI mutates
the tree in any way, this will be detected, and the hidden lockfile will be
ignored\.
.P
Note that it \fIis\fR possible to manually change the \fIcontents\fR of a package
in such a way that the modified time of the package folder is unaffected\.
For example, if you add a file to \fBnode_modules/foo/lib/bar\.js\fP, then the
modified time on \fBnode_modules/foo\fP will not reflect this change\.  If you
are manually editing files in \fBnode_modules\fP, it is generally best to
delete the file at \fBnode_modules/\.package\-lock\.json\fP\|\.
.P
As the hidden lockfile is ignored by older npm versions, it does not
contain the backwards compatibility affordances present in "normal"
lockfiles\.  That is, it is \fBlockfileVersion: 3\fP, rather than
\fBlockfileVersion: 2\fP\|\.
.SS Handling Old Lockfiles
.P
When npm detects a lockfile from npm v6 or before during the package
installation process, it is automatically updated to fetch missing
information from either the \fBnode_modules\fP tree or (in the case of empty
\fBnode_modules\fP trees or very old lockfile formats) the npm registry\.
.SS File Format
.SS \fBname\fP
.P
The name of the package this is a package\-lock for\. This will match what's
in \fBpackage\.json\fP\|\.
.SS \fBversion\fP
.P
The version of the package this is a package\-lock for\. This will match
what's in \fBpackage\.json\fP\|\.
.SS \fBlockfileVersion\fP
.P
An integer version, starting at \fB1\fP with the version number of this
document whose semantics were used when generating this
\fBpackage\-lock\.json\fP\|\.
.P
Note that the file format changed significantly in npm v7 to track
information that would have otherwise required looking in \fBnode_modules\fP or
the npm registry\.  Lockfiles generated by npm v7 will contain
\fBlockfileVersion: 2\fP\|\.
.RS 0
.IP \(bu 2
No version provided: an "ancient" shrinkwrap file from a version of npm
prior to npm v5\.
.IP \(bu 2
\fB1\fP: The lockfile version used by npm v5 and v6\.
.IP \(bu 2
\fB2\fP: The lockfile version used by npm v7, which is backwards compatible
to v1 lockfiles\.
.IP \(bu 2
\fB3\fP: The lockfile version used by npm v7, \fIwithout\fR backwards
compatibility affordances\.  This is used for the hidden lockfile at
\fBnode_modules/\.package\-lock\.json\fP, and will likely be used in a future
version of npm, once support for npm v6 is no longer relevant\.

.RE
.P
npm will always attempt to get whatever data it can out of a lockfile, even
if it is not a version that it was designed to support\.
.SS \fBpackages\fP
.P
This is an object that maps package locations to an object containing the
information about that package\.
.P
The root project is typically listed with a key of \fB""\fP, and all other
packages are listed with their relative paths from the root project folder\.
.P
Package descriptors have the following fields:
.RS 0
.IP \(bu 2
version: The version found in \fBpackage\.json\fP
.IP \(bu 2
resolved: The place where the package was actually resolved from\.  In
the case of packages fetched from the registry, this will be a url to a
tarball\.  In the case of git dependencies, this will be the full git url
with commit sha\.  In the case of link dependencies, this will be the
location of the link target\. \fBregistry\.npmjs\.org\fP is a magic value meaning
"the currently configured registry"\.
.IP \(bu 2
integrity: A \fBsha512\fP or \fBsha1\fP Standard Subresource
Integrity \fIhttps://w3c\.github\.io/webappsec/specs/subresourceintegrity/\fR
string for the artifact that was unpacked in this location\.
.IP \(bu 2
link: A flag to indicate that this is a symbolic link\.  If this is
present, no other fields are specified, since the link target will also
be included in the lockfile\.
.IP \(bu 2
dev, optional, devOptional: If the package is strictly part of the
\fBdevDependencies\fP tree, then \fBdev\fP will be true\.  If it is strictly part
of the \fBoptionalDependencies\fP tree, then \fBoptional\fP will be set\.  If it
is both a \fBdev\fP dependency \fIand\fR an \fBoptional\fP dependency of a non\-dev
dependency, then \fBdevOptional\fP will be set\.  (An \fBoptional\fP dependency of
a \fBdev\fP dependency will have both \fBdev\fP and \fBoptional\fP set\.)
.IP \(bu 2
inBundle: A flag to indicate that the package is a bundled dependency\.
.IP \(bu 2
hasInstallScript: A flag to indicate that the package has a \fBpreinstall\fP,
\fBinstall\fP, or \fBpostinstall\fP script\.
.IP \(bu 2
hasShrinkwrap: A flag to indicate that the package has an
\fBnpm\-shrinkwrap\.json\fP file\.
.IP \(bu 2
bin, license, engines, dependencies, optionalDependencies: fields from
\fBpackage\.json\fP

.RE
.SS dependencies
.P
Legacy data for supporting versions of npm that use \fBlockfileVersion: 1\fP\|\.
This is a mapping of package names to dependency objects\.  Because the
object structure is strictly hierarchical, symbolic link dependencies are
somewhat challenging to represent in some cases\.
.P
npm v7 ignores this section entirely if a \fBpackages\fP section is present,
but does keep it up to date in order to support switching between npm v6
and npm v7\.
.P
Dependency objects have the following fields:
.RS 0
.IP \(bu 2
version: a specifier that varies depending on the nature of the package,
and is usable in fetching a new copy of it\.
.RS
.IP \(bu 2
bundled dependencies: Regardless of source, this is a version number
that is purely for informational purposes\.
.IP \(bu 2
registry sources: This is a version number\. (eg, \fB1\.2\.3\fP)
.IP \(bu 2
git sources: This is a git specifier with resolved committish\. (eg,
\fBgit+https://example\.com/foo/bar#115311855adb0789a0466714ed48a1499ffea97e\fP)
.IP \(bu 2
http tarball sources: This is the URL of the tarball\. (eg,
\fBhttps://example\.com/example\-1\.3\.0\.tgz\fP)
.IP \(bu 2
local tarball sources: This is the file URL of the tarball\. (eg
\fBfile:///opt/storage/example\-1\.3\.0\.tgz\fP)
.IP \(bu 2
local link sources: This is the file URL of the link\. (eg
\fBfile:libs/our\-module\fP)

.RE
.IP \(bu 2
integrity: A \fBsha512\fP or \fBsha1\fP Standard Subresource
Integrity \fIhttps://w3c\.github\.io/webappsec/specs/subresourceintegrity/\fR
string for the artifact that was unpacked in this location\.  For git
dependencies, this is the commit sha\.
.IP \(bu 2
resolved: For registry sources this is path of the tarball relative to
the registry URL\.  If the tarball URL isn't on the same server as the
registry URL then this is a complete URL\. \fBregistry\.npmjs\.org\fP is a magic
value meaning "the currently configured registry"\.
.IP \(bu 2
bundled:  If true, this is the bundled dependency and will be installed
by the parent module\.  When installing, this module will be extracted
from the parent module during the extract phase, not installed as a
separate dependency\.
.IP \(bu 2
dev: If true then this dependency is either a development dependency ONLY
of the top level module or a transitive dependency of one\.  This is false
for dependencies that are both a development dependency of the top level
and a transitive dependency of a non\-development dependency of the top
level\.
.IP \(bu 2
optional: If true then this dependency is either an optional dependency
ONLY of the top level module or a transitive dependency of one\.  This is
false for dependencies that are both an optional dependency of the top
level and a transitive dependency of a non\-optional dependency of the top
level\.
.IP \(bu 2
requires: This is a mapping of module name to version\.  This is a list of
everything this module requires, regardless of where it will be
installed\.  The version should match via normal matching rules a
dependency either in our \fBdependencies\fP or in a level higher than us\.
.IP \(bu 2
dependencies: The dependencies of this dependency, exactly as at the top
level\.

.RE
.SS See also
.RS 0
.IP \(bu 2
npm help shrinkwrap
.IP \(bu 2
npm help npm\-shrinkwrap\.json
.IP \(bu 2
npm help package\.json
.IP \(bu 2
npm help install

.RE
