.TH "NPM\-PREFIX" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-prefix\fR \- Display prefix
.SS Synopsis
.P
.RS 2
.nf
npm prefix [\-g]
.fi
.RE
.P
Note: This command is unaware of workspaces\.
.SS Description
.P
Print the local prefix to standard output\. This is the closest parent directory
to contain a \fBpackage\.json\fP file or \fBnode_modules\fP directory, unless \fB\-g\fP is
also specified\.
.P
If \fB\-g\fP is specified, this will be the value of the global prefix\. See
npm help \fBconfig\fP for more detail\.
.SS Example
.P
.RS 2
.nf
npm prefix
/usr/local/projects/foo
.fi
.RE
.P
.RS 2
.nf
npm prefix \-g
/usr/local
.fi
.RE
.SS Configuration
.SS \fBglobal\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Operates in "global" mode, so that packages are installed into the \fBprefix\fP
folder instead of the current working directory\. See
npm help folders for more on the differences in behavior\.
.RS 0
.IP \(bu 2
packages are installed into the \fB{prefix}/lib/node_modules\fP folder, instead
of the current working directory\.
.IP \(bu 2
bin files are linked to \fB{prefix}/bin\fP
.IP \(bu 2
man pages are linked to \fB{prefix}/share/man\fP

.RE
.SS See Also
.RS 0
.IP \(bu 2
npm help root
.IP \(bu 2
npm help bin
.IP \(bu 2
npm help folders
.IP \(bu 2
npm help config
.IP \(bu 2
npm help npmrc

.RE
