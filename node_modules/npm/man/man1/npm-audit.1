.TH "NPM\-AUDIT" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-audit\fR \- Run a security audit
.SS Synopsis
.P
.RS 2
.nf
npm audit [fix|signatures]
.fi
.RE
.SS Description
.P
The audit command submits a description of the dependencies configured in
your project to your default registry and asks for a report of known
vulnerabilities\.  If any vulnerabilities are found, then the impact and
appropriate remediation will be calculated\.  If the \fBfix\fP argument is
provided, then remediations will be applied to the package tree\.
.P
The command will exit with a 0 exit code if no vulnerabilities were found\.
.P
Note that some vulnerabilities cannot be fixed automatically and will
require manual intervention or review\.  Also note that since \fBnpm audit
fix\fP runs a full\-fledged \fBnpm install\fP under the hood, all configs that
apply to the installer will also apply to \fBnpm install\fP \-\- so things like
\fBnpm audit fix \-\-package\-lock\-only\fP will work as expected\.
.P
By default, the audit command will exit with a non\-zero code if any
vulnerability is found\. It may be useful in CI environments to include the
\fB\-\-audit\-level\fP parameter to specify the minimum vulnerability level that
will cause the command to fail\. This option does not filter the report
output, it simply changes the command's failure threshold\.
.SS Audit Signatures
.P
To ensure the integrity of packages you download from the public npm registry, or any registry that supports signatures, you can verify the registry signatures of downloaded packages using the npm CLI\.
.P
Registry signatures can be verified using the following \fBaudit\fP command:
.P
.RS 2
.nf
$ npm audit signatures
.fi
.RE
.P
The npm CLI supports registry signatures and signing keys provided by any registry if the following conventions are followed:
.RS 0
.IP 1. 3
Signatures are provided in the package's \fBpackument\fP in each published version within the \fBdist\fP object:

.RE
.P
.RS 2
.nf
"dist":{
  "\.\.omitted\.\.": "\.\.omitted\.\.",
  "signatures": [{
    "keyid": "SHA256:{{SHA256_PUBLIC_KEY}}",
    "sig": "a312b9c3cb4a1b693e8ebac5ee1ca9cc01f2661c14391917dcb111517f72370809\.\.\."
  }]
}
.fi
.RE
.P
See this example \fIhttps://registry\.npmjs\.org/light\-cycle/1\.4\.3\fR of a signed package from the public npm registry\.
.P
The \fBsig\fP is generated using the following template: \fB${package\.name}@${package\.version}:${package\.dist\.integrity}\fP and the \fBkeyid\fP has to match one of the public signing keys below\.
.RS 0
.IP 1. 3
Public signing keys are provided at \fBregistry\-host\.tld/\-/npm/v1/keys\fP in the following format:

.RE
.P
.RS 2
.nf
{
  "keys": [{
    "expires": null,
    "keyid": "SHA256:{{SHA256_PUBLIC_KEY}}",
    "keytype": "ecdsa\-sha2\-nistp256",
    "scheme": "ecdsa\-sha2\-nistp256",
    "key": "{{B64_PUBLIC_KEY}}"
  }]
}
.fi
.RE
.P
Keys response:
.RS 0
.IP \(bu 2
\fBexpires\fP: null or a simplified extended <a href="https://en\.wikipedia\.org/wiki/ISO_8601" target="_blank">ISO 8601 format</a>: \fBYYYY\-MM\-DDTHH:mm:ss\.sssZ\fP
.IP \(bu 2
\fBkeydid\fP: sha256 fingerprint of the public key
.IP \(bu 2
\fBkeytype\fP: only \fBecdsa\-sha2\-nistp256\fP is currently supported by the npm CLI
.IP \(bu 2
\fBscheme\fP: only \fBecdsa\-sha2\-nistp256\fP is currently supported by the npm CLI
.IP \(bu 2
\fBkey\fP: base64 encoded public key

.RE
.P
See this <a href="https://registry\.npmjs\.org/\-/npm/v1/keys" target="_blank">example key's response from the public npm registry</a>\|\.
.SS Audit Endpoints
.P
There are two audit endpoints that npm may use to fetch vulnerability
information: the \fBBulk Advisory\fP endpoint and the \fBQuick Audit\fP endpoint\.
.SS Bulk Advisory Endpoint
.P
As of version 7, npm uses the much faster \fBBulk Advisory\fP endpoint to
optimize the speed of calculating audit results\.
.P
npm will generate a JSON payload with the name and list of versions of each
package in the tree, and POST it to the default configured registry at
the path \fB/\-/npm/v1/security/advisories/bulk\fP\|\.
.P
Any packages in the tree that do not have a \fBversion\fP field in their
package\.json file will be ignored\.  If any \fB\-\-omit\fP options are specified
(either via the \fB\-\-omit\fP config, or one of the shorthands such as
\fB\-\-production\fP, \fB\-\-only=dev\fP, and so on), then packages will be omitted
from the submitted payload as appropriate\.
.P
If the registry responds with an error, or with an invalid response, then
npm will attempt to load advisory data from the \fBQuick Audit\fP endpoint\.
.P
The expected result will contain a set of advisory objects for each
dependency that matches the advisory range\.  Each advisory object contains
a \fBname\fP, \fBurl\fP, \fBid\fP, \fBseverity\fP, \fBvulnerable_versions\fP, and \fBtitle\fP\|\.
.P
npm then uses these advisory objects to calculate vulnerabilities and
meta\-vulnerabilities of the dependencies within the tree\.
.SS Quick Audit Endpoint
.P
If the \fBBulk Advisory\fP endpoint returns an error, or invalid data, npm will
attempt to load advisory data from the \fBQuick Audit\fP endpoint, which is
considerably slower in most cases\.
.P
The full package tree as found in \fBpackage\-lock\.json\fP is submitted, along
with the following pieces of additional metadata:
.RS 0
.IP \(bu 2
\fBnpm_version\fP
.IP \(bu 2
\fBnode_version\fP
.IP \(bu 2
\fBplatform\fP
.IP \(bu 2
\fBarch\fP
.IP \(bu 2
\fBnode_env\fP

.RE
.P
All packages in the tree are submitted to the Quick Audit endpoint\.
Omitted dependency types are skipped when generating the report\.
.SS Scrubbing
.P
Out of an abundance of caution, npm versions 5 and 6 would "scrub" any
packages from the submitted report if their name contained a \fB/\fP character,
so as to avoid leaking the names of potentially private packages or git
URLs\.
.P
However, in practice, this resulted in audits often failing to properly
detect meta\-vulnerabilities, because the tree would appear to be invalid
due to missing dependencies, and prevented the detection of vulnerabilities
in package trees that used git dependencies or private modules\.
.P
This scrubbing has been removed from npm as of version 7\.
.SS Calculating Meta\-Vulnerabilities and Remediations
.P
npm uses the
\fB@npmcli/metavuln\-calculator\fP \fIhttp://npm\.im/@npmcli/metavuln\-calculator\fR
module to turn a set of security advisories into a set of "vulnerability"
objects\.  A "meta\-vulnerability" is a dependency that is vulnerable by
virtue of dependence on vulnerable versions of a vulnerable package\.
.P
For example, if the package \fBfoo\fP is vulnerable in the range \fB>=1\.0\.2
<2\.0\.0\fP, and the package \fBbar\fP depends on \fBfoo@^1\.1\.0\fP, then that version
of \fBbar\fP can only be installed by installing a vulnerable version of \fBfoo\fP\|\.
In this case, \fBbar\fP is a "metavulnerability"\.
.P
Once metavulnerabilities for a given package are calculated, they are
cached in the \fB~/\.npm\fP folder and only re\-evaluated if the advisory range
changes, or a new version of the package is published (in which case, the
new version is checked for metavulnerable status as well)\.
.P
If the chain of metavulnerabilities extends all the way to the root
project, and it cannot be updated without changing its dependency ranges,
then \fBnpm audit fix\fP will require the \fB\-\-force\fP option to apply the
remediation\.  If remediations do not require changes to the dependency
ranges, then all vulnerable packages will be updated to a version that does
not have an advisory or metavulnerability posted against it\.
.SS Exit Code
.P
The \fBnpm audit\fP command will exit with a 0 exit code if no vulnerabilities
were found\.  The \fBnpm audit fix\fP command will exit with 0 exit code if no
vulnerabilities are found \fIor\fR if the remediation is able to successfully
fix all vulnerabilities\.
.P
If vulnerabilities were found the exit code will depend on the
\fBaudit\-level\fP configuration setting\.
.SS Examples
.P
Scan your project for vulnerabilities and automatically install any compatible
updates to vulnerable dependencies:
.P
.RS 2
.nf
$ npm audit fix
.fi
.RE
.P
Run \fBaudit fix\fP without modifying \fBnode_modules\fP, but still updating the
pkglock:
.P
.RS 2
.nf
$ npm audit fix \-\-package\-lock\-only
.fi
.RE
.P
Skip updating \fBdevDependencies\fP:
.P
.RS 2
.nf
$ npm audit fix \-\-only=prod
.fi
.RE
.P
Have \fBaudit fix\fP install SemVer\-major updates to toplevel dependencies, not
just SemVer\-compatible ones:
.P
.RS 2
.nf
$ npm audit fix \-\-force
.fi
.RE
.P
Do a dry run to get an idea of what \fBaudit fix\fP will do, and \fIalso\fR output
install information in JSON format:
.P
.RS 2
.nf
$ npm audit fix \-\-dry\-run \-\-json
.fi
.RE
.P
Scan your project for vulnerabilities and just show the details, without
fixing anything:
.P
.RS 2
.nf
$ npm audit
.fi
.RE
.P
Get the detailed audit report in JSON format:
.P
.RS 2
.nf
$ npm audit \-\-json
.fi
.RE
.P
Fail an audit only if the results include a vulnerability with a level of moderate or higher:
.P
.RS 2
.nf
$ npm audit \-\-audit\-level=moderate
.fi
.RE
.SS Configuration
.SS \fBaudit\-level\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null, "info", "low", "moderate", "high", "critical", or "none"

.RE
.P
The minimum level of vulnerability for \fBnpm audit\fP to exit with a non\-zero
exit code\.
.SS \fBdry\-run\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Indicates that you don't want npm to make any changes and that it should
only report what it would have done\. This can be passed into any of the
commands that modify your local installation, eg, \fBinstall\fP, \fBupdate\fP,
\fBdedupe\fP, \fBuninstall\fP, as well as \fBpack\fP and \fBpublish\fP\|\.
.P
Note: This is NOT honored by other network related commands, eg \fBdist\-tags\fP,
\fBowner\fP, etc\.
.SS \fBforce\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Removes various protections against unfortunate side effects, common
mistakes, unnecessary performance degradation, and malicious input\.
.RS 0
.IP \(bu 2
Allow clobbering non\-npm files in global installs\.
.IP \(bu 2
Allow the \fBnpm version\fP command to work on an unclean git repository\.
.IP \(bu 2
Allow deleting the cache folder with \fBnpm cache clean\fP\|\.
.IP \(bu 2
Allow installing packages that have an \fBengines\fP declaration requiring a
different version of npm\.
.IP \(bu 2
Allow installing packages that have an \fBengines\fP declaration requiring a
different version of \fBnode\fP, even if \fB\-\-engine\-strict\fP is enabled\.
.IP \(bu 2
Allow \fBnpm audit fix\fP to install modules outside your stated dependency
range (including SemVer\-major changes)\.
.IP \(bu 2
Allow unpublishing all versions of a published package\.
.IP \(bu 2
Allow conflicting peerDependencies to be installed in the root project\.
.IP \(bu 2
Implicitly set \fB\-\-yes\fP during \fBnpm init\fP\|\.
.IP \(bu 2
Allow clobbering existing values in \fBnpm pkg\fP
.IP \(bu 2
Allow unpublishing of entire packages (not just a single version)\.

.RE
.P
If you don't have a clear idea of what you want to do, it is strongly
recommended that you do not use this option!
.SS \fBjson\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Whether or not to output JSON data, rather than the normal output\.
.RS 0
.IP \(bu 2
In \fBnpm pkg set\fP it enables parsing set values with JSON\.parse() before
saving them to your \fBpackage\.json\fP\|\.

.RE
.P
Not supported by all npm commands\.
.SS \fBpackage\-lock\-only\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
If set to true, the current operation will only use the \fBpackage\-lock\.json\fP,
ignoring \fBnode_modules\fP\|\.
.P
For \fBupdate\fP this means only the \fBpackage\-lock\.json\fP will be updated,
instead of checking \fBnode_modules\fP and downloading dependencies\.
.P
For \fBlist\fP this means the output will be based on the tree described by the
\fBpackage\-lock\.json\fP, rather than the contents of \fBnode_modules\fP\|\.
.SS \fBomit\fP
.RS 0
.IP \(bu 2
Default: 'dev' if the \fBNODE_ENV\fP environment variable is set to
\|'production', otherwise empty\.
.IP \(bu 2
Type: "dev", "optional", or "peer" (can be set multiple times)

.RE
.P
Dependency types to omit from the installation tree on disk\.
.P
Note that these dependencies \fIare\fR still resolved and added to the
\fBpackage\-lock\.json\fP or \fBnpm\-shrinkwrap\.json\fP file\. They are just not
physically installed on disk\.
.P
If a package type appears in both the \fB\-\-include\fP and \fB\-\-omit\fP lists, then
it will be included\.
.P
If the resulting omit list includes \fB\|'dev'\fP, then the \fBNODE_ENV\fP environment
variable will be set to \fB\|'production'\fP for all lifecycle scripts\.
.SS \fBforeground\-scripts\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Run all build scripts (ie, \fBpreinstall\fP, \fBinstall\fP, and \fBpostinstall\fP)
scripts for installed packages in the foreground process, sharing standard
input, output, and error with the main npm process\.
.P
Note that this will generally make installs run slower, and be much noisier,
but can be useful for debugging\.
.SS \fBignore\-scripts\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
If true, npm does not run scripts specified in package\.json files\.
.P
Note that commands explicitly intended to run a particular script, such as
\fBnpm start\fP, \fBnpm stop\fP, \fBnpm restart\fP, \fBnpm test\fP, and \fBnpm run\-script\fP
will still run their intended script if \fBignore\-scripts\fP is set, but they
will \fInot\fR run any pre\- or post\-scripts\.
.SS \fBworkspace\fP
.RS 0
.IP \(bu 2
Default:
.IP \(bu 2
Type: String (can be set multiple times)

.RE
.P
Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option\.
.P
Valid values for the \fBworkspace\fP config are either:
.RS 0
.IP \(bu 2
Workspace names
.IP \(bu 2
Path to a workspace directory
.IP \(bu 2
Path to a parent workspace directory (will result in selecting all
workspaces within that folder)

.RE
.P
When set for the \fBnpm init\fP command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project\.
.P
This value is not exported to the environment for child processes\.
.SS \fBworkspaces\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or Boolean

.RE
.P
Set to true to run the command in the context of \fBall\fR configured
workspaces\.
.P
Explicitly setting this to false will cause commands like \fBinstall\fP to
ignore workspaces altogether\. When not set explicitly:
.RS 0
.IP \(bu 2
Commands that operate on the \fBnode_modules\fP tree (install, update, etc\.)
will link workspaces into the \fBnode_modules\fP folder\. \- Commands that do
other things (test, exec, publish, etc\.) will operate on the root project,
\fIunless\fR one or more workspaces are specified in the \fBworkspace\fP config\.

.RE
.P
This value is not exported to the environment for child processes\.
.SS \fBinclude\-workspace\-root\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Include the workspace root when workspaces are enabled for a command\.
.P
When false, specifying individual workspaces via the \fBworkspace\fP config, or
all workspaces via the \fBworkspaces\fP flag, will cause npm to operate only on
the specified workspaces, and not on the root project\.
.P
This value is not exported to the environment for child processes\.
.SS \fBinstall\-links\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
When set file: protocol dependencies that exist outside of the project root
will be packed and installed as regular dependencies instead of creating a
symlink\. This option has no effect on workspaces\.
.SS See Also
.RS 0
.IP \(bu 2
npm help install
.IP \(bu 2
npm help config

.RE
