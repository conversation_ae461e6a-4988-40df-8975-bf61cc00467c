.TH "NPM\-TOKEN" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-token\fR \- Manage your authentication tokens
.SS Synopsis
.P
.RS 2
.nf
npm token list
npm token revoke <id|token>
npm token create [\-\-read\-only] [\-\-cidr=list]
.fi
.RE
.P
Note: This command is unaware of workspaces\.
.SS Description
.P
This lets you list, create and revoke authentication tokens\.
.RS 0
.IP \(bu 2
\fBnpm token list\fP:
Shows a table of all active authentication tokens\. You can request
this as JSON with \fB\-\-json\fP or tab\-separated values with \fB\-\-parseable\fP\|\.

.RE
.P
.RS 2
.nf
+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| id     | token   | created    | read\-only | CIDR whitelist |
+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| 7f3134 | 1fa9ba… | 2017\-10\-02 | yes      |                |
+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| c03241 | af7aef… | 2017\-10\-02 | no       | 192\.168\.0\.1/24 |
+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| e0cf92 | 3a436a… | 2017\-10\-02 | no       |                |
+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| 63eb9d | 74ef35… | 2017\-09\-28 | no       |                |
+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| 2daaa8 | cbad5f… | 2017\-09\-26 | no       |                |
+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| 68c2fe | 127e51… | 2017\-09\-23 | no       |                |
+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| 6334e1 | 1dadd1… | 2017\-09\-23 | no       |                |
+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
.fi
.RE
.RS 0
.IP \(bu 2
\fBnpm token create [\-\-read\-only] [\-\-cidr=<cidr\-ranges>]\fP:
Create a new authentication token\. It can be \fB\-\-read\-only\fP, or accept
a list of
CIDR \fIhttps://en\.wikipedia\.org/wiki/Classless_Inter\-Domain_Routing\fR
ranges with which to limit use of this token\. This will prompt you for
your password, and, if you have two\-factor authentication enabled, an
otp\.
Currently, the cli can not generate automation tokens\. Please refer to
the docs
website \fIhttps://docs\.npmjs\.com/creating\-and\-viewing\-access\-tokens\fR
for more information on generating automation tokens\.

.RE
.P
.RS 2
.nf
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| token          | a73c9572\-f1b9\-8983\-983d\-ba3ac3cc913d |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| cidr_whitelist |                                      |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| readonly       | false                                |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| created        | 2017\-10\-02T07:52:24\.838Z             |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
.fi
.RE
.RS 0
.IP \(bu 2
\fBnpm token revoke <token|id>\fP:
Immediately removes an authentication token from the registry\.  You
will no longer be able to use it\.  This can accept both complete
tokens (such as those you get back from \fBnpm token create\fP, and those
found in your \fB\|\.npmrc\fP), and ids as seen in the parseable or json
output of \fBnpm token list\fP\|\.  This will NOT accept the truncated token
found in the normal \fBnpm token list\fP output\.

.RE
.SS Configuration
.SS \fBread\-only\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
This is used to mark a token as unable to publish when configuring limited
access tokens with the \fBnpm token create\fP command\.
.SS \fBcidr\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or String (can be set multiple times)

.RE
.P
This is a list of CIDR address to be used when configuring limited access
tokens with the \fBnpm token create\fP command\.
.SS \fBregistry\fP
.RS 0
.IP \(bu 2
Default: "https://registry\.npmjs\.org/"
.IP \(bu 2
Type: URL

.RE
.P
The base URL of the npm registry\.
.SS \fBotp\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or String

.RE
.P
This is a one\-time password from a two\-factor authenticator\. It's needed
when publishing or changing package permissions with \fBnpm access\fP\|\.
.P
If not set, and a registry response fails with a challenge for a one\-time
password, npm will prompt on the command line for one\.
.SS See Also
.RS 0
.IP \(bu 2
npm help adduser
.IP \(bu 2
npm help registry
.IP \(bu 2
npm help config
.IP \(bu 2
npm help npmrc
.IP \(bu 2
npm help owner
.IP \(bu 2
npm help whoami
.IP \(bu 2
npm help profile

.RE
