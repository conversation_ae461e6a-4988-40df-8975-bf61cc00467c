.TH "NPM\-RUN\-SCRIPT" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-run-script\fR \- Run arbitrary package scripts
.SS Synopsis
.P
.RS 2
.nf
npm run\-script <command> [\-\- <args>]

aliases: run, rum, urn
.fi
.RE
.SS Description
.P
This runs an arbitrary command from a package's \fB"scripts"\fP object\.  If no
\fB"command"\fP is provided, it will list the available scripts\.
.P
\fBrun[\-script]\fP is used by the test, start, restart, and stop commands, but
can be called directly, as well\. When the scripts in the package are
printed out, they're separated into lifecycle (test, start, restart) and
directly\-run scripts\.
.P
Any positional arguments are passed to the specified script\.  Use \fB\-\-\fP to
pass \fB\-\fP\-prefixed flags and options which would otherwise be parsed by npm\.
.P
For example:
.P
.RS 2
.nf
npm run test \-\- \-\-grep="pattern"
.fi
.RE
.P
The arguments will only be passed to the script specified after \fBnpm run\fP
and not to any \fBpre\fP or \fBpost\fP script\.
.P
The \fBenv\fP script is a special built\-in command that can be used to list
environment variables that will be available to the script at runtime\. If an
"env" command is defined in your package, it will take precedence over the
built\-in\.
.P
In addition to the shell's pre\-existing \fBPATH\fP, \fBnpm run\fP adds
\fBnode_modules/\.bin\fP to the \fBPATH\fP provided to scripts\. Any binaries
provided by locally\-installed dependencies can be used without the
\fBnode_modules/\.bin\fP prefix\. For example, if there is a \fBdevDependency\fP on
\fBtap\fP in your package, you should write:
.P
.RS 2
.nf
"scripts": {"test": "tap test/*\.js"}
.fi
.RE
.P
instead of
.P
.RS 2
.nf
"scripts": {"test": "node_modules/\.bin/tap test/*\.js"}
.fi
.RE
.P
The actual shell your script is run within is platform dependent\. By default,
on Unix\-like systems it is the \fB/bin/sh\fP command, on Windows it is
\fBcmd\.exe\fP\|\.
The actual shell referred to by \fB/bin/sh\fP also depends on the system\.
You can customize the shell with the \fBscript\-shell\fP configuration\.
.P
Scripts are run from the root of the package folder, regardless of what the
current working directory is when \fBnpm run\fP is called\. If you want your
script to use different behavior based on what subdirectory you're in, you
can use the \fBINIT_CWD\fP environment variable, which holds the full path you
were in when you ran \fBnpm run\fP\|\.
.P
\fBnpm run\fP sets the \fBNODE\fP environment variable to the \fBnode\fP executable
with which \fBnpm\fP is executed\.
.P
If you try to run a script without having a \fBnode_modules\fP directory and it
fails, you will be given a warning to run \fBnpm install\fP, just in case you've
forgotten\.
.SS Workspaces support
.P
You may use the \fBworkspace\fP or \fBworkspaces\fP configs in order to run an
arbitrary command from a package's \fB"scripts"\fP object in the context of the
specified workspaces\. If no \fB"command"\fP is provided, it will list the available
scripts for each of these configured workspaces\.
.P
Given a project with configured workspaces, e\.g:
.P
.RS 2
.nf
\|\.
+\-\- package\.json
`\-\- packages
   +\-\- a
   |   `\-\- package\.json
   +\-\- b
   |   `\-\- package\.json
   `\-\- c
       `\-\- package\.json
.fi
.RE
.P
Assuming the workspace configuration is properly set up at the root level
\fBpackage\.json\fP file\. e\.g:
.P
.RS 2
.nf
{
    "workspaces": [ "\./packages/*" ]
}
.fi
.RE
.P
And that each of the configured workspaces has a configured \fBtest\fP script,
we can run tests in all of them using the \fBworkspaces\fP config:
.P
.RS 2
.nf
npm test \-\-workspaces
.fi
.RE
.SS Filtering workspaces
.P
It's also possible to run a script in a single workspace using the \fBworkspace\fP
config along with a name or directory path:
.P
.RS 2
.nf
npm test \-\-workspace=a
.fi
.RE
.P
The \fBworkspace\fP config can also be specified multiple times in order to run a
specific script in the context of multiple workspaces\. When defining values for
the \fBworkspace\fP config in the command line, it also possible to use \fB\-w\fP as a
shorthand, e\.g:
.P
.RS 2
.nf
npm test \-w a \-w b
.fi
.RE
.P
This last command will run \fBtest\fP in both \fB\|\./packages/a\fP and \fB\|\./packages/b\fP
packages\.
.SS Configuration
.SS \fBworkspace\fP
.RS 0
.IP \(bu 2
Default:
.IP \(bu 2
Type: String (can be set multiple times)

.RE
.P
Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option\.
.P
Valid values for the \fBworkspace\fP config are either:
.RS 0
.IP \(bu 2
Workspace names
.IP \(bu 2
Path to a workspace directory
.IP \(bu 2
Path to a parent workspace directory (will result in selecting all
workspaces within that folder)

.RE
.P
When set for the \fBnpm init\fP command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project\.
.P
This value is not exported to the environment for child processes\.
.SS \fBworkspaces\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or Boolean

.RE
.P
Set to true to run the command in the context of \fBall\fR configured
workspaces\.
.P
Explicitly setting this to false will cause commands like \fBinstall\fP to
ignore workspaces altogether\. When not set explicitly:
.RS 0
.IP \(bu 2
Commands that operate on the \fBnode_modules\fP tree (install, update, etc\.)
will link workspaces into the \fBnode_modules\fP folder\. \- Commands that do
other things (test, exec, publish, etc\.) will operate on the root project,
\fIunless\fR one or more workspaces are specified in the \fBworkspace\fP config\.

.RE
.P
This value is not exported to the environment for child processes\.
.SS \fBinclude\-workspace\-root\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Include the workspace root when workspaces are enabled for a command\.
.P
When false, specifying individual workspaces via the \fBworkspace\fP config, or
all workspaces via the \fBworkspaces\fP flag, will cause npm to operate only on
the specified workspaces, and not on the root project\.
.P
This value is not exported to the environment for child processes\.
.SS \fBif\-present\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
If true, npm will not exit with an error code when \fBrun\-script\fP is invoked
for a script that isn't defined in the \fBscripts\fP section of \fBpackage\.json\fP\|\.
This option can be used when it's desirable to optionally run a script when
it's present and fail if the script fails\. This is useful, for example, when
running scripts that may only apply for some builds in an otherwise generic
CI setup\.
.P
This value is not exported to the environment for child processes\.
.SS \fBignore\-scripts\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
If true, npm does not run scripts specified in package\.json files\.
.P
Note that commands explicitly intended to run a particular script, such as
\fBnpm start\fP, \fBnpm stop\fP, \fBnpm restart\fP, \fBnpm test\fP, and \fBnpm run\-script\fP
will still run their intended script if \fBignore\-scripts\fP is set, but they
will \fInot\fR run any pre\- or post\-scripts\.
.SS \fBforeground\-scripts\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Run all build scripts (ie, \fBpreinstall\fP, \fBinstall\fP, and \fBpostinstall\fP)
scripts for installed packages in the foreground process, sharing standard
input, output, and error with the main npm process\.
.P
Note that this will generally make installs run slower, and be much noisier,
but can be useful for debugging\.
.SS \fBscript\-shell\fP
.RS 0
.IP \(bu 2
Default: '/bin/sh' on POSIX systems, 'cmd\.exe' on Windows
.IP \(bu 2
Type: null or String

.RE
.P
The shell to use for scripts run with the \fBnpm exec\fP, \fBnpm run\fP and \fBnpm
init <package\-spec>\fP commands\.
.SS See Also
.RS 0
.IP \(bu 2
npm help scripts
.IP \(bu 2
npm help test
.IP \(bu 2
npm help start
.IP \(bu 2
npm help restart
.IP \(bu 2
npm help stop
.IP \(bu 2
npm help config
.IP \(bu 2
npm help workspaces

.RE
