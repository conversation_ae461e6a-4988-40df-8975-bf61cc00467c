.TH "NPM\-PKG" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-pkg\fR \- Manages your package\.json
.SS Synopsis
.P
.RS 2
.nf
npm pkg set <key>=<value> [<key>=<value> \.\.\.]
npm pkg get [<key> [<key> \.\.\.]]
npm pkg delete <key> [<key> \.\.\.]
npm pkg set [<array>[<index>]\.<key>=<value> \.\.\.]
npm pkg set [<array>[]\.<key>=<value> \.\.\.]
.fi
.RE
.SS Description
.P
A command that automates the management of \fBpackage\.json\fP files\.
\fBnpm pkg\fP provide 3 different sub commands that allow you to modify or retrieve
values for given object keys in your \fBpackage\.json\fP\|\.
.P
The syntax to retrieve and set fields is a dot separated representation of
the nested object properties to be found within your \fBpackage\.json\fP, it's the
same notation used in npm help \fBview\fP to retrieve information
from the registry manifest, below you can find more examples on how to use it\.
.P
Returned values are always in \fBjson\fR format\.
.RS 0
.IP \(bu 2
\fBnpm pkg get <field>\fP
  Retrieves a value \fBkey\fP, defined in your \fBpackage\.json\fP file\.
  For example, in order to retrieve the name of the current package, you
  can run:
.P
.RS 2
.nf
  npm pkg get name
.fi
.RE
  It's also possible to retrieve multiple values at once:
.P
.RS 2
.nf
  npm pkg get name version
.fi
.RE
  You can view child fields by separating them with a period\. To retrieve
  the value of a test \fBscript\fP value, you would run the following command:
.P
.RS 2
.nf
  npm pkg get scripts\.test
.fi
.RE
  For fields that are arrays, requesting a non\-numeric field will return
  all of the values from the objects in the list\. For example, to get all
  the contributor emails for a package, you would run:
.P
.RS 2
.nf
  npm pkg get contributors\.email
.fi
.RE
  You may also use numeric indices in square braces to specifically select
  an item in an array field\. To just get the email address of the first
  contributor in the list, you can run:
.P
.RS 2
.nf
  npm pkg get contributors[0]\.email
.fi
.RE
  For complex fields you can also name a property in square brackets
  to specifically select a child field\. This is especially helpful
  with the exports object:
.P
.RS 2
.nf
  npm pkg get "exports[\.]\.require"
.fi
.RE
.IP \(bu 2
\fBnpm pkg set <field>=<value>\fP
  Sets a \fBvalue\fP in your \fBpackage\.json\fP based on the \fBfield\fP value\. When
  saving to your \fBpackage\.json\fP file the same set of rules used during
  \fBnpm install\fP and other cli commands that touches the \fBpackage\.json\fP file
  are used, making sure to respect the existing indentation and possibly
  applying some validation prior to saving values to the file\.
  The same syntax used to retrieve values from your package can also be used
  to define new properties or overriding existing ones, below are some
  examples of how the dot separated syntax can be used to edit your
  \fBpackage\.json\fP file\.
  Defining a new bin named \fBmynewcommand\fP in your \fBpackage\.json\fP that points
  to a file \fBcli\.js\fP:
.P
.RS 2
.nf
  npm pkg set bin\.mynewcommand=cli\.js
.fi
.RE
  Setting multiple fields at once is also possible:
.P
.RS 2
.nf
  npm pkg set description='Awesome package' engines\.node='>=10'
.fi
.RE
  It's also possible to add to array values, for example to add a new
  contributor entry:
.P
.RS 2
.nf
  npm pkg set contributors[0]\.name='Foo' contributors[0]\.email='foo@bar\.ca'
.fi
.RE
  You may also append items to the end of an array using the special
  empty bracket notation:
.P
.RS 2
.nf
  npm pkg set contributors[]\.name='Foo' contributors[]\.name='Bar'
.fi
.RE
  It's also possible to parse values as json prior to saving them to your
  \fBpackage\.json\fP file, for example in order to set a \fB"private": true\fP
  property:
.P
.RS 2
.nf
  npm pkg set private=true \-\-json
.fi
.RE
  It also enables saving values as numbers:
.P
.RS 2
.nf
  npm pkg set tap\.timeout=60 \-\-json
.fi
.RE
.IP \(bu 2
\fBnpm pkg delete <key>\fP
  Deletes a \fBkey\fP from your \fBpackage\.json\fP
  The same syntax used to set values from your package can also be used
  to remove existing ones\. For example, in order to remove a script named
  build:
.P
.RS 2
.nf
  npm pkg delete scripts\.build
.fi
.RE

.RE
.SS Workspaces support
.P
You can set/get/delete items across your configured workspaces by using the
\fBworkspace\fP or \fBworkspaces\fP config options\.
.P
For example, setting a \fBfunding\fP value across all configured workspaces
of a project:
.P
.RS 2
.nf
npm pkg set funding=https://example\.com \-\-ws
.fi
.RE
.P
When using \fBnpm pkg get\fP to retrieve info from your configured workspaces, the
returned result will be in a json format in which top level keys are the
names of each workspace, the values of these keys will be the result values
returned from each of the configured workspaces, e\.g:
.P
.RS 2
.nf
npm pkg get name version \-\-ws
{
  "a": {
    "name": "a",
    "version": "1\.0\.0"
  },
  "b": {
    "name": "b",
    "version": "1\.0\.0"
  }
}
.fi
.RE
.SS Configuration
.SS \fBforce\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Removes various protections against unfortunate side effects, common
mistakes, unnecessary performance degradation, and malicious input\.
.RS 0
.IP \(bu 2
Allow clobbering non\-npm files in global installs\.
.IP \(bu 2
Allow the \fBnpm version\fP command to work on an unclean git repository\.
.IP \(bu 2
Allow deleting the cache folder with \fBnpm cache clean\fP\|\.
.IP \(bu 2
Allow installing packages that have an \fBengines\fP declaration requiring a
different version of npm\.
.IP \(bu 2
Allow installing packages that have an \fBengines\fP declaration requiring a
different version of \fBnode\fP, even if \fB\-\-engine\-strict\fP is enabled\.
.IP \(bu 2
Allow \fBnpm audit fix\fP to install modules outside your stated dependency
range (including SemVer\-major changes)\.
.IP \(bu 2
Allow unpublishing all versions of a published package\.
.IP \(bu 2
Allow conflicting peerDependencies to be installed in the root project\.
.IP \(bu 2
Implicitly set \fB\-\-yes\fP during \fBnpm init\fP\|\.
.IP \(bu 2
Allow clobbering existing values in \fBnpm pkg\fP
.IP \(bu 2
Allow unpublishing of entire packages (not just a single version)\.

.RE
.P
If you don't have a clear idea of what you want to do, it is strongly
recommended that you do not use this option!
.SS \fBjson\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Whether or not to output JSON data, rather than the normal output\.
.RS 0
.IP \(bu 2
In \fBnpm pkg set\fP it enables parsing set values with JSON\.parse() before
saving them to your \fBpackage\.json\fP\|\.

.RE
.P
Not supported by all npm commands\.
.SS \fBworkspace\fP
.RS 0
.IP \(bu 2
Default:
.IP \(bu 2
Type: String (can be set multiple times)

.RE
.P
Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option\.
.P
Valid values for the \fBworkspace\fP config are either:
.RS 0
.IP \(bu 2
Workspace names
.IP \(bu 2
Path to a workspace directory
.IP \(bu 2
Path to a parent workspace directory (will result in selecting all
workspaces within that folder)

.RE
.P
When set for the \fBnpm init\fP command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project\.
.P
This value is not exported to the environment for child processes\.
.SS \fBworkspaces\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or Boolean

.RE
.P
Set to true to run the command in the context of \fBall\fR configured
workspaces\.
.P
Explicitly setting this to false will cause commands like \fBinstall\fP to
ignore workspaces altogether\. When not set explicitly:
.RS 0
.IP \(bu 2
Commands that operate on the \fBnode_modules\fP tree (install, update, etc\.)
will link workspaces into the \fBnode_modules\fP folder\. \- Commands that do
other things (test, exec, publish, etc\.) will operate on the root project,
\fIunless\fR one or more workspaces are specified in the \fBworkspace\fP config\.

.RE
.P
This value is not exported to the environment for child processes\.
.SH See Also
.RS 0
.IP \(bu 2
npm help install
.IP \(bu 2
npm help init
.IP \(bu 2
npm help config
.IP \(bu 2
npm help set\-script
.IP \(bu 2
npm help workspaces

.RE
