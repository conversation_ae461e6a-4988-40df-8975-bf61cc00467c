.TH "NPM\-ORG" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-org\fR \- Manage orgs
.SS Synopsis
.P
.RS 2
.nf
npm org set orgname username [developer | admin | owner]
npm org rm orgname username
npm org ls orgname [<username>]

alias: ogr
.fi
.RE
.P
Note: This command is unaware of workspaces\.
.SS Example
.P
Add a new developer to an org:
.P
.RS 2
.nf
$ npm org set my\-org @mx\-smith
.fi
.RE
.P
Add a new admin to an org (or change a developer to an admin):
.P
.RS 2
.nf
$ npm org set my\-org @mx\-santos admin
.fi
.RE
.P
Remove a user from an org:
.P
.RS 2
.nf
$ npm org rm my\-org mx\-santos
.fi
.RE
.P
List all users in an org:
.P
.RS 2
.nf
$ npm org ls my\-org
.fi
.RE
.P
List all users in JSON format:
.P
.RS 2
.nf
$ npm org ls my\-org \-\-json
.fi
.RE
.P
See what role a user has in an org:
.P
.RS 2
.nf
$ npm org ls my\-org @mx\-santos
.fi
.RE
.SS Description
.P
You can use the \fBnpm org\fP commands to manage and view users of an
organization\.  It supports adding and removing users, changing their roles,
listing them, and finding specific ones and their roles\.
.SS Configuration
.SS \fBregistry\fP
.RS 0
.IP \(bu 2
Default: "https://registry\.npmjs\.org/"
.IP \(bu 2
Type: URL

.RE
.P
The base URL of the npm registry\.
.SS \fBotp\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or String

.RE
.P
This is a one\-time password from a two\-factor authenticator\. It's needed
when publishing or changing package permissions with \fBnpm access\fP\|\.
.P
If not set, and a registry response fails with a challenge for a one\-time
password, npm will prompt on the command line for one\.
.SS \fBjson\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Whether or not to output JSON data, rather than the normal output\.
.RS 0
.IP \(bu 2
In \fBnpm pkg set\fP it enables parsing set values with JSON\.parse() before
saving them to your \fBpackage\.json\fP\|\.

.RE
.P
Not supported by all npm commands\.
.SS \fBparseable\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Output parseable results from commands that write to standard output\. For
\fBnpm search\fP, this will be tab\-separated table format\.
.SS See Also
.RS 0
.IP \(bu 2
npm help using orgs
.IP \(bu 2
Documentation on npm Orgs \fIhttps://docs\.npmjs\.com/orgs/\fR

.RE
