.TH "NPM\-PROFILE" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-profile\fR \- Change settings on your registry profile
.SS Synopsis
.P
.RS 2
.nf
npm profile enable\-2fa [auth\-only|auth\-and\-writes]
npm profile disable\-2fa
npm profile get [<key>]
npm profile set <key> <value>
.fi
.RE
.P
Note: This command is unaware of workspaces\.
.SS Description
.P
Change your profile information on the registry\.  Note that this command
depends on the registry implementation, so third\-party registries may not
support this interface\.
.RS 0
.IP \(bu 2
\fBnpm profile get [<property>]\fP: Display all of the properties of your
profile, or one or more specific properties\.  It looks like:

.RE
.P
.RS 2
.nf
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| name            | example                   |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| email           | me@example\.com (verified) |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| two factor auth | auth\-and\-writes           |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| fullname        | Example User              |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| homepage        |                           |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| freenode        |                           |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| twitter         |                           |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| github          |                           |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| created         | 2015\-02\-26T01:38:35\.892Z  |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
| updated         | 2017\-10\-02T21:29:45\.922Z  |
+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
.fi
.RE
.RS 0
.IP \(bu 2
\fBnpm profile set <property> <value>\fP: Set the value of a profile
property\. You can set the following properties this way: email, fullname,
homepage, freenode, twitter, github
.IP \(bu 2
\fBnpm profile set password\fP: Change your password\.  This is interactive,
you'll be prompted for your current password and a new password\.  You'll
also be prompted for an OTP if you have two\-factor authentication
enabled\.
.IP \(bu 2
\fBnpm profile enable\-2fa [auth\-and\-writes|auth\-only]\fP: Enables two\-factor
authentication\. Defaults to \fBauth\-and\-writes\fP mode\. Modes are:
.RS
.IP \(bu 2
\fBauth\-only\fP: Require an OTP when logging in or making changes to your
account's authentication\.  The OTP will be required on both the website
and the command line\.
.IP \(bu 2
\fBauth\-and\-writes\fP: Requires an OTP at all the times \fBauth\-only\fP does,
and also requires one when publishing a module, setting the \fBlatest\fP
dist\-tag, or changing access via \fBnpm access\fP and \fBnpm owner\fP\|\.

.RE
.IP \(bu 2
\fBnpm profile disable\-2fa\fP: Disables two\-factor authentication\.

.RE
.SS Details
.P
Some of these commands may not be available on non npmjs\.com registries\.
.SS Configuration
.SS \fBregistry\fP
.RS 0
.IP \(bu 2
Default: "https://registry\.npmjs\.org/"
.IP \(bu 2
Type: URL

.RE
.P
The base URL of the npm registry\.
.SS \fBjson\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Whether or not to output JSON data, rather than the normal output\.
.RS 0
.IP \(bu 2
In \fBnpm pkg set\fP it enables parsing set values with JSON\.parse() before
saving them to your \fBpackage\.json\fP\|\.

.RE
.P
Not supported by all npm commands\.
.SS \fBparseable\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Output parseable results from commands that write to standard output\. For
\fBnpm search\fP, this will be tab\-separated table format\.
.SS \fBotp\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or String

.RE
.P
This is a one\-time password from a two\-factor authenticator\. It's needed
when publishing or changing package permissions with \fBnpm access\fP\|\.
.P
If not set, and a registry response fails with a challenge for a one\-time
password, npm will prompt on the command line for one\.
.SS See Also
.RS 0
.IP \(bu 2
npm help adduser
.IP \(bu 2
npm help registry
.IP \(bu 2
npm help config
.IP \(bu 2
npm help npmrc
.IP \(bu 2
npm help owner
.IP \(bu 2
npm help whoami
.IP \(bu 2
npm help token

.RE
