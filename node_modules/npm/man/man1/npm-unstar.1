.TH "NPM\-UNSTAR" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-unstar\fR \- Remove an item from your favorite packages
.SS Synopsis
.P
.RS 2
.nf
npm unstar [<package\-spec>\.\.\.]
.fi
.RE
.P
Note: This command is unaware of workspaces\.
.SS Description
.P
"Unstarring" a package is the opposite of npm help \fBstar\fP,
it removes an item from your list of favorite packages\.
.SS More
.P
There's also these extra commands to help you manage your favorite packages:
.SS Star
.P
You can "star" a package using npm help \fBstar\fP
.SS Listing stars
.P
You can see all your starred packages using npm help \fBstars\fP
.SS Configuration
.SS \fBregistry\fP
.RS 0
.IP \(bu 2
Default: "https://registry\.npmjs\.org/"
.IP \(bu 2
Type: URL

.RE
.P
The base URL of the npm registry\.
.SS \fBunicode\fP
.RS 0
.IP \(bu 2
Default: false on windows, true on mac/unix systems with a unicode locale,
as defined by the \fBLC_ALL\fP, \fBLC_CTYPE\fP, or \fBLANG\fP environment variables\.
.IP \(bu 2
Type: Boolean

.RE
.P
When set to true, npm uses unicode characters in the tree output\. When
false, it uses ascii characters instead of unicode glyphs\.
.SS \fBotp\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or String

.RE
.P
This is a one\-time password from a two\-factor authenticator\. It's needed
when publishing or changing package permissions with \fBnpm access\fP\|\.
.P
If not set, and a registry response fails with a challenge for a one\-time
password, npm will prompt on the command line for one\.
.SS See Also
.RS 0
.IP \(bu 2
npm help star
.IP \(bu 2
npm help stars
.IP \(bu 2
npm help view
.IP \(bu 2
npm help whoami
.IP \(bu 2
npm help adduser

.RE
