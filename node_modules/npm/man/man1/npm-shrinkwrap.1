.TH "NPM\-SHRINKWRAP" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-shrinkwrap\fR \- Lock down dependency versions for publication
.SS Synopsis
.P
.RS 2
.nf
npm shrinkwrap
.fi
.RE
.P
Note: This command is unaware of workspaces\.
.SS Description
.P
This command repurposes \fBpackage\-lock\.json\fP into a publishable
\fBnpm\-shrinkwrap\.json\fP or simply creates a new one\. The file created and
updated by this command will then take precedence over any other existing
or future \fBpackage\-lock\.json\fP files\. For a detailed explanation of the
design and purpose of package locks in npm, see
npm help package\-lock\-json\.
.SS See Also
.RS 0
.IP \(bu 2
npm help install
.IP \(bu 2
npm help run\-script
.IP \(bu 2
npm help scripts
.IP \(bu 2
npm help package\.json
.IP \(bu 2
npm help package\-lock\.json
.IP \(bu 2
npm help npm\-shrinkwrap\.json
.IP \(bu 2
npm help ls

.RE
