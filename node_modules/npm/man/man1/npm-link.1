.TH "NPM\-LINK" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-link\fR \- Symlink a package folder
.SS Synopsis
.P
.RS 2
.nf
npm link [<package\-spec>]

alias: ln
.fi
.RE
.SS Description
.P
This is handy for installing your own stuff, so that you can work on it and
test iteratively without having to continually rebuild\.
.P
Package linking is a two\-step process\.
.P
First, \fBnpm link\fP in a package folder with no arguments will create a
symlink in the global folder \fB{prefix}/lib/node_modules/<package>\fP that
links to the package where the \fBnpm link\fP command was executed\. It will
also link any bins in the package to \fB{prefix}/bin/{name}\fP\|\.  Note that
\fBnpm link\fP uses the global prefix (see \fBnpm prefix \-g\fP for its value)\.
.P
Next, in some other location, \fBnpm link package\-name\fP will create a
symbolic link from globally\-installed \fBpackage\-name\fP to \fBnode_modules/\fP of
the current folder\.
.P
Note that \fBpackage\-name\fP is taken from \fBpackage\.json\fP, \fInot\fR from the
directory name\.
.P
The package name can be optionally prefixed with a scope\. See
npm help \fBscope\fP\|\.  The scope must be preceded by an @\-symbol and
followed by a slash\.
.P
When creating tarballs for \fBnpm publish\fP, the linked packages are
"snapshotted" to their current state by resolving the symbolic links, if
they are included in \fBbundleDependencies\fP\|\.
.P
For example:
.P
.RS 2
.nf
cd ~/projects/node\-redis    # go into the package directory
npm link                    # creates global link
cd ~/projects/node\-bloggy   # go into some other package directory\.
npm link redis              # link\-install the package
.fi
.RE
.P
Now, any changes to \fB~/projects/node\-redis\fP will be reflected in
\fB~/projects/node\-bloggy/node_modules/node\-redis/\fP\|\. Note that the link
should be to the package name, not the directory name for that package\.
.P
You may also shortcut the two steps in one\.  For example, to do the
above use\-case in a shorter way:
.P
.RS 2
.nf
cd ~/projects/node\-bloggy  # go into the dir of your main project
npm link \.\./node\-redis     # link the dir of your dependency
.fi
.RE
.P
The second line is the equivalent of doing:
.P
.RS 2
.nf
(cd \.\./node\-redis; npm link)
npm link redis
.fi
.RE
.P
That is, it first creates a global link, and then links the global
installation target into your project's \fBnode_modules\fP folder\.
.P
Note that in this case, you are referring to the directory name,
\fBnode\-redis\fP, rather than the package name \fBredis\fP\|\.
.P
If your linked package is scoped (see npm help \fBscope\fP) your
link command must include that scope, e\.g\.
.P
.RS 2
.nf
npm link @myorg/privatepackage
.fi
.RE
.SS Caveat
.P
Note that package dependencies linked in this way are \fInot\fR saved to
\fBpackage\.json\fP by default, on the assumption that the intention is to have
a link stand in for a regular non\-link dependency\.  Otherwise, for example,
if you depend on \fBredis@^3\.0\.1\fP, and ran \fBnpm link redis\fP, it would replace
the \fB^3\.0\.1\fP dependency with \fBfile:\.\./path/to/node\-redis\fP, which you
probably don't want!  Additionally, other users or developers on your
project would run into issues if they do not have their folders set up
exactly the same as yours\.
.P
If you are adding a \fInew\fR dependency as a link, you should add it to the
relevant metadata by running \fBnpm install <dep> \-\-package\-lock\-only\fP\|\.
.P
If you \fIwant\fR to save the \fBfile:\fP reference in your \fBpackage\.json\fP and
\fBpackage\-lock\.json\fP files, you can use \fBnpm link <dep> \-\-save\fP to do so\.
.SS Workspace Usage
.P
\fBnpm link <pkg> \-\-workspace <name>\fP will link the relevant package as a
dependency of the specified workspace(s)\.  Note that It may actually be
linked into the parent project's \fBnode_modules\fP folder, if there are no
conflicting dependencies\.
.P
\fBnpm link \-\-workspace <name>\fP will create a global link to the specified
workspace(s)\.
.SS Configuration
.SS \fBsave\fP
.RS 0
.IP \(bu 2
Default: \fBtrue\fP unless when using \fBnpm update\fP where it defaults to \fBfalse\fP
.IP \(bu 2
Type: Boolean

.RE
.P
Save installed packages to a \fBpackage\.json\fP file as dependencies\.
.P
When used with the \fBnpm rm\fP command, removes the dependency from
\fBpackage\.json\fP\|\.
.P
Will also prevent writing to \fBpackage\-lock\.json\fP if set to \fBfalse\fP\|\.
.SS \fBsave\-exact\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Dependencies saved to package\.json will be configured with an exact version
rather than using npm's default semver range operator\.
.SS \fBglobal\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Operates in "global" mode, so that packages are installed into the \fBprefix\fP
folder instead of the current working directory\. See
npm help folders for more on the differences in behavior\.
.RS 0
.IP \(bu 2
packages are installed into the \fB{prefix}/lib/node_modules\fP folder, instead
of the current working directory\.
.IP \(bu 2
bin files are linked to \fB{prefix}/bin\fP
.IP \(bu 2
man pages are linked to \fB{prefix}/share/man\fP

.RE
.SS \fBglobal\-style\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Causes npm to install the package into your local \fBnode_modules\fP folder with
the same layout it uses with the global \fBnode_modules\fP folder\. Only your
direct dependencies will show in \fBnode_modules\fP and everything they depend
on will be flattened in their \fBnode_modules\fP folders\. This obviously will
eliminate some deduping\. If used with \fBlegacy\-bundling\fP, \fBlegacy\-bundling\fP
will be preferred\.
.SS \fBlegacy\-bundling\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Causes npm to install the package such that versions of npm prior to 1\.4,
such as the one included with node 0\.8, can install the package\. This
eliminates all automatic deduping\. If used with \fBglobal\-style\fP this option
will be preferred\.
.SS \fBstrict\-peer\-deps\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
If set to \fBtrue\fP, and \fB\-\-legacy\-peer\-deps\fP is not set, then \fIany\fR
conflicting \fBpeerDependencies\fP will be treated as an install failure, even
if npm could reasonably guess the appropriate resolution based on non\-peer
dependency relationships\.
.P
By default, conflicting \fBpeerDependencies\fP deep in the dependency graph will
be resolved using the nearest non\-peer dependency specification, even if
doing so will result in some packages receiving a peer dependency outside
the range set in their package's \fBpeerDependencies\fP object\.
.P
When such and override is performed, a warning is printed, explaining the
conflict and the packages involved\. If \fB\-\-strict\-peer\-deps\fP is set, then
this warning is treated as a failure\.
.SS \fBpackage\-lock\fP
.RS 0
.IP \(bu 2
Default: true
.IP \(bu 2
Type: Boolean

.RE
.P
If set to false, then ignore \fBpackage\-lock\.json\fP files when installing\. This
will also prevent \fIwriting\fR \fBpackage\-lock\.json\fP if \fBsave\fP is true\.
.P
This configuration does not affect \fBnpm ci\fP\|\.
.SS \fBomit\fP
.RS 0
.IP \(bu 2
Default: 'dev' if the \fBNODE_ENV\fP environment variable is set to
\|'production', otherwise empty\.
.IP \(bu 2
Type: "dev", "optional", or "peer" (can be set multiple times)

.RE
.P
Dependency types to omit from the installation tree on disk\.
.P
Note that these dependencies \fIare\fR still resolved and added to the
\fBpackage\-lock\.json\fP or \fBnpm\-shrinkwrap\.json\fP file\. They are just not
physically installed on disk\.
.P
If a package type appears in both the \fB\-\-include\fP and \fB\-\-omit\fP lists, then
it will be included\.
.P
If the resulting omit list includes \fB\|'dev'\fP, then the \fBNODE_ENV\fP environment
variable will be set to \fB\|'production'\fP for all lifecycle scripts\.
.SS \fBignore\-scripts\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
If true, npm does not run scripts specified in package\.json files\.
.P
Note that commands explicitly intended to run a particular script, such as
\fBnpm start\fP, \fBnpm stop\fP, \fBnpm restart\fP, \fBnpm test\fP, and \fBnpm run\-script\fP
will still run their intended script if \fBignore\-scripts\fP is set, but they
will \fInot\fR run any pre\- or post\-scripts\.
.SS \fBaudit\fP
.RS 0
.IP \(bu 2
Default: true
.IP \(bu 2
Type: Boolean

.RE
.P
When "true" submit audit reports alongside the current npm command to the
default registry and all registries configured for scopes\. See the
documentation for npm help \fBaudit\fP for details on what is
submitted\.
.SS \fBbin\-links\fP
.RS 0
.IP \(bu 2
Default: true
.IP \(bu 2
Type: Boolean

.RE
.P
Tells npm to create symlinks (or \fB\|\.cmd\fP shims on Windows) for package
executables\.
.P
Set to false to have it not do this\. This can be used to work around the
fact that some file systems don't support symlinks, even on ostensibly Unix
systems\.
.SS \fBfund\fP
.RS 0
.IP \(bu 2
Default: true
.IP \(bu 2
Type: Boolean

.RE
.P
When "true" displays the message at the end of each \fBnpm install\fP
acknowledging the number of dependencies looking for funding\. See npm help \fBnpm
fund\fP for details\.
.SS \fBdry\-run\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Indicates that you don't want npm to make any changes and that it should
only report what it would have done\. This can be passed into any of the
commands that modify your local installation, eg, \fBinstall\fP, \fBupdate\fP,
\fBdedupe\fP, \fBuninstall\fP, as well as \fBpack\fP and \fBpublish\fP\|\.
.P
Note: This is NOT honored by other network related commands, eg \fBdist\-tags\fP,
\fBowner\fP, etc\.
.SS \fBworkspace\fP
.RS 0
.IP \(bu 2
Default:
.IP \(bu 2
Type: String (can be set multiple times)

.RE
.P
Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option\.
.P
Valid values for the \fBworkspace\fP config are either:
.RS 0
.IP \(bu 2
Workspace names
.IP \(bu 2
Path to a workspace directory
.IP \(bu 2
Path to a parent workspace directory (will result in selecting all
workspaces within that folder)

.RE
.P
When set for the \fBnpm init\fP command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project\.
.P
This value is not exported to the environment for child processes\.
.SS \fBworkspaces\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or Boolean

.RE
.P
Set to true to run the command in the context of \fBall\fR configured
workspaces\.
.P
Explicitly setting this to false will cause commands like \fBinstall\fP to
ignore workspaces altogether\. When not set explicitly:
.RS 0
.IP \(bu 2
Commands that operate on the \fBnode_modules\fP tree (install, update, etc\.)
will link workspaces into the \fBnode_modules\fP folder\. \- Commands that do
other things (test, exec, publish, etc\.) will operate on the root project,
\fIunless\fR one or more workspaces are specified in the \fBworkspace\fP config\.

.RE
.P
This value is not exported to the environment for child processes\.
.SS \fBinclude\-workspace\-root\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Include the workspace root when workspaces are enabled for a command\.
.P
When false, specifying individual workspaces via the \fBworkspace\fP config, or
all workspaces via the \fBworkspaces\fP flag, will cause npm to operate only on
the specified workspaces, and not on the root project\.
.P
This value is not exported to the environment for child processes\.
.SS \fBinstall\-links\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
When set file: protocol dependencies that exist outside of the project root
will be packed and installed as regular dependencies instead of creating a
symlink\. This option has no effect on workspaces\.
.SS See Also
.RS 0
.IP \(bu 2
npm help package spec
.IP \(bu 2
npm help developers
.IP \(bu 2
npm help package\.json
.IP \(bu 2
npm help install
.IP \(bu 2
npm help folders
.IP \(bu 2
npm help config
.IP \(bu 2
npm help npmrc

.RE
