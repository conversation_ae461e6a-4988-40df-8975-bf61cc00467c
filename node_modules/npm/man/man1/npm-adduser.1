.TH "NPM\-ADDUSER" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-adduser\fR \- Add a registry user account
.SS Synopsis
.P
.RS 2
.nf
npm adduser

aliases: login, add\-user
.fi
.RE
.P
Note: This command is unaware of workspaces\.
.SS Description
.P
Create or verify a user named \fB<username>\fP in the specified registry, and
save the credentials to the \fB\|\.npmrc\fP file\. If no registry is specified,
the default registry will be used (see npm help \fBconfig\fP)\.
.P
The username, password, and email are read in from prompts\.
.P
To reset your password, go to https://www\.npmjs\.com/forgot
.P
To change your email address, go to https://www\.npmjs\.com/email\-edit
.P
You may use this command multiple times with the same user account to
authorize on a new machine\.  When authenticating on a new machine,
the username, password and email address must all match with
your existing record\.
.P
\fBnpm login\fP is an alias to \fBadduser\fP and behaves exactly the same way\.
.SS Configuration
.SS \fBregistry\fP
.RS 0
.IP \(bu 2
Default: "https://registry\.npmjs\.org/"
.IP \(bu 2
Type: URL

.RE
.P
The base URL of the npm registry\.
.SS \fBscope\fP
.RS 0
.IP \(bu 2
Default: the scope of the current project, if any, or ""
.IP \(bu 2
Type: String

.RE
.P
Associate an operation with a scope for a scoped registry\.
.P
Useful when logging in to or out of a private registry:
.P
.RS 2
.nf
# log in, linking the scope to the custom registry
npm login \-\-scope=@mycorp \-\-registry=https://registry\.mycorp\.com

# log out, removing the link and the auth token
npm logout \-\-scope=@mycorp
.fi
.RE
.P
This will cause \fB@mycorp\fP to be mapped to the registry for future
installation of packages specified according to the pattern
\fB@mycorp/package\fP\|\.
.P
This will also cause \fBnpm init\fP to create a scoped package\.
.P
.RS 2
.nf
# accept all defaults, and create a package named "@foo/whatever",
# instead of just named "whatever"
npm init \-\-scope=@foo \-\-yes
.fi
.RE
.SS \fBauth\-type\fP
.RS 0
.IP \(bu 2
Default: "legacy"
.IP \(bu 2
Type: "legacy", "web", "sso", "saml", "oauth", or "webauthn"

.RE
.P
NOTE: auth\-type values "sso", "saml", "oauth", and "webauthn" will be
removed in a future version\.
.P
What authentication strategy to use with \fBlogin\fP\|\.
.SS See Also
.RS 0
.IP \(bu 2
npm help registry
.IP \(bu 2
npm help config
.IP \(bu 2
npm help npmrc
.IP \(bu 2
npm help owner
.IP \(bu 2
npm help whoami
.IP \(bu 2
npm help token
.IP \(bu 2
npm help profile

.RE
