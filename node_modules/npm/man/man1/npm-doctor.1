.TH "NPM\-DOCTOR" "1" "August 2022" "" ""
.SH "NAME"
\fBnpm-doctor\fR \- Check your npm environment
.SS Synopsis
.P
.RS 2
.nf
npm doctor
.fi
.RE
.P
Note: This command is unaware of workspaces\.
.SS Description
.P
\fBnpm doctor\fP runs a set of checks to ensure that your npm installation has
what it needs to manage your JavaScript packages\. npm is mostly a
standalone tool, but it does have some basic requirements that must be met:
.RS 0
.IP \(bu 2
Node\.js and git must be executable by npm\.
.IP \(bu 2
The primary npm registry, \fBregistry\.npmjs\.com\fP, or another service that
uses the registry API, is available\.
.IP \(bu 2
The directories that npm uses, \fBnode_modules\fP (both locally and
globally), exist and can be written by the current user\.
.IP \(bu 2
The npm cache exists, and the package tarballs within it aren't corrupt\.

.RE
.P
Without all of these working properly, npm may not work properly\.  Many
issues are often attributable to things that are outside npm's code base,
so \fBnpm doctor\fP confirms that the npm installation is in a good state\.
.P
Also, in addition to this, there are also very many issue reports due to
using old versions of npm\. Since npm is constantly improving, running
\fBnpm@latest\fP is better than an old version\.
.P
\fBnpm doctor\fP verifies the following items in your environment, and if there
are any recommended changes, it will display them\.
.SS \fBnpm ping\fP
.P
By default, npm installs from the primary npm registry,
\fBregistry\.npmjs\.org\fP\|\.  \fBnpm doctor\fP hits a special ping endpoint within the
registry\. This can also be checked with \fBnpm ping\fP\|\. If this check fails,
you may be using a proxy that needs to be configured, or may need to talk
to your IT staff to get access over HTTPS to \fBregistry\.npmjs\.org\fP\|\.
.P
This check is done against whichever registry you've configured (you can
see what that is by running \fBnpm config get registry\fP), and if you're using
a private registry that doesn't support the \fB/whoami\fP endpoint supported by
the primary registry, this check may fail\.
.SS \fBnpm \-v\fP
.P
While Node\.js may come bundled with a particular version of npm, it's the
policy of the CLI team that we recommend all users run \fBnpm@latest\fP if they
can\. As the CLI is maintained by a small team of contributors, there are
only resources for a single line of development, so npm's own long\-term
support releases typically only receive critical security and regression
fixes\. The team believes that the latest tested version of npm is almost
always likely to be the most functional and defect\-free version of npm\.
.SS \fBnode \-v\fP
.P
For most users, in most circumstances, the best version of Node will be the
latest long\-term support (LTS) release\. Those of you who want access to new
ECMAscript features or bleeding\-edge changes to Node's standard library may
be running a newer version, and some may be required to run an older
version of Node because of enterprise change control policies\. That's OK!
But in general, the npm team recommends that most users run Node\.js LTS\.
.SS \fBnpm config get registry\fP
.P
You may be installing from private package registries for your project or
company\. That's great! Others may be following tutorials or StackOverflow
questions in an effort to troubleshoot problems you may be having\.
Sometimes, this may entail changing the registry you're pointing at\.  This
part of \fBnpm doctor\fP just lets you, and maybe whoever's helping you with
support, know that you're not using the default registry\.
.SS \fBwhich git\fP
.P
While it's documented in the README, it may not be obvious that npm needs
Git installed to do many of the things that it does\. Also, in some cases
– especially on Windows – you may have Git set up in such a way that it's
not accessible via your \fBPATH\fP so that npm can find it\. This check ensures
that Git is available\.
.SS Permissions checks
.RS 0
.IP \(bu 2
Your cache must be readable and writable by the user running npm\.
.IP \(bu 2
Global package binaries must be writable by the user running npm\.
.IP \(bu 2
Your local \fBnode_modules\fP path, if you're running \fBnpm doctor\fP with a
project directory, must be readable and writable by the user running npm\.

.RE
.SS Validate the checksums of cached packages
.P
When an npm package is published, the publishing process generates a
checksum that npm uses at install time to verify that the package didn't
get corrupted in transit\. \fBnpm doctor\fP uses these checksums to validate the
package tarballs in your local cache (you can see where that cache is
located with \fBnpm config get cache\fP)\. In the event that there are corrupt
packages in your cache, you should probably run \fBnpm cache clean \-f\fP and
reset the cache\.
.SS Configuration
.SS \fBregistry\fP
.RS 0
.IP \(bu 2
Default: "https://registry\.npmjs\.org/"
.IP \(bu 2
Type: URL

.RE
.P
The base URL of the npm registry\.
.SS See Also
.RS 0
.IP \(bu 2
npm help bugs
.IP \(bu 2
npm help help
.IP \(bu 2
npm help ping

.RE
