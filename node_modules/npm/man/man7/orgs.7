.TH "ORGS" "7" "August 2022" "" ""
.SH "NAME"
\fBorgs\fR \- Working with Teams & Orgs
.SS Description
.P
There are three levels of org users:
.RS 0
.IP 1. 3
Super admin, controls billing & adding people to the org\.
.IP 2. 3
Team admin, manages team membership & package access\.
.IP 3. 3
Developer, works on packages they are given access to\.  

.RE
.P
The super admin is the only person who can add users to the org because it impacts the monthly bill\. The super admin will use the website to manage membership\. Every org has a \fBdevelopers\fP team that all users are automatically added to\.
.P
The team admin is the person who manages team creation, team membership, and package access for teams\. The team admin grants package access to teams, not individuals\.
.P
The developer will be able to access packages based on the teams they are on\. Access is either read\-write or read\-only\.
.P
There are two main commands:
.RS 0
.IP 1. 3
\fBnpm team\fP see npm help team for more details
.IP 2. 3
\fBnpm access\fP see npm help access for more details

.RE
.SS Team Admins create teams
.RS 0
.IP \(bu 2
Check who you’ve added to your org:

.RE
.P
.RS 2
.nf
npm team ls <org>:developers
.fi
.RE
.RS 0
.IP \(bu 2
Each org is automatically given a \fBdevelopers\fP team, so you can see the whole list of team members in your org\. This team automatically gets read\-write access to all packages, but you can change that with the \fBaccess\fP command\.
.IP \(bu 2
Create a new team:

.RE
.P
.RS 2
.nf
npm team create <org:team>
.fi
.RE
.RS 0
.IP \(bu 2
Add members to that team:

.RE
.P
.RS 2
.nf
npm team add <org:team> <user>
.fi
.RE
.SS Publish a package and adjust package access
.RS 0
.IP \(bu 2
In package directory, run

.RE
.P
.RS 2
.nf
npm init \-\-scope=<org>
.fi
.RE
.P
to scope it for your org & publish as usual
.RS 0
.IP \(bu 2
Grant access:  

.RE
.P
.RS 2
.nf
npm access grant <read\-only|read\-write> <org:team> [<package>]
.fi
.RE
.RS 0
.IP \(bu 2
Revoke access:

.RE
.P
.RS 2
.nf
npm access revoke <org:team> [<package>]
.fi
.RE
.SS Monitor your package access
.RS 0
.IP \(bu 2
See what org packages a team member can access:

.RE
.P
.RS 2
.nf
npm access ls\-packages <org> <user>
.fi
.RE
.RS 0
.IP \(bu 2
See packages available to a specific team:

.RE
.P
.RS 2
.nf
npm access ls\-packages <org:team>
.fi
.RE
.RS 0
.IP \(bu 2
Check which teams are collaborating on a package:

.RE
.P
.RS 2
.nf
npm access ls\-collaborators <pkg>
.fi
.RE
.SS See also
.RS 0
.IP \(bu 2
npm help team
.IP \(bu 2
npm help access
.IP \(bu 2
npm help scope

.RE
