<lint-module
    format="1"
    dir="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-vlc-media-player/android"
    name=":react-native-vlc-media-player"
    type="LIBRARY"
    maven="ModrkClient:react-native-vlc-media-player:unspecified"
    agpVersion="8.4.0"
    buildFolder="build"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-31/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-31"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
