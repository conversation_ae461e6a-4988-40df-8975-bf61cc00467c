<lint-module
    format="1"
    dir="/Users/<USER>/Projects/ModrkClient/node_modules/react-native-image-resizer/android"
    name=":react-native-image-resizer"
    type="LIBRARY"
    maven="ModrkClient:react-native-image-resizer:unspecified"
    agpVersion="8.4.0"
    buildFolder="build"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-34/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      disable="LongLogTag"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="LongLogTag"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
