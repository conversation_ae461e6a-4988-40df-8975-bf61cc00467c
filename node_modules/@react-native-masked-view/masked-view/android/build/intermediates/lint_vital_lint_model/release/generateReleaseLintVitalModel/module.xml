<lint-module
    format="1"
    dir="/Users/<USER>/Projects/ModrkClient/node_modules/@react-native-masked-view/masked-view/android"
    name=":react-native-masked-view_masked-view"
    type="LIBRARY"
    maven="ModrkClient:react-native-masked-view_masked-view:unspecified"
    agpVersion="8.4.0"
    buildFolder="build"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-34/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="InvalidPackage"
        severity="WARNING" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
