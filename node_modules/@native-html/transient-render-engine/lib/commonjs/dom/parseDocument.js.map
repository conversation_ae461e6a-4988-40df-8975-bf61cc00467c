{"version": 3, "sources": ["parseDocument.ts"], "names": ["parseDocument", "data", "options", "handler", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "end", "root"], "mappings": ";;;;;;;AACA;;AACA;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,aAAT,CACbC,IADa,EAEbC,OAA0C,GAAG,EAFhC,EAGH;AACV,QAAMC,OAAO,GAAG,IAAIC,mBAAJ,CAAeF,OAAf,CAAhB;AACA,MAAIG,kBAAJ,CAAWF,OAAX,EAAoBD,OAApB,EAA6BI,GAA7B,CAAiCL,IAAjC;AACA,SAAOE,OAAO,CAACI,IAAf;AACD", "sourcesContent": ["import { Document } from 'domhandler';\nimport { Parser, ParserOptions } from 'htmlparser2';\nimport <PERSON><PERSON><PERSON><PERSON>, { DomHandlerOptions } from './DomHandler';\n/**\n * Parses the data, returns the resulting document.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n */\nexport default function parseDocument(\n  data: string,\n  options: ParserOptions & DomHandlerOptions = {}\n): Document {\n  const handler = new DomHandler(options);\n  new Parser(handler, options).end(data);\n  return handler.root;\n}\n"]}