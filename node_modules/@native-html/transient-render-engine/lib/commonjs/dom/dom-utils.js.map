{"version": 3, "sources": ["dom-utils.ts"], "names": ["isDomText", "node", "type", "TextType", "isDomElement", "TagType"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA;;AAIO,SAASA,SAAT,CAAmBC,IAAnB,EAA4C;AACjD,SAAOA,IAAI,IAAIA,IAAI,CAACC,IAAL,KAAcC,oBAA7B;AACD;;AAEM,SAASC,YAAT,CAAsBH,IAAtB,EAAkD;AACvD,SAAOA,IAAI,IAAIA,IAAI,CAACC,IAAL,KAAcG,mBAA7B;AACD", "sourcesContent": ["import { Text, Element, Node, Document, NodeWithChildren } from 'domhandler';\nimport { Text as TextType, Tag as TagType } from 'domelementtype';\n\nexport { Text, Element, Node, NodeWithChildren, Document };\n\nexport function isDomText(node: any): node is Text {\n  return node && node.type === TextType;\n}\n\nexport function isDomElement(node: any): node is Element {\n  return node && node.type === TagType;\n}\n"]}