{"version": 3, "sources": ["HTMLModelRegistry.ts"], "names": ["HTMLModelRegistry", "constructor", "customize", "internalHTMLElementModels", "modelRecords", "getElementModelFromTagName", "tagName"], "mappings": ";;;;;;;AAAA;;;;;;AAMe,MAAMA,iBAAN,CAA0C;AAIvDC,EAAAA,WAAW,CACTC,SADS,EAIT;AAAA,0CANAC,iCAMA;;AACA,QAAI,OAAOD,SAAP,KAAqB,UAAzB,EAAqC;AACnC,WAAKE,YAAL,GAAoBF,SAAS,CAACC,iCAAD,CAA7B;AACD;AACF;;AAEDE,EAAAA,0BAA0B,CACxBC,OADwB,EAEc;AACtC,QAAIA,OAAO,IAAI,KAAKF,YAApB,EAAkC;AAChC,aAAO,KAAKA,YAAL,CAAkBE,OAAlB,CAAP;AACD;;AACD,WAAO,IAAP;AACD;;AArBsD", "sourcesContent": ["import internalHTMLElementModels, {\n  DefaultHTMLElementModelsStatic\n} from './defaultHTMLElementModels';\nimport HTMLElementModel from './HTMLElementModel';\nimport { HTMLModelRecord, TagName } from './model-types';\n\nexport default class HTMLModelRegistry<E extends string> {\n  public readonly modelRecords: HTMLModelRecord<E | TagName> =\n    internalHTMLElementModels as HTMLModelRecord<any>;\n\n  constructor(\n    customize?: (\n      defaultHTMLElementModels: DefaultHTMLElementModelsStatic\n    ) => HTMLModelRecord<E | TagName>\n  ) {\n    if (typeof customize === 'function') {\n      this.modelRecords = customize(internalHTMLElementModels);\n    }\n  }\n\n  getElementModelFromTagName(\n    tagName: E | TagName\n  ): HTMLElementModel<string, any> | null {\n    if (tagName in this.modelRecords) {\n      return this.modelRecords[tagName];\n    }\n    return null;\n  }\n}\n"]}