{"version": 3, "sources": ["HTMLElementModel.ts"], "names": ["phrasingCategories", "translatableBlockCategories", "HTMLElementModel", "constructor", "tagName", "contentModel", "isOpaque", "mixedUAStyles", "isVoid", "getUADerivedStyleFromAttributes", "getMixedUAStyles", "setMarkersForTNode", "getReactNativeProps", "reactNativeProps", "fromCustomModel", "template", "optionalFields", "fromNativeModel", "nativeElementModel", "category", "otherProps", "isPhrasing", "indexOf", "isTranslatable", "HTMLContentModel", "mixed", "textual", "block", "none", "isTranslatableBlock", "isTranslatableTextual", "extend", "arg", "properties"], "mappings": ";;;;;;;AAIA;;;;;;AASA,MAAMA,kBAAqC,GAAG,CAAC,SAAD,EAAY,OAAZ,EAAqB,QAArB,CAA9C;AACA,MAAMC,2BAA8C,GAAG,CACrD,SADqD,EAErD,UAFqD,EAGrD,YAHqD,CAAvD;AAMA;AACA;AACA;AACA;AACA;AACA;;AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMC,gBAAN,CAIf;AACE;AACF;AACA;;AAEE;AACF;AACA;;AAEE;AACF;AACA;AACA;;AAEE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEE;AACF;AACA;AACA;AACA;;AAEE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEE;AACF;AACA;AACA;AACA;;AAEE;AACF;AACA;AACA;AACA;AACA;;AAEE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGE;AACF;AACA;AAGUC,EAAAA,WAAW,CAAC;AAClBC,IAAAA,OADkB;AAElBC,IAAAA,YAFkB;AAGlBC,IAAAA,QAHkB;AAIlBC,IAAAA,aAJkB;AAKlBC,IAAAA,MALkB;AAMlBC,IAAAA,+BANkB;AAOlBC,IAAAA,gBAPkB;AAQlBC,IAAAA,kBARkB;AASlBC,IAAAA,mBATkB;AAUlBC,IAAAA;AAVkB,GAAD,EAWa;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAC9B,SAAKT,OAAL,GAAeA,OAAf;AACA,SAAKC,YAAL,GAAoBA,YAApB;AACA,SAAKC,QAAL,GAAgBA,QAAQ,IAAI,KAA5B;AACA,SAAKE,MAAL,GAAcA,MAAd;AACA,SAAKD,aAAL,GAAqBA,aAArB;AACA,SAAKE,+BAAL,GAAuCA,+BAAvC;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAKC,kBAAL,GAA0BA,kBAA1B;AACA,SAAKC,mBAAL,GAA2BA,mBAA3B;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACD;AAED;AACF;AACA;AACA;AACA;;;AACwB,SAAfC,eAAe,CAGpBC,QAHoB,EAGoC;AACxD,UAAM;AACJV,MAAAA,YADI;AAEJD,MAAAA,OAFI;AAGJE,MAAAA,QAAQ,GAAG,KAHP;AAIJE,MAAAA,MAAM,GAAG,KAJL;AAKJ,SAAGQ;AALC,QAMFD,QANJ;AAOA,WAAO,IAAIb,gBAAJ,CAA+C;AACpDE,MAAAA,OADoD;AAEpDC,MAAAA,YAFoD;AAGpDC,MAAAA,QAHoD;AAIpDE,MAAAA,MAJoD;AAKpD,SAAGQ;AALiD,KAA/C,CAAP;AAOD;AAED;AACF;AACA;AACA;AACA;;;AACwB,SAAfC,eAAe,CACpBC,kBADoB,EAEpB;AACA,UAAM;AACJC,MAAAA,QADI;AAEJb,MAAAA,QAFI;AAGJE,MAAAA,MAAM,GAAG,KAHL;AAIJ,SAAGY;AAJC,QAKFF,kBALJ;AAMA,UAAMG,UAAU,GAAGrB,kBAAkB,CAACsB,OAAnB,CAA2BH,QAA3B,MAAyC,CAAC,CAA7D;AACA,UAAMI,cAAc,GAClBF,UAAU,IAAIpB,2BAA2B,CAACqB,OAA5B,CAAoCH,QAApC,MAAkD,CAAC,CADnE;AAEA,UAAMd,YAAY,GAChBc,QAAQ,KAAK,QAAb,IAAyBA,QAAQ,KAAK,OAAtC,GACIK,0BAAiBC,KADrB,GAEIJ,UAAU,GACVG,0BAAiBE,OADP,GAEVH,cAAc,GACdC,0BAAiBG,KADH,GAEdH,0BAAiBI,IAPvB;AAQA,WAAO,IAAI1B,gBAAJ,CASL;AACAM,MAAAA,MADA;AAEAH,MAAAA,YAAY,EAAEA,YAFd;AAGAC,MAAAA,QAAQ,EAAEA,QAAF,aAAEA,QAAF,cAAEA,QAAF,GAAca,QAAQ,KAAK,UAHnC;AAIA,SAAGC;AAJH,KATK,CAAP;AAeD;;AAEDS,EAAAA,mBAAmB,GAAY;AAC7B,WAAO,KAAKxB,YAAL,KAAsBmB,0BAAiBG,KAA9C;AACD;;AAEDG,EAAAA,qBAAqB,GAAG;AACtB,WACE,KAAKzB,YAAL,KAAsBmB,0BAAiBE,OAAvC,IACA,KAAKrB,YAAL,KAAsBmB,0BAAiBC,KAFzC;AAID;AAED;AACF;AACA;AACA;AACA;AACA;;;AAeEM,EAAAA,MAAM,CACJC,GADI,EAMqB;AACzB,UAAMC,UAAU,GAAG,OAAOD,GAAP,KAAe,UAAf,GAA4BA,GAAG,CAAC,IAAD,CAA/B,GAAwCA,GAA3D;AACA,WAAO,IAAI9B,gBAAJ,CAA4B,EACjC,GAAG,IAD8B;AAEjC,SAAG+B;AAF8B,KAA5B,CAAP;AAID;;AAnQH", "sourcesContent": ["/* eslint-disable no-dupe-class-members */\nimport { MixedStyleDeclaration } from '@native-html/css-processor';\nimport { ReactNativePropsDefinitions } from '../helper-types';\nimport { SetMarkersForTNode } from '../tree/tree-types';\nimport HTMLContentModel from './HTMLContentModel';\nimport {\n  CustomElementModel,\n  ElementCategory,\n  ElementModelBase,\n  NativeElementModel,\n  TagName\n} from './model-types';\n\nconst phrasingCategories: ElementCategory[] = ['textual', 'edits', 'anchor'];\nconst translatableBlockCategories: ElementCategory[] = [\n  'tabular',\n  'grouping',\n  'sectioning'\n];\n\n/**\n * An object to specify custom tags.\n *\n * @typeParam T - The name of the tag to which the model will apply.\n * @typeParam M - The {@link HTMLContentModel} associated with this tag.\n */\nexport interface HTMLElementModelShape<\n  T extends string,\n  M extends HTMLContentModel\n> extends ElementModelBase<T> {\n  /**\n   * The {@link HTMLContentModel} attached to this model.\n   */\n  readonly contentModel: M;\n  /**\n   * `true` when the associated tag is a {@link https://html.spec.whatwg.org/multipage/syntax.html#void-elements | void element}.\n   *\n   * @remarks\n   *\n   * - Void elements cannot have children.\n   * - TText-translated void elements will be preserved even though they don't\n   *   have children.\n   */\n  readonly isVoid: boolean;\n  /**\n   * An opaque element translated {@link TNode} will have no translated {@link TNode}\n   * children.\n   */\n  readonly isOpaque: boolean;\n}\n\n/**\n * An object defining engine internals for tags, such as default styles\n * (UAStyles), content model (how this tag is treated during hoisting)... etc.\n *\n * @typeParam T - The name of the tag to which the model will apply.\n * @typeParam M - The {@link HTMLContentModel} associated with this tag.\n */\nexport default class HTMLElementModel<\n  T extends string,\n  M extends HTMLContentModel\n> implements HTMLElementModelShape<T, M>\n{\n  /**\n   * The tag name associated with this model.\n   */\n  public readonly tagName: T;\n  /**\n   * The {@link HTMLContentModel} attached to this model.\n   */\n  public readonly contentModel: M;\n  /**\n   * An opaque element translated {@link TNode} will have no translated {@link TNode}\n   * children.\n   */\n  public readonly isOpaque: boolean;\n  /**\n   * `true` when the associated tag is a {@link https://html.spec.whatwg.org/multipage/syntax.html#void-elements | void element}.\n   *\n   * @remarks\n   *\n   * - Void elements cannot have children.\n   * - TText-translated void elements will be preserved even though they don't\n   *   have children.\n   */\n  public readonly isVoid: boolean;\n  /**\n   * Equivalent of \"user-agent\" styles. The default styles for the element.\n   *\n   * @remarks These styles will be merged over by `tagsStyles`.\n   */\n  public readonly mixedUAStyles?: MixedStyleDeclaration;\n  /**\n   * React Native props grouped by native components. Those props\n   * will be passed to the underlying native component at render time.\n   *\n   * @remarks Some props might be overriden by props derived from the\n   * {@link TNode} attributes. For example, if you pass `accessibilityLabel`\n   * and there is an `aria-label` attribute attached to one node, the\n   * `aria-label` will be used. If you want to be able to override the\n   * `aria-label`, use {@link HTMLElementModel.getReactNativeProps} instead.\n   *\n   * @example\n   *\n   * ```ts\n   * import {HTMLElementModel, HTMLContentModel} from 'react-native-render-html';\n   *\n   * const customHTMLElementModels = {\n   *  'nav-button': HTMLElementModel.fromCustomModel({\n   *    tagName: 'nav-button',\n   *    contentModel: HTMLContentModel.block,\n   *    reactNativeProps: {\n   *      native: {\n   *        onPress() {\n   *          console.info('nav-button pressed');\n   *        },\n   *      },\n   *    },\n   *  }),\n   *};\n   * ```\n   */\n  readonly reactNativeProps?: ReactNativePropsDefinitions;\n  /**\n   * A function to create conditional \"user-agent\" styles.\n   *\n   * @deprecated Use {@link HTMLElementModel.getMixedUAStyles} instead.\n   */\n  public readonly getUADerivedStyleFromAttributes: NativeElementModel['getUADerivedStyleFromAttributes'];\n  /**\n   * A function to create conditional \"user-agent\" styles.\n   *\n   * @remarks For example, &lt;a&gt; tags will have underline decoration and be\n   * colored blue only when `href` is defined.\n   */\n  public readonly getMixedUAStyles: NativeElementModel['getMixedUAStyles'];\n  /**\n   * A function to create React Native props from a {@link TNode} grouped by\n   * native components.\n   *\n   * Those props will be deep-merged over the pre-generated props. You can\n   * preserve some of the pre-generated props since you receive them as second\n   * argument.\n   *\n   * **Merge strategy** (latest overrides former):\n   *\n   * 1. props from `reactNativeProps`,\n   * 2. auto-generated props from attributes\n   * 3. props returned by this function\n   *\n   * @param tnode - The {@link TNode} for which to create React Native props.\n   * @param preGeneratedProps - The props that were pre-generated for the {@link TNode}\n   * based on attributes (e.g. aria-label ...) and\n   * {@link ElementModelBase.reactNativeProps}.\n   * @returns React Native props grouped by native components (see\n   * {@link ReactNativePropsDefinitions}). Those props will be passed to the\n   * underlying native component at render time.\n   *\n   * @example\n   *\n   * ```ts\n   * import { defaultHTMLElementModels } from \"react-native-render-html\";\n   *\n   * const customHTMLElementModels = {\n   *   a: defaultHTMLElementModels.a.extend({\n   *     getReactNativeProps(tnode) {\n   *       const attributes = tnode.attributes;\n   *       return {\n   *         native: {\n   *           accessibilityHint:\n   *             attributes['data-scope'] === 'internal'\n   *               ? 'Open in a new screen.'\n   *               : 'Open in system web browser.',\n   *         },\n   *       };\n   *     },\n   *   }),\n   * };\n   * ```\n   */\n  public readonly getReactNativeProps: NativeElementModel['getReactNativeProps'];\n\n  /**\n   * Derive markers for one TNode.\n   */\n  public readonly setMarkersForTNode?: SetMarkersForTNode;\n\n  private constructor({\n    tagName,\n    contentModel,\n    isOpaque,\n    mixedUAStyles,\n    isVoid,\n    getUADerivedStyleFromAttributes,\n    getMixedUAStyles,\n    setMarkersForTNode,\n    getReactNativeProps,\n    reactNativeProps\n  }: HTMLElementModelShape<T, M>) {\n    this.tagName = tagName;\n    this.contentModel = contentModel;\n    this.isOpaque = isOpaque || false;\n    this.isVoid = isVoid;\n    this.mixedUAStyles = mixedUAStyles;\n    this.getUADerivedStyleFromAttributes = getUADerivedStyleFromAttributes;\n    this.getMixedUAStyles = getMixedUAStyles;\n    this.setMarkersForTNode = setMarkersForTNode;\n    this.getReactNativeProps = getReactNativeProps;\n    this.reactNativeProps = reactNativeProps;\n  }\n\n  /**\n   * Create an {@link HTMLElementModel} from a custom template.\n   *\n   * @param template - The custom template.\n   */\n  static fromCustomModel<\n    CustomTags extends string,\n    ContentModel extends HTMLContentModel\n  >(template: CustomElementModel<CustomTags, ContentModel>) {\n    const {\n      contentModel,\n      tagName,\n      isOpaque = false,\n      isVoid = false,\n      ...optionalFields\n    } = template;\n    return new HTMLElementModel<CustomTags, ContentModel>({\n      tagName,\n      contentModel,\n      isOpaque,\n      isVoid,\n      ...optionalFields\n    });\n  }\n\n  /**\n   * Create an {@link HTMLElementModel} from a native description.\n   *\n   * @param nativeElementModel - The native model declaration.\n   */\n  static fromNativeModel<TN extends TagName, E extends ElementCategory>(\n    nativeElementModel: NativeElementModel<TN, E>\n  ) {\n    const {\n      category,\n      isOpaque,\n      isVoid = false,\n      ...otherProps\n    } = nativeElementModel;\n    const isPhrasing = phrasingCategories.indexOf(category) !== -1;\n    const isTranslatable =\n      isPhrasing || translatableBlockCategories.indexOf(category) !== -1;\n    const contentModel =\n      category === 'anchor' || category === 'edits'\n        ? HTMLContentModel.mixed\n        : isPhrasing\n        ? HTMLContentModel.textual\n        : isTranslatable\n        ? HTMLContentModel.block\n        : HTMLContentModel.none;\n    return new HTMLElementModel<\n      TN,\n      E extends 'edits' | 'anchor'\n        ? HTMLContentModel.mixed\n        : E extends 'sectioning' | 'grouping' | 'tabular'\n        ? HTMLContentModel.block\n        : E extends 'textual'\n        ? HTMLContentModel.textual\n        : HTMLContentModel.none\n    >({\n      isVoid,\n      contentModel: contentModel as any,\n      isOpaque: isOpaque ?? category === 'embedded',\n      ...otherProps\n    });\n  }\n\n  isTranslatableBlock(): boolean {\n    return this.contentModel === HTMLContentModel.block;\n  }\n\n  isTranslatableTextual() {\n    return (\n      this.contentModel === HTMLContentModel.textual ||\n      this.contentModel === HTMLContentModel.mixed\n    );\n  }\n\n  /**\n   * Create a new {@link HTMLElementModel} by shallow-merging properties into this model.\n   *\n   * @param merger - A function to generate the new properties to shallow-merge into this model.\n   * @typeParam CM - The {@link HTMLContentModel} attached to the new model.\n   */\n  extend<CM extends HTMLContentModel>(\n    merger: (\n      shape: HTMLElementModelShape<T, CM>\n    ) => Partial<HTMLElementModelShape<T, CM>>\n  ): HTMLElementModel<T, CM>;\n  /**\n   * Create a new {@link HTMLElementModel} by shallow-merging properties into this model.\n   *\n   * @param shape - The {@link HTMLElementModelShape} to shallow-merge into this model.\n   * @typeParam CM - The {@link HTMLContentModel} attached to the new model.\n   */\n  extend<CM extends HTMLContentModel>(\n    shape: Partial<HTMLElementModelShape<T, CM>>\n  ): HTMLElementModel<T, CM>;\n  extend<CM extends HTMLContentModel>(\n    arg:\n      | ((\n          shape: HTMLElementModelShape<T, M>\n        ) => Partial<HTMLElementModelShape<T, CM>>)\n      | Partial<HTMLElementModelShape<T, CM>>\n  ): HTMLElementModel<T, CM> {\n    const properties = typeof arg === 'function' ? arg(this) : arg;\n    return new HTMLElementModel<T, CM>({\n      ...this,\n      ...properties\n    });\n  }\n}\n"]}