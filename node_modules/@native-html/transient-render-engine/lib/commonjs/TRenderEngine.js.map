{"version": 3, "sources": ["TRenderEngine.ts"], "names": ["createStylesConfig", "options", "enableUserAgentStyles", "stylesConfig", "defaultStylesConfig", "baseStyle", "TRenderEngine", "constructor", "hoistingEnabled", "dangerouslyDisableHoisting", "whitespaceCollapsingEnabled", "dangerouslyDisableWhitespaceCollapsing", "modelRegistry", "HTMLModelRegistry", "customizeHTMLModels", "userSelectedFontSize", "cssProcessorConfig", "rootFontSize", "fontSize", "stylesMerger", "TStylesMerger", "defaultCSSProcessorConfig", "htmlParserOptions", "decodeEntities", "lowerCaseTags", "ignoredTags", "ignoredDomTags", "ignoreNode", "ignoreDomNode", "visitors", "domVisitors", "dataFlowParams", "setMarkersForTNode", "baseStyles", "TStyles", "compileStyleDeclaration", "removeLineBreaksAroundEastAsianDiscardSet", "selectDomRoot", "normalizeDocument", "document", "body", "head", "child", "children", "tagName", "Element", "childNodes", "for<PERSON>ach", "c", "parent", "parentNode", "parseDocument", "html", "selected", "buildTTreeFromDoc", "tdoc", "hoistedTDoc", "collapsedTDoc", "buildTTree", "getHTMLElementsModels", "modelRecords"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAEA;;AACA;;AAIA;;AAEA;;AACA;;AACA;;AACA;;AAIA;;;;;;AAwGA,SAASA,kBAAT,CACEC,OADF,EAE0B;AAAA;;AACxB,QAAMC,qBAAqB,GACzB,QAAOD,OAAP,aAAOA,OAAP,gDAAOA,OAAO,CAAEE,YAAhB,0DAAO,sBAAuBD,qBAA9B,MAAwD,SAAxD,GACID,OAAO,CAACE,YAAR,CAAqBD,qBADzB,GAEIE,8BAAoBF,qBAH1B;AAIA,QAAMG,SAAS,GAAG,EAChB,IAAIH,qBAAqB,GACrBE,8BAAoBC,SADC,GAErB,mBAAK,CAAC,UAAD,CAAL,EAAmBD,8BAAoBC,SAAvC,CAFJ,CADgB;AAIhB,QAAGJ,OAAH,aAAGA,OAAH,iDAAGA,OAAO,CAAEE,YAAZ,2DAAG,uBAAuBE,SAA1B;AAJgB,GAAlB;AAMA,SAAO,EACL,GAAGD,6BADE;AAEL,QAAGH,OAAH,aAAGA,OAAH,uBAAGA,OAAO,CAAEE,YAAZ,CAFK;AAGLE,IAAAA;AAHK,GAAP;AAKD;AAED;AACA;AACA;AACA;AACA;;;AACO,MAAMC,aAAN,CAAoB;AAMzBC,EAAAA,WAAW,CAACN,OAAD,EAAiC;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAC1C,UAAME,YAAY,GAAGH,kBAAkB,CAACC,OAAD,CAAvC;AACA,SAAKO,eAAL,GAAuB,2BAAEP,OAAF,aAAEA,OAAF,uBAAEA,OAAO,CAAEQ,0BAAX,yEAAyC,KAAzC,CAAvB;AACA,SAAKC,2BAAL,GAAmC,4BACjCT,OADiC,aACjCA,OADiC,uBACjCA,OAAO,CAAEU,sCADwB,2EACkB,KADlB,CAAnC;AAGA,UAAMC,aAAa,GAAG,IAAIC,0BAAJ,CAAsBZ,OAAtB,aAAsBA,OAAtB,uBAAsBA,OAAO,CAAEa,mBAA/B,CAAtB;AACA,UAAMC,oBAAoB,GACxB,CAAAd,OAAO,SAAP,IAAAA,OAAO,WAAP,qCAAAA,OAAO,CAAEe,kBAAT,gFAA6BC,YAA7B,+BACAd,YAAY,CAACE,SADb,0DACA,sBAAwBa,QADxB,CADF,CAP0C,CAU1C;;AACA,UAAMC,YAAY,GAAG,IAAIC,4BAAJ,CAAkBjB,YAAlB,EAAgCS,aAAhC,EAA+C,EAClE,GAAGS,uCAD+D;AAElE,UAAGpB,OAAH,aAAGA,OAAH,uBAAGA,OAAO,CAAEe,kBAAZ,CAFkE;AAGlEC,MAAAA,YAAY,EACV,OAAOF,oBAAP,KAAgC,QAAhC,GAA2CA,oBAA3C,GAAkE;AAJF,KAA/C,CAArB;AAMA,SAAKO,iBAAL,GAAyB;AACvBC,MAAAA,cAAc,EAAE,IADO;AAEvBC,MAAAA,aAAa,EAAE,IAFQ;AAGvBC,MAAAA,WAAW,EAAExB,OAAF,aAAEA,OAAF,uBAAEA,OAAO,CAAEyB,cAHC;AAIvBC,MAAAA,UAAU,EAAE1B,OAAF,aAAEA,OAAF,uBAAEA,OAAO,CAAE2B,aAJE;AAKvBC,MAAAA,QAAQ,EAAE5B,OAAF,aAAEA,OAAF,uBAAEA,OAAO,CAAE6B,WALI;AAMvB,UAAG7B,OAAH,aAAGA,OAAH,uBAAGA,OAAO,CAAEqB,iBAAZ;AANuB,KAAzB;AAQA,SAAKS,cAAL,GAAsB;AACpBZ,MAAAA,YADoB;AAEpBP,MAAAA,aAFoB;AAGpBoB,MAAAA,kBAAkB,EAAE/B,OAAF,aAAEA,OAAF,uBAAEA,OAAO,CAAE+B,kBAHT;AAIpBC,MAAAA,UAAU,EAAE,IAAIC,gBAAJ,CACVf,YAAY,CAACgB,uBAAb,CAAqChC,YAAY,CAACE,SAAlD,CADU,CAJQ;AAOpB+B,MAAAA,yCAAyC,EACvC,CAAAnC,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAEmC,yCAAT,KAAsD;AARpC,KAAtB;AAUA,SAAKC,aAAL,GAAqBpC,OAArB,aAAqBA,OAArB,uBAAqBA,OAAO,CAAEoC,aAA9B;AACD;;AAEOC,EAAAA,iBAAiB,CAACC,QAAD,EAAqB;AAC5C,QAAIC,IAAJ;AACA,QAAIC,IAAJ;;AACA,SAAK,MAAMC,KAAX,IAAoBH,QAAQ,CAACI,QAA7B,EAAuC;AACrC,UAAIH,IAAI,IAAIC,IAAZ,EAAkB;AAChB;AACD;;AACD,UAAI,4BAAaC,KAAb,KAAuBA,KAAK,CAACE,OAAN,KAAkB,MAA7C,EAAqD;AACnDJ,QAAAA,IAAI,GAAGE,KAAP;AACD;;AACD,UAAI,4BAAaA,KAAb,KAAuBA,KAAK,CAACE,OAAN,KAAkB,MAA7C,EAAqD;AACnDH,QAAAA,IAAI,GAAGC,KAAP;AACD;AACF,KAb2C,CAc5C;;;AACA,QAAI,CAACF,IAAD,IAAS,CAACC,IAAd,EAAoB;AAClBD,MAAAA,IAAI,GAAG,IAAIK,iBAAJ,CAAY,MAAZ,EAAoB,EAApB,CAAP;AACAL,MAAAA,IAAI,CAACM,UAAL,GAAkBP,QAAQ,CAACI,QAA3B;AACAJ,MAAAA,QAAQ,CAACI,QAAT,CAAkBI,OAAlB,CAA2BC,CAAD,IAAO;AAC/BA,QAAAA,CAAC,CAACC,MAAF,GAAWT,IAAX;AACAQ,QAAAA,CAAC,CAACE,UAAF,GAAeV,IAAf;AACD,OAHD;AAIAA,MAAAA,IAAI,CAACS,MAAL,GAAcV,QAAd;AACAC,MAAAA,IAAI,CAACU,UAAL,GAAkBX,QAAlB;AACAA,MAAAA,QAAQ,CAACO,UAAT,GAAsB,CAACN,IAAD,CAAtB;AACD;;AACD,WAAOD,QAAP;AACD;;AAEDY,EAAAA,aAAa,CAACC,IAAD,EAAe;AAC1B,QAAIb,QAAQ,GAAG,4BAAca,IAAd,EAAoB,KAAK9B,iBAAzB,CAAf;;AACA,QAAI,KAAKe,aAAT,EAAwB;AACtB,YAAMgB,QAAQ,GAAG,KAAKhB,aAAL,CAAmBE,QAAnB,CAAjB;;AACA,UAAIc,QAAQ,IAAIA,QAAQ,KAAKd,QAA7B,EAAuC;AACrCA,QAAAA,QAAQ,CAACO,UAAT,GAAsB,CAACO,QAAD,CAAtB;AACAA,QAAAA,QAAQ,CAACJ,MAAT,GAAkBV,QAAlB;AACD;AACF;;AACD,SAAK,MAAMG,KAAX,IAAoBH,QAAQ,CAACI,QAA7B,EAAuC;AACrC,UAAI,4BAAaD,KAAb,KAAuBA,KAAK,CAACE,OAAN,KAAkB,MAA7C,EAAqD;AACnDL,QAAAA,QAAQ,GAAGG,KAAX;AACA;AACD;AACF;;AACD,WAAO,KAAKJ,iBAAL,CAAuBC,QAAvB,CAAP;AACD;;AAEDe,EAAAA,iBAAiB,CAACf,QAAD,EAA0C;AACzD,UAAMgB,IAAI,GAAG,kCAAkBhB,QAAlB,EAA4B,KAAKR,cAAjC,CAAb;AACA,UAAMyB,WAAW,GAAG,KAAKhD,eAAL,GAAuB,kBAAM+C,IAAN,CAAvB,GAAqCA,IAAzD;AACA,UAAME,aAAa,GAAG,KAAK/C,2BAAL,GAClB,wBAAS8C,WAAT,CADkB,GAElBD,IAFJ;AAGA,WAAOE,aAAP;AACD;;AAEDC,EAAAA,UAAU,CAACN,IAAD,EAA0B;AAClC,WAAO,KAAKE,iBAAL,CAAuB,KAAKH,aAAL,CAAmBC,IAAnB,CAAvB,CAAP;AACD;;AAEDO,EAAAA,qBAAqB,GAAG;AACtB,WAAO,KAAK5B,cAAL,CAAoBnB,aAApB,CAAkCgD,YAAzC;AACD;;AA1GwB", "sourcesContent": ["import { collapse } from './flow/collapse';\nimport { hoist } from './flow/hoist';\nimport { translateDocument } from './flow/translate';\nimport { ParserOptions as HTMLParserOptions } from 'htmlparser2';\nimport omit from 'ramda/src/omit';\nimport {\n  CSSProcessorConfig,\n  defaultCSSProcessorConfig\n} from '@native-html/css-processor';\nimport parseDocument from './dom/parseDocument';\nimport { StylesConfig } from './styles/types';\nimport { TStylesMerger } from './styles/TStylesMerger';\nimport { defaultStylesConfig } from './styles/defaults';\nimport { TStyles } from './styles/TStyles';\nimport HTMLModelRegistry from './model/HTMLModelRegistry';\nimport { HTMLModelRecord, TagName } from './model/model-types';\nimport { DefaultHTMLElementModelsStatic } from './model/defaultHTMLElementModels';\nimport { DataFlowParams } from './flow/types';\nimport {\n  Document,\n  Element,\n  Node,\n  NodeWithChildren,\n  isDomElement\n} from './dom/dom-utils';\nimport { SetMarkersForTNode, TDocument } from './tree/tree-types';\nimport { DomHandlerOptions, DomVisitorCallbacks } from './dom/DomHandler';\n\nexport interface TRenderEngineOptions<E extends string = never> {\n  /**\n   * Customization for CSS inline processing.\n   */\n  readonly cssProcessorConfig?: Partial<CSSProcessorConfig>;\n  /**\n   * Options for htmlparser2 library parser.\n   */\n  readonly htmlParserOptions?: Readonly<HTMLParserOptions>;\n  /**\n   * Various configuration for styling.\n   */\n  readonly stylesConfig?: StylesConfig;\n  /**\n   * Customize supported tags in the engine.\n   *\n   * @remarks If you need to add new tags, always use lowercase names.\n   */\n  readonly customizeHTMLModels?: (\n    defaultHTMLElementModels: DefaultHTMLElementModelsStatic\n  ) => HTMLModelRecord<TagName | E>;\n  /**\n   * Remove line breaks around special east-asian characters such as defined here:\n   * https://www.w3.org/TR/2020/WD-css-text-3-20200429/#line-break-transform\n   *\n   * @defaultValue false\n   */\n  readonly removeLineBreaksAroundEastAsianDiscardSet?: boolean;\n  /**\n   * A list of tags which should not be included in the DOM.\n   */\n  readonly ignoredDomTags?: string[];\n\n  /**\n   * An object which callbacks will be invoked when a DOM element or text node\n   * has been parsed and its children attached.\n   *\n   * @remark Each callback is applied during parsing, thus with very little\n   * overhead. However, it means that one node next siblings won't be\n   * available. If you need some siblings logic, apply this logic to the\n   * children of this node.\n   */\n  readonly domVisitors?: DomVisitorCallbacks;\n\n  /**\n   * Ignore specific DOM nodes.\n   *\n   * **Warning**: when this function is invoked, the node has not yet been\n   * attached to its parent or siblings. Use the second argument (`parent`)\n   * if you need to perform logic based on parent.\n   *\n   * @remarks The function is applied during parsing, thus with very little\n   * overhead. However, it means that one node next siblings won't be\n   * available.\n   *\n   * @returns `true` if this node should not be included in the DOM, anything\n   * else otherwise.\n   */\n  readonly ignoreDomNode?: (\n    node: Node,\n    parent: NodeWithChildren\n  ) => boolean | void | unknown;\n\n  /**\n   * Select the DOM root before TTree generation. For example, you could\n   * iterate over children until you reach an article element and return this\n   * element.\n   *\n   * @remarks Applied after DOM parsing, before normalization and TTree\n   * construction. Before normalization implies that a body will be added in\n   * the tree **after** selecting root.\n   */\n  readonly selectDomRoot?: (node: NodeWithChildren) => any;\n\n  /**\n   * Customize markers logic by extracting markers from TNode properties such\n   * as classes, ids, attributes, tagName ...\n   *\n   * @remarks If you are using JavaScript, you can use module augmentation and\n   * declaration merging to add properties to the {@link Markers} shape.\n   */\n  readonly setMarkersForTNode?: SetMarkersForTNode;\n\n  /**\n   * Disable hoisting. Note that your layout might break!\n   */\n  readonly dangerouslyDisableHoisting?: boolean;\n  /**\n   * Disable whitespace collapsing. Especially useful if your html is\n   * being pre-processed server-side with a minifier.\n   */\n  readonly dangerouslyDisableWhitespaceCollapsing?: boolean;\n}\n\nfunction createStylesConfig(\n  options?: TRenderEngineOptions\n): Required<StylesConfig> {\n  const enableUserAgentStyles =\n    typeof options?.stylesConfig?.enableUserAgentStyles === 'boolean'\n      ? options.stylesConfig.enableUserAgentStyles\n      : defaultStylesConfig.enableUserAgentStyles;\n  const baseStyle = {\n    ...(enableUserAgentStyles\n      ? defaultStylesConfig.baseStyle\n      : omit(['fontSize'], defaultStylesConfig.baseStyle)),\n    ...options?.stylesConfig?.baseStyle\n  };\n  return {\n    ...defaultStylesConfig,\n    ...options?.stylesConfig,\n    baseStyle\n  };\n}\n\n/**\n * The Transient Render Engine.\n *\n * @public\n */\nexport class TRenderEngine {\n  private htmlParserOptions: Readonly<HTMLParserOptions & DomHandlerOptions>;\n  private dataFlowParams: DataFlowParams;\n  private hoistingEnabled: boolean;\n  private whitespaceCollapsingEnabled: boolean;\n  private selectDomRoot: TRenderEngineOptions['selectDomRoot'];\n  constructor(options?: TRenderEngineOptions) {\n    const stylesConfig = createStylesConfig(options);\n    this.hoistingEnabled = !(options?.dangerouslyDisableHoisting ?? false);\n    this.whitespaceCollapsingEnabled = !(\n      options?.dangerouslyDisableWhitespaceCollapsing ?? false\n    );\n    const modelRegistry = new HTMLModelRegistry(options?.customizeHTMLModels);\n    const userSelectedFontSize =\n      options?.cssProcessorConfig?.rootFontSize ||\n      stylesConfig.baseStyle?.fontSize;\n    // TODO log a warning when type is string\n    const stylesMerger = new TStylesMerger(stylesConfig, modelRegistry, {\n      ...defaultCSSProcessorConfig,\n      ...options?.cssProcessorConfig,\n      rootFontSize:\n        typeof userSelectedFontSize === 'number' ? userSelectedFontSize : 14\n    });\n    this.htmlParserOptions = {\n      decodeEntities: true,\n      lowerCaseTags: true,\n      ignoredTags: options?.ignoredDomTags,\n      ignoreNode: options?.ignoreDomNode,\n      visitors: options?.domVisitors,\n      ...options?.htmlParserOptions\n    };\n    this.dataFlowParams = {\n      stylesMerger,\n      modelRegistry,\n      setMarkersForTNode: options?.setMarkersForTNode,\n      baseStyles: new TStyles(\n        stylesMerger.compileStyleDeclaration(stylesConfig.baseStyle)\n      ),\n      removeLineBreaksAroundEastAsianDiscardSet:\n        options?.removeLineBreaksAroundEastAsianDiscardSet || false\n    };\n    this.selectDomRoot = options?.selectDomRoot;\n  }\n\n  private normalizeDocument(document: Document) {\n    let body: Element | undefined;\n    let head: Element | undefined;\n    for (const child of document.children) {\n      if (body && head) {\n        break;\n      }\n      if (isDomElement(child) && child.tagName === 'body') {\n        body = child;\n      }\n      if (isDomElement(child) && child.tagName === 'head') {\n        head = child;\n      }\n    }\n    //@ts-ignore\n    if (!body && !head) {\n      body = new Element('body', {});\n      body.childNodes = document.children;\n      document.children.forEach((c) => {\n        c.parent = body as Element;\n        c.parentNode = body as Element;\n      });\n      body.parent = document;\n      body.parentNode = document;\n      document.childNodes = [body];\n    }\n    return document;\n  }\n\n  parseDocument(html: string) {\n    let document = parseDocument(html, this.htmlParserOptions);\n    if (this.selectDomRoot) {\n      const selected = this.selectDomRoot(document) as Document;\n      if (selected && selected !== document) {\n        document.childNodes = [selected];\n        selected.parent = document;\n      }\n    }\n    for (const child of document.children) {\n      if (isDomElement(child) && child.tagName === 'html') {\n        document = child;\n        break;\n      }\n    }\n    return this.normalizeDocument(document);\n  }\n\n  buildTTreeFromDoc(document: Document | Element): TDocument {\n    const tdoc = translateDocument(document, this.dataFlowParams);\n    const hoistedTDoc = this.hoistingEnabled ? hoist(tdoc) : tdoc;\n    const collapsedTDoc = this.whitespaceCollapsingEnabled\n      ? collapse(hoistedTDoc)\n      : tdoc;\n    return collapsedTDoc as unknown as TDocument;\n  }\n\n  buildTTree(html: string): TDocument {\n    return this.buildTTreeFromDoc(this.parseDocument(html));\n  }\n\n  getHTMLElementsModels() {\n    return this.dataFlowParams.modelRegistry.modelRecords;\n  }\n}\n"]}