  Manifest android  
permission android.Manifest  RECORD_AUDIO android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  Activity android.app  applicationContext android.app.Activity  getAPPLICATIONContext android.app.Activity  getApplicationContext android.app.Activity  setApplicationContext android.app.Activity  Context android.content  getJSModule android.content.Context  getJSModule android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  MediaPlayer 
android.media  
MediaRecorder 
android.media  currentPosition android.media.MediaPlayer  duration android.media.MediaPlayer  equals android.media.MediaPlayer  getCURRENTPosition android.media.MediaPlayer  getCurrentPosition android.media.MediaPlayer  getDURATION android.media.MediaPlayer  getDuration android.media.MediaPlayer  getISPlaying android.media.MediaPlayer  getIsPlaying android.media.MediaPlayer  	isPlaying android.media.MediaPlayer  pause android.media.MediaPlayer  prepare android.media.MediaPlayer  release android.media.MediaPlayer  seekTo android.media.MediaPlayer  setCurrentPosition android.media.MediaPlayer  
setDataSource android.media.MediaPlayer  setDuration android.media.MediaPlayer  setOnCompletionListener android.media.MediaPlayer  setOnPreparedListener android.media.MediaPlayer  
setPlaying android.media.MediaPlayer  	setVolume android.media.MediaPlayer  start android.media.MediaPlayer  stop android.media.MediaPlayer  <SAM-CONSTRUCTOR> .android.media.MediaPlayer.OnCompletionListener  <SAM-CONSTRUCTOR> ,android.media.MediaPlayer.OnPreparedListener  AudioEncoder android.media.MediaRecorder  AudioSource android.media.MediaRecorder  OutputFormat android.media.MediaRecorder  equals android.media.MediaRecorder  getMAXAmplitude android.media.MediaRecorder  getMaxAmplitude android.media.MediaRecorder  maxAmplitude android.media.MediaRecorder  pause android.media.MediaRecorder  prepare android.media.MediaRecorder  release android.media.MediaRecorder  resume android.media.MediaRecorder  setAudioEncoder android.media.MediaRecorder  setAudioEncodingBitRate android.media.MediaRecorder  setAudioSamplingRate android.media.MediaRecorder  setAudioSource android.media.MediaRecorder  setMaxAmplitude android.media.MediaRecorder  
setOutputFile android.media.MediaRecorder  setOutputFormat android.media.MediaRecorder  start android.media.MediaRecorder  stop android.media.MediaRecorder  AAC (android.media.MediaRecorder.AudioEncoder  MIC 'android.media.MediaRecorder.AudioSource  MPEG_4 (android.media.MediaRecorder.OutputFormat  Uri android.net  parse android.net.Uri  Build 
android.os  Handler 
android.os  Looper 
android.os  SystemClock 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  equals android.os.Handler  postDelayed android.os.Handler  removeCallbacks android.os.Handler  
getMainLooper android.os.Looper  elapsedRealtime android.os.SystemClock  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  ActivityCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  requestPermissions  androidx.core.app.ActivityCompat  ActivityCompat !com.dooboolab.audiorecorderplayer  	Arguments !com.dooboolab.audiorecorderplayer  Array !com.dooboolab.audiorecorderplayer  Arrays !com.dooboolab.audiorecorderplayer  Boolean !com.dooboolab.audiorecorderplayer  Build !com.dooboolab.audiorecorderplayer  Double !com.dooboolab.audiorecorderplayer  	Exception !com.dooboolab.audiorecorderplayer  Handler !com.dooboolab.audiorecorderplayer  HashMap !com.dooboolab.audiorecorderplayer  Int !com.dooboolab.audiorecorderplayer  IntArray !com.dooboolab.audiorecorderplayer  List !com.dooboolab.audiorecorderplayer  Log !com.dooboolab.audiorecorderplayer  Looper !com.dooboolab.audiorecorderplayer  Manifest !com.dooboolab.audiorecorderplayer  MediaPlayer !com.dooboolab.audiorecorderplayer  
MediaRecorder !com.dooboolab.audiorecorderplayer  
MutableMap !com.dooboolab.audiorecorderplayer  NullPointerException !com.dooboolab.audiorecorderplayer  PackageManager !com.dooboolab.audiorecorderplayer  Promise !com.dooboolab.audiorecorderplayer  RCTDeviceEventEmitter !com.dooboolab.audiorecorderplayer  RNAudioRecorderPlayerModule !com.dooboolab.audiorecorderplayer  RNAudioRecorderPlayerPackage !com.dooboolab.audiorecorderplayer  ReactApplicationContext !com.dooboolab.audiorecorderplayer  ReactContext !com.dooboolab.audiorecorderplayer  ReactContextBaseJavaModule !com.dooboolab.audiorecorderplayer  ReactMethod !com.dooboolab.audiorecorderplayer  ReadableMap !com.dooboolab.audiorecorderplayer  Runnable !com.dooboolab.audiorecorderplayer  RuntimeException !com.dooboolab.audiorecorderplayer  String !com.dooboolab.audiorecorderplayer  SystemClock !com.dooboolab.audiorecorderplayer  Timer !com.dooboolab.audiorecorderplayer  	TimerTask !com.dooboolab.audiorecorderplayer  Uri !com.dooboolab.audiorecorderplayer  WritableMap !com.dooboolab.audiorecorderplayer  _meteringEnabled !com.dooboolab.audiorecorderplayer  arrayOf !com.dooboolab.audiorecorderplayer  defaultFileName !com.dooboolab.audiorecorderplayer  	emptyList !com.dooboolab.audiorecorderplayer  invoke !com.dooboolab.audiorecorderplayer  java !com.dooboolab.audiorecorderplayer  let !com.dooboolab.audiorecorderplayer  log10 !com.dooboolab.audiorecorderplayer  
mediaRecorder !com.dooboolab.audiorecorderplayer  
plusAssign !com.dooboolab.audiorecorderplayer  reactContext !com.dooboolab.audiorecorderplayer  
recordHandler !com.dooboolab.audiorecorderplayer  	sendEvent !com.dooboolab.audiorecorderplayer  subsDurationMillis !com.dooboolab.audiorecorderplayer  tag !com.dooboolab.audiorecorderplayer  totalPausedRecordTime !com.dooboolab.audiorecorderplayer  ActivityCompat =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  	Arguments =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Array =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Boolean =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Build =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Double =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  	Exception =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Handler =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  HashMap =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  IOException =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Int =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  IntArray =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Log =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Looper =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Manifest =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  MediaPlayer =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  
MediaRecorder =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  
MutableMap =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  NullPointerException =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  PackageManager =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Promise =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  RCTDeviceEventEmitter =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  ReactApplicationContext =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  ReactContext =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  ReactMethod =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  ReadableMap =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Runnable =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  RuntimeException =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  String =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  SystemClock =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Timer =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  	TimerTask =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  Uri =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  WritableMap =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  _meteringEnabled =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  arrayOf =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  audioFileURL =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  currentActivity =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  defaultFileName =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  
getARRAYOf =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  
getArrayOf =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  getCURRENTActivity =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  getCurrentActivity =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  getDEFAULTFileName =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  getDefaultFileName =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  getLET =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  getLet =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  getLog10 =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  
getPLUSAssign =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  
getPlusAssign =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  getTAG =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  getTag =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  java =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  let =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  log10 =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  mTask =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  mTimer =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  mediaPlayer =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  
mediaRecorder =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  pausedRecordTime =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  
plusAssign =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  reactContext =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  
recordHandler =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  recorderRunnable =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  	sendEvent =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  setCurrentActivity =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  subsDurationMillis =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  tag =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  totalPausedRecordTime =com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule  ActivityCompat Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  	Arguments Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Array Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Boolean Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Build Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Double Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  	Exception Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Handler Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  HashMap Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  IOException Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Int Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  IntArray Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Log Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Looper Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Manifest Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  MediaPlayer Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  
MediaRecorder Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  
MutableMap Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  NullPointerException Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  PackageManager Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Promise Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  RCTDeviceEventEmitter Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  ReactApplicationContext Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  ReactContext Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  ReactMethod Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  ReadableMap Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Runnable Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  RuntimeException Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  String Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  SystemClock Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Timer Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  	TimerTask Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  Uri Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  WritableMap Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  _meteringEnabled Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  arrayOf Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  defaultFileName Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  
getARRAYOf Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  
getArrayOf Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  getLET Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  getLet Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  getLog10 Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  
getPLUSAssign Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  
getPlusAssign Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  invoke Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  java Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  let Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  log10 Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  
mediaRecorder Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  
plusAssign Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  reactContext Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  
recordHandler Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  	sendEvent Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  subsDurationMillis Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  tag Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  totalPausedRecordTime Gcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.Companion  getREACTContext hcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startPlayer.<anonymous>.<no name provided>  getReactContext hcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startPlayer.<anonymous>.<no name provided>  getSENDEvent hcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startPlayer.<anonymous>.<no name provided>  getSendEvent hcom.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startPlayer.<anonymous>.<no name provided>  getLog10 ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getMEDIARecorder ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getMediaRecorder ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getREACTContext ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getRECORDHandler ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getReactContext ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getRecordHandler ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getSENDEvent ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getSUBSDurationMillis ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getSendEvent ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getSubsDurationMillis ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getTOTALPausedRecordTime ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  getTotalPausedRecordTime ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  get_meteringEnabled ^com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerModule.startRecorder.<no name provided>  Arrays >com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage  List >com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage  NativeModule >com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage  RNAudioRecorderPlayerModule >com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage  ReactApplicationContext >com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage  ViewManager >com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage  	emptyList >com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage  getEMPTYList >com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage  getEmptyList >com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage  invoke >com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage  ReactPackage com.facebook.react  ActivityCompat com.facebook.react.bridge  	Arguments com.facebook.react.bridge  Build com.facebook.react.bridge  	Exception com.facebook.react.bridge  Handler com.facebook.react.bridge  HashMap com.facebook.react.bridge  Log com.facebook.react.bridge  Looper com.facebook.react.bridge  Manifest com.facebook.react.bridge  MediaPlayer com.facebook.react.bridge  
MediaRecorder com.facebook.react.bridge  NativeModule com.facebook.react.bridge  NullPointerException com.facebook.react.bridge  PackageManager com.facebook.react.bridge  Promise com.facebook.react.bridge  RCTDeviceEventEmitter com.facebook.react.bridge  ReactApplicationContext com.facebook.react.bridge  ReactContext com.facebook.react.bridge  ReactContextBaseJavaModule com.facebook.react.bridge  ReactMethod com.facebook.react.bridge  ReadableMap com.facebook.react.bridge  ReadableMapKeySetIterator com.facebook.react.bridge  Runnable com.facebook.react.bridge  RuntimeException com.facebook.react.bridge  SystemClock com.facebook.react.bridge  Timer com.facebook.react.bridge  	TimerTask com.facebook.react.bridge  Uri com.facebook.react.bridge  WritableMap com.facebook.react.bridge  _meteringEnabled com.facebook.react.bridge  arrayOf com.facebook.react.bridge  defaultFileName com.facebook.react.bridge  java com.facebook.react.bridge  let com.facebook.react.bridge  log10 com.facebook.react.bridge  
mediaRecorder com.facebook.react.bridge  
plusAssign com.facebook.react.bridge  reactContext com.facebook.react.bridge  
recordHandler com.facebook.react.bridge  	sendEvent com.facebook.react.bridge  subsDurationMillis com.facebook.react.bridge  tag com.facebook.react.bridge  totalPausedRecordTime com.facebook.react.bridge  	createMap #com.facebook.react.bridge.Arguments  ActivityCompat (com.facebook.react.bridge.BaseJavaModule  	Arguments (com.facebook.react.bridge.BaseJavaModule  Array (com.facebook.react.bridge.BaseJavaModule  Boolean (com.facebook.react.bridge.BaseJavaModule  Build (com.facebook.react.bridge.BaseJavaModule  Double (com.facebook.react.bridge.BaseJavaModule  	Exception (com.facebook.react.bridge.BaseJavaModule  Handler (com.facebook.react.bridge.BaseJavaModule  HashMap (com.facebook.react.bridge.BaseJavaModule  IOException (com.facebook.react.bridge.BaseJavaModule  Int (com.facebook.react.bridge.BaseJavaModule  IntArray (com.facebook.react.bridge.BaseJavaModule  Log (com.facebook.react.bridge.BaseJavaModule  Looper (com.facebook.react.bridge.BaseJavaModule  Manifest (com.facebook.react.bridge.BaseJavaModule  MediaPlayer (com.facebook.react.bridge.BaseJavaModule  
MediaRecorder (com.facebook.react.bridge.BaseJavaModule  
MutableMap (com.facebook.react.bridge.BaseJavaModule  NullPointerException (com.facebook.react.bridge.BaseJavaModule  PackageManager (com.facebook.react.bridge.BaseJavaModule  Promise (com.facebook.react.bridge.BaseJavaModule  RCTDeviceEventEmitter (com.facebook.react.bridge.BaseJavaModule  ReactApplicationContext (com.facebook.react.bridge.BaseJavaModule  ReactContext (com.facebook.react.bridge.BaseJavaModule  ReactMethod (com.facebook.react.bridge.BaseJavaModule  ReadableMap (com.facebook.react.bridge.BaseJavaModule  Runnable (com.facebook.react.bridge.BaseJavaModule  RuntimeException (com.facebook.react.bridge.BaseJavaModule  String (com.facebook.react.bridge.BaseJavaModule  SystemClock (com.facebook.react.bridge.BaseJavaModule  Timer (com.facebook.react.bridge.BaseJavaModule  	TimerTask (com.facebook.react.bridge.BaseJavaModule  Uri (com.facebook.react.bridge.BaseJavaModule  WritableMap (com.facebook.react.bridge.BaseJavaModule  _meteringEnabled (com.facebook.react.bridge.BaseJavaModule  arrayOf (com.facebook.react.bridge.BaseJavaModule  defaultFileName (com.facebook.react.bridge.BaseJavaModule  java (com.facebook.react.bridge.BaseJavaModule  let (com.facebook.react.bridge.BaseJavaModule  log10 (com.facebook.react.bridge.BaseJavaModule  
mediaRecorder (com.facebook.react.bridge.BaseJavaModule  
plusAssign (com.facebook.react.bridge.BaseJavaModule  reactContext (com.facebook.react.bridge.BaseJavaModule  
recordHandler (com.facebook.react.bridge.BaseJavaModule  	sendEvent (com.facebook.react.bridge.BaseJavaModule  subsDurationMillis (com.facebook.react.bridge.BaseJavaModule  tag (com.facebook.react.bridge.BaseJavaModule  totalPausedRecordTime (com.facebook.react.bridge.BaseJavaModule  reject !com.facebook.react.bridge.Promise  resolve !com.facebook.react.bridge.Promise  cacheDir 1com.facebook.react.bridge.ReactApplicationContext  getCACHEDir 1com.facebook.react.bridge.ReactApplicationContext  getCacheDir 1com.facebook.react.bridge.ReactApplicationContext  setCacheDir 1com.facebook.react.bridge.ReactApplicationContext  getJSModule &com.facebook.react.bridge.ReactContext  ActivityCompat 4com.facebook.react.bridge.ReactContextBaseJavaModule  	Arguments 4com.facebook.react.bridge.ReactContextBaseJavaModule  Array 4com.facebook.react.bridge.ReactContextBaseJavaModule  Boolean 4com.facebook.react.bridge.ReactContextBaseJavaModule  Build 4com.facebook.react.bridge.ReactContextBaseJavaModule  Double 4com.facebook.react.bridge.ReactContextBaseJavaModule  	Exception 4com.facebook.react.bridge.ReactContextBaseJavaModule  Handler 4com.facebook.react.bridge.ReactContextBaseJavaModule  HashMap 4com.facebook.react.bridge.ReactContextBaseJavaModule  IOException 4com.facebook.react.bridge.ReactContextBaseJavaModule  Int 4com.facebook.react.bridge.ReactContextBaseJavaModule  IntArray 4com.facebook.react.bridge.ReactContextBaseJavaModule  Log 4com.facebook.react.bridge.ReactContextBaseJavaModule  Looper 4com.facebook.react.bridge.ReactContextBaseJavaModule  Manifest 4com.facebook.react.bridge.ReactContextBaseJavaModule  MediaPlayer 4com.facebook.react.bridge.ReactContextBaseJavaModule  
MediaRecorder 4com.facebook.react.bridge.ReactContextBaseJavaModule  
MutableMap 4com.facebook.react.bridge.ReactContextBaseJavaModule  NullPointerException 4com.facebook.react.bridge.ReactContextBaseJavaModule  PackageManager 4com.facebook.react.bridge.ReactContextBaseJavaModule  Promise 4com.facebook.react.bridge.ReactContextBaseJavaModule  RCTDeviceEventEmitter 4com.facebook.react.bridge.ReactContextBaseJavaModule  ReactApplicationContext 4com.facebook.react.bridge.ReactContextBaseJavaModule  ReactContext 4com.facebook.react.bridge.ReactContextBaseJavaModule  ReactMethod 4com.facebook.react.bridge.ReactContextBaseJavaModule  ReadableMap 4com.facebook.react.bridge.ReactContextBaseJavaModule  Runnable 4com.facebook.react.bridge.ReactContextBaseJavaModule  RuntimeException 4com.facebook.react.bridge.ReactContextBaseJavaModule  String 4com.facebook.react.bridge.ReactContextBaseJavaModule  SystemClock 4com.facebook.react.bridge.ReactContextBaseJavaModule  Timer 4com.facebook.react.bridge.ReactContextBaseJavaModule  	TimerTask 4com.facebook.react.bridge.ReactContextBaseJavaModule  Uri 4com.facebook.react.bridge.ReactContextBaseJavaModule  WritableMap 4com.facebook.react.bridge.ReactContextBaseJavaModule  _meteringEnabled 4com.facebook.react.bridge.ReactContextBaseJavaModule  arrayOf 4com.facebook.react.bridge.ReactContextBaseJavaModule  defaultFileName 4com.facebook.react.bridge.ReactContextBaseJavaModule  java 4com.facebook.react.bridge.ReactContextBaseJavaModule  let 4com.facebook.react.bridge.ReactContextBaseJavaModule  log10 4com.facebook.react.bridge.ReactContextBaseJavaModule  
mediaRecorder 4com.facebook.react.bridge.ReactContextBaseJavaModule  
plusAssign 4com.facebook.react.bridge.ReactContextBaseJavaModule  reactContext 4com.facebook.react.bridge.ReactContextBaseJavaModule  
recordHandler 4com.facebook.react.bridge.ReactContextBaseJavaModule  	sendEvent 4com.facebook.react.bridge.ReactContextBaseJavaModule  subsDurationMillis 4com.facebook.react.bridge.ReactContextBaseJavaModule  tag 4com.facebook.react.bridge.ReactContextBaseJavaModule  totalPausedRecordTime 4com.facebook.react.bridge.ReactContextBaseJavaModule  equals %com.facebook.react.bridge.ReadableMap  getInt %com.facebook.react.bridge.ReadableMap  	getString %com.facebook.react.bridge.ReadableMap  hasKey %com.facebook.react.bridge.ReadableMap  keySetIterator %com.facebook.react.bridge.ReadableMap  
hasNextKey 3com.facebook.react.bridge.ReadableMapKeySetIterator  nextKey 3com.facebook.react.bridge.ReadableMapKeySetIterator  	putDouble %com.facebook.react.bridge.WritableMap  putInt %com.facebook.react.bridge.WritableMap  DeviceEventManagerModule com.facebook.react.modules.core  PermissionListener com.facebook.react.modules.core  RCTDeviceEventEmitter 8com.facebook.react.modules.core.DeviceEventManagerModule  <SAM-CONSTRUCTOR> Ncom.facebook.react.modules.core.DeviceEventManagerModule.RCTDeviceEventEmitter  emit Ncom.facebook.react.modules.core.DeviceEventManagerModule.RCTDeviceEventEmitter  ViewManager com.facebook.react.uimanager  File java.io  IOException java.io  message java.io.IOException  ActivityCompat 	java.lang  	Arguments 	java.lang  Arrays 	java.lang  Build 	java.lang  Class 	java.lang  	Exception 	java.lang  Handler 	java.lang  HashMap 	java.lang  Log 	java.lang  Looper 	java.lang  Manifest 	java.lang  MediaPlayer 	java.lang  
MediaRecorder 	java.lang  NullPointerException 	java.lang  PackageManager 	java.lang  RCTDeviceEventEmitter 	java.lang  RNAudioRecorderPlayerModule 	java.lang  Runnable 	java.lang  RuntimeException 	java.lang  SystemClock 	java.lang  Timer 	java.lang  Uri 	java.lang  _meteringEnabled 	java.lang  arrayOf 	java.lang  defaultFileName 	java.lang  	emptyList 	java.lang  java 	java.lang  let 	java.lang  log10 	java.lang  
mediaRecorder 	java.lang  
plusAssign 	java.lang  reactContext 	java.lang  
recordHandler 	java.lang  	sendEvent 	java.lang  subsDurationMillis 	java.lang  tag 	java.lang  totalPausedRecordTime 	java.lang  message java.lang.Exception  toString java.lang.Exception  toString java.lang.NullPointerException  getLET java.lang.Runnable  getLet java.lang.Runnable  let java.lang.Runnable  run java.lang.Runnable  message java.lang.RuntimeException  toString java.lang.RuntimeException  ActivityCompat 	java.util  	Arguments 	java.util  Arrays 	java.util  Build 	java.util  	Exception 	java.util  Handler 	java.util  HashMap 	java.util  Log 	java.util  Looper 	java.util  Manifest 	java.util  MediaPlayer 	java.util  
MediaRecorder 	java.util  NullPointerException 	java.util  PackageManager 	java.util  Promise 	java.util  RCTDeviceEventEmitter 	java.util  RNAudioRecorderPlayerModule 	java.util  ReactApplicationContext 	java.util  ReactContext 	java.util  ReactContextBaseJavaModule 	java.util  ReactMethod 	java.util  ReadableMap 	java.util  Runnable 	java.util  RuntimeException 	java.util  SystemClock 	java.util  Timer 	java.util  	TimerTask 	java.util  Uri 	java.util  WritableMap 	java.util  _meteringEnabled 	java.util  arrayOf 	java.util  defaultFileName 	java.util  	emptyList 	java.util  java 	java.util  let 	java.util  log10 	java.util  
mediaRecorder 	java.util  
plusAssign 	java.util  reactContext 	java.util  
recordHandler 	java.util  	sendEvent 	java.util  subsDurationMillis 	java.util  tag 	java.util  totalPausedRecordTime 	java.util  asList java.util.Arrays  cancel java.util.Timer  equals java.util.Timer  schedule java.util.Timer  	Arguments java.util.TimerTask  reactContext java.util.TimerTask  	sendEvent java.util.TimerTask  ActivityCompat kotlin  Any kotlin  	Arguments kotlin  Array kotlin  Arrays kotlin  Boolean kotlin  Build kotlin  Double kotlin  	Exception kotlin  Float kotlin  	Function1 kotlin  Handler kotlin  HashMap kotlin  Int kotlin  IntArray kotlin  Log kotlin  Long kotlin  Looper kotlin  Manifest kotlin  MediaPlayer kotlin  
MediaRecorder kotlin  Nothing kotlin  NullPointerException kotlin  PackageManager kotlin  RCTDeviceEventEmitter kotlin  RNAudioRecorderPlayerModule kotlin  Runnable kotlin  RuntimeException kotlin  String kotlin  SystemClock kotlin  Timer kotlin  Unit kotlin  Uri kotlin  _meteringEnabled kotlin  arrayOf kotlin  defaultFileName kotlin  	emptyList kotlin  java kotlin  let kotlin  log10 kotlin  
mediaRecorder kotlin  
plusAssign kotlin  reactContext kotlin  
recordHandler kotlin  	sendEvent kotlin  subsDurationMillis kotlin  tag kotlin  totalPausedRecordTime kotlin  
getPLUSAssign kotlin.Long  
getPlusAssign kotlin.Long  getLET 
kotlin.String  getLet 
kotlin.String  ActivityCompat kotlin.annotation  	Arguments kotlin.annotation  Arrays kotlin.annotation  Build kotlin.annotation  	Exception kotlin.annotation  Handler kotlin.annotation  HashMap kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  Manifest kotlin.annotation  MediaPlayer kotlin.annotation  
MediaRecorder kotlin.annotation  NullPointerException kotlin.annotation  PackageManager kotlin.annotation  RCTDeviceEventEmitter kotlin.annotation  RNAudioRecorderPlayerModule kotlin.annotation  Runnable kotlin.annotation  RuntimeException kotlin.annotation  SystemClock kotlin.annotation  Timer kotlin.annotation  Uri kotlin.annotation  _meteringEnabled kotlin.annotation  arrayOf kotlin.annotation  defaultFileName kotlin.annotation  	emptyList kotlin.annotation  java kotlin.annotation  let kotlin.annotation  log10 kotlin.annotation  
mediaRecorder kotlin.annotation  
plusAssign kotlin.annotation  reactContext kotlin.annotation  
recordHandler kotlin.annotation  	sendEvent kotlin.annotation  subsDurationMillis kotlin.annotation  tag kotlin.annotation  totalPausedRecordTime kotlin.annotation  ActivityCompat kotlin.collections  	Arguments kotlin.collections  Arrays kotlin.collections  Build kotlin.collections  	Exception kotlin.collections  Handler kotlin.collections  HashMap kotlin.collections  List kotlin.collections  Log kotlin.collections  Looper kotlin.collections  Manifest kotlin.collections  MediaPlayer kotlin.collections  
MediaRecorder kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  NullPointerException kotlin.collections  PackageManager kotlin.collections  RCTDeviceEventEmitter kotlin.collections  RNAudioRecorderPlayerModule kotlin.collections  Runnable kotlin.collections  RuntimeException kotlin.collections  SystemClock kotlin.collections  Timer kotlin.collections  Uri kotlin.collections  _meteringEnabled kotlin.collections  arrayOf kotlin.collections  defaultFileName kotlin.collections  	emptyList kotlin.collections  java kotlin.collections  let kotlin.collections  log10 kotlin.collections  
mediaRecorder kotlin.collections  
plusAssign kotlin.collections  reactContext kotlin.collections  
recordHandler kotlin.collections  	sendEvent kotlin.collections  subsDurationMillis kotlin.collections  tag kotlin.collections  totalPausedRecordTime kotlin.collections  ActivityCompat kotlin.comparisons  	Arguments kotlin.comparisons  Arrays kotlin.comparisons  Build kotlin.comparisons  	Exception kotlin.comparisons  Handler kotlin.comparisons  HashMap kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  Manifest kotlin.comparisons  MediaPlayer kotlin.comparisons  
MediaRecorder kotlin.comparisons  NullPointerException kotlin.comparisons  PackageManager kotlin.comparisons  RCTDeviceEventEmitter kotlin.comparisons  RNAudioRecorderPlayerModule kotlin.comparisons  Runnable kotlin.comparisons  RuntimeException kotlin.comparisons  SystemClock kotlin.comparisons  Timer kotlin.comparisons  Uri kotlin.comparisons  _meteringEnabled kotlin.comparisons  arrayOf kotlin.comparisons  defaultFileName kotlin.comparisons  	emptyList kotlin.comparisons  java kotlin.comparisons  let kotlin.comparisons  log10 kotlin.comparisons  
mediaRecorder kotlin.comparisons  
plusAssign kotlin.comparisons  reactContext kotlin.comparisons  
recordHandler kotlin.comparisons  	sendEvent kotlin.comparisons  subsDurationMillis kotlin.comparisons  tag kotlin.comparisons  totalPausedRecordTime kotlin.comparisons  ActivityCompat 	kotlin.io  	Arguments 	kotlin.io  Arrays 	kotlin.io  Build 	kotlin.io  	Exception 	kotlin.io  Handler 	kotlin.io  HashMap 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  Manifest 	kotlin.io  MediaPlayer 	kotlin.io  
MediaRecorder 	kotlin.io  NullPointerException 	kotlin.io  PackageManager 	kotlin.io  RCTDeviceEventEmitter 	kotlin.io  RNAudioRecorderPlayerModule 	kotlin.io  Runnable 	kotlin.io  RuntimeException 	kotlin.io  SystemClock 	kotlin.io  Timer 	kotlin.io  Uri 	kotlin.io  _meteringEnabled 	kotlin.io  arrayOf 	kotlin.io  defaultFileName 	kotlin.io  	emptyList 	kotlin.io  java 	kotlin.io  let 	kotlin.io  log10 	kotlin.io  
mediaRecorder 	kotlin.io  
plusAssign 	kotlin.io  reactContext 	kotlin.io  
recordHandler 	kotlin.io  	sendEvent 	kotlin.io  subsDurationMillis 	kotlin.io  tag 	kotlin.io  totalPausedRecordTime 	kotlin.io  ActivityCompat 
kotlin.jvm  	Arguments 
kotlin.jvm  Arrays 
kotlin.jvm  Build 
kotlin.jvm  	Exception 
kotlin.jvm  Handler 
kotlin.jvm  HashMap 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  Manifest 
kotlin.jvm  MediaPlayer 
kotlin.jvm  
MediaRecorder 
kotlin.jvm  NullPointerException 
kotlin.jvm  PackageManager 
kotlin.jvm  RCTDeviceEventEmitter 
kotlin.jvm  RNAudioRecorderPlayerModule 
kotlin.jvm  Runnable 
kotlin.jvm  RuntimeException 
kotlin.jvm  SystemClock 
kotlin.jvm  Timer 
kotlin.jvm  Uri 
kotlin.jvm  _meteringEnabled 
kotlin.jvm  arrayOf 
kotlin.jvm  defaultFileName 
kotlin.jvm  	emptyList 
kotlin.jvm  java 
kotlin.jvm  let 
kotlin.jvm  log10 
kotlin.jvm  
mediaRecorder 
kotlin.jvm  
plusAssign 
kotlin.jvm  reactContext 
kotlin.jvm  
recordHandler 
kotlin.jvm  	sendEvent 
kotlin.jvm  subsDurationMillis 
kotlin.jvm  tag 
kotlin.jvm  totalPausedRecordTime 
kotlin.jvm  log10 kotlin.math  ActivityCompat 
kotlin.ranges  	Arguments 
kotlin.ranges  Arrays 
kotlin.ranges  Build 
kotlin.ranges  	Exception 
kotlin.ranges  Handler 
kotlin.ranges  HashMap 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  Manifest 
kotlin.ranges  MediaPlayer 
kotlin.ranges  
MediaRecorder 
kotlin.ranges  NullPointerException 
kotlin.ranges  PackageManager 
kotlin.ranges  RCTDeviceEventEmitter 
kotlin.ranges  RNAudioRecorderPlayerModule 
kotlin.ranges  Runnable 
kotlin.ranges  RuntimeException 
kotlin.ranges  SystemClock 
kotlin.ranges  Timer 
kotlin.ranges  Uri 
kotlin.ranges  _meteringEnabled 
kotlin.ranges  arrayOf 
kotlin.ranges  defaultFileName 
kotlin.ranges  	emptyList 
kotlin.ranges  java 
kotlin.ranges  let 
kotlin.ranges  log10 
kotlin.ranges  
mediaRecorder 
kotlin.ranges  
plusAssign 
kotlin.ranges  reactContext 
kotlin.ranges  
recordHandler 
kotlin.ranges  	sendEvent 
kotlin.ranges  subsDurationMillis 
kotlin.ranges  tag 
kotlin.ranges  totalPausedRecordTime 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  ActivityCompat kotlin.sequences  	Arguments kotlin.sequences  Arrays kotlin.sequences  Build kotlin.sequences  	Exception kotlin.sequences  Handler kotlin.sequences  HashMap kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  Manifest kotlin.sequences  MediaPlayer kotlin.sequences  
MediaRecorder kotlin.sequences  NullPointerException kotlin.sequences  PackageManager kotlin.sequences  RCTDeviceEventEmitter kotlin.sequences  RNAudioRecorderPlayerModule kotlin.sequences  Runnable kotlin.sequences  RuntimeException kotlin.sequences  SystemClock kotlin.sequences  Timer kotlin.sequences  Uri kotlin.sequences  _meteringEnabled kotlin.sequences  arrayOf kotlin.sequences  defaultFileName kotlin.sequences  	emptyList kotlin.sequences  java kotlin.sequences  let kotlin.sequences  log10 kotlin.sequences  
mediaRecorder kotlin.sequences  
plusAssign kotlin.sequences  reactContext kotlin.sequences  
recordHandler kotlin.sequences  	sendEvent kotlin.sequences  subsDurationMillis kotlin.sequences  tag kotlin.sequences  totalPausedRecordTime kotlin.sequences  ActivityCompat kotlin.text  	Arguments kotlin.text  Arrays kotlin.text  Build kotlin.text  	Exception kotlin.text  Handler kotlin.text  HashMap kotlin.text  Log kotlin.text  Looper kotlin.text  Manifest kotlin.text  MediaPlayer kotlin.text  
MediaRecorder kotlin.text  NullPointerException kotlin.text  PackageManager kotlin.text  RCTDeviceEventEmitter kotlin.text  RNAudioRecorderPlayerModule kotlin.text  Runnable kotlin.text  RuntimeException kotlin.text  SystemClock kotlin.text  Timer kotlin.text  Uri kotlin.text  _meteringEnabled kotlin.text  arrayOf kotlin.text  defaultFileName kotlin.text  	emptyList kotlin.text  java kotlin.text  let kotlin.text  log10 kotlin.text  
mediaRecorder kotlin.text  
plusAssign kotlin.text  reactContext kotlin.text  
recordHandler kotlin.text  	sendEvent kotlin.text  subsDurationMillis kotlin.text  tag kotlin.text  totalPausedRecordTime kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       