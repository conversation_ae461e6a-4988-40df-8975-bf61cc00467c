<lint-module
    format="1"
    dir="/Users/<USER>/Projects/ModrkClient/node_modules/@react-native-firebase/messaging/android"
    name=":react-native-firebase_messaging"
    type="LIBRARY"
    maven="ModrkClient:react-native-firebase_messaging:unspecified"
    agpVersion="8.4.0"
    buildFolder="build"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-34/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      disable="GradleCompatible"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="GradleCompatible"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
