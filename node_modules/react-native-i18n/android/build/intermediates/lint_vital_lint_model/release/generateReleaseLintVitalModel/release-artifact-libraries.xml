<libraries>
  <library
      name="com.facebook.react:react-android:0.74.1:release@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/a351094198d582a3bd53743624458af0/transformed/jetified-react-android-0.74.1-release/jars/classes.jar"
      resolved="com.facebook.react:react-android:0.74.1"
      folder="/Users/<USER>/.gradle/caches/transforms-4/a351094198d582a3bd53743624458af0/transformed/jetified-react-android-0.74.1-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/6bfb408f2404c1ef80c188894c79b500/transformed/appcompat-1.6.1/jars/classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="/Users/<USER>/.gradle/caches/transforms-4/6bfb408f2404c1ef80c188894c79b500/transformed/appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/0023dc2ca08c92e9f833232dc30be149/transformed/fragment-1.3.6/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="/Users/<USER>/.gradle/caches/transforms-4/0023dc2ca08c92e9f833232dc30be149/transformed/fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/937cf256e95f64d3874e01d0ad0c0762/transformed/jetified-activity-1.6.0/jars/classes.jar"
      resolved="androidx.activity:activity:1.6.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/937cf256e95f64d3874e01d0ad0c0762/transformed/jetified-activity-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/068848ea3ea149e5300df61093fb1359/transformed/jetified-appcompat-resources-1.6.1/jars/classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="/Users/<USER>/.gradle/caches/transforms-4/068848ea3ea149e5300df61093fb1359/transformed/jetified-appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/b7a6ecda5c5f84582f878b45c87b9137/transformed/swiperefreshlayout-1.1.0/jars/classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/b7a6ecda5c5f84582f878b45c87b9137/transformed/swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/57c052604e2d47925e8895e0eb73fdf7/transformed/drawerlayout-1.0.0/jars/classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/57c052604e2d47925e8895e0eb73fdf7/transformed/drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/02838125c65dc7ad60f475ad736c1b65/transformed/vectordrawable-animated-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/02838125c65dc7ad60f475ad736c1b65/transformed/vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/b7534be0de369c38adc330cac7f2d992/transformed/vectordrawable-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/b7534be0de369c38adc330cac7f2d992/transformed/vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/59185d9771b2c35ac9ad1bc133d99ffa/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/59185d9771b2c35ac9ad1bc133d99ffa/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/4a485472c010951efdda24dae61efaa3/transformed/customview-1.0.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/4a485472c010951efdda24dae61efaa3/transformed/customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/1f9f3202874d7ca4d0d1f917b8030bf8/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/1f9f3202874d7ca4d0d1f917b8030bf8/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/ddc5eeed93acefbd0cae9e1e6b5c4863/transformed/jetified-lifecycle-viewmodel-savedstate-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1"
      folder="/Users/<USER>/.gradle/caches/transforms-4/ddc5eeed93acefbd0cae9e1e6b5c4863/transformed/jetified-lifecycle-viewmodel-savedstate-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.9.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/fbd4165a79c78e28156fb19b972321e6/transformed/jetified-core-ktx-1.9.0/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.9.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/fbd4165a79c78e28156fb19b972321e6/transformed/jetified-core-ktx-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.9.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/44fb890b3476edbdbdd38f9f2ba920ee/transformed/core-1.9.0/jars/classes.jar"
      resolved="androidx.core:core:1.9.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/44fb890b3476edbdbdd38f9f2ba920ee/transformed/core-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/815f394ad8c0f7e7109b84731d7327b6/transformed/cursoradapter-1.0.0/jars/classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/815f394ad8c0f7e7109b84731d7327b6/transformed/cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/fb11dd6889dc1e2d9cf46f61b57efa04/transformed/jetified-savedstate-1.2.0/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/fb11dd6889dc1e2d9cf46f61b57efa04/transformed/jetified-savedstate-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/bbaec28b5fa0e364c74566929d5aa4a4/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/bbaec28b5fa0e364c74566929d5aa4a4/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/fffc32e27d2dfe6c9987123f8f0bfad8/transformed/lifecycle-runtime-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.5.1"
      folder="/Users/<USER>/.gradle/caches/transforms-4/fffc32e27d2dfe6c9987123f8f0bfad8/transformed/lifecycle-runtime-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/41d362d191b1e9884b5512a5f937681e/transformed/lifecycle-viewmodel-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.5.1"
      folder="/Users/<USER>/.gradle/caches/transforms-4/41d362d191b1e9884b5512a5f937681e/transformed/lifecycle-viewmodel-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/345f39455373668e6d91990f8c7613fb/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/Users/<USER>/.gradle/caches/transforms-4/345f39455373668e6d91990f8c7613fb/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/b41933913e2f7e58b7f92c941b5f62b3/transformed/lifecycle-livedata-2.0.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/b41933913e2f7e58b7f92c941b5f62b3/transformed/lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/26e0c91520b9cf4138a571b9e4dd00b9/transformed/core-runtime-2.1.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/26e0c91520b9cf4138a571b9e4dd00b9/transformed/core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.1.0/b3152fc64428c9354344bd89848ecddc09b6f07e/core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/6ad9997dd8b2cbbf2d610af09ab2fe96/transformed/lifecycle-livedata-core-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.5.1"
      folder="/Users/<USER>/.gradle/caches/transforms-4/6ad9997dd8b2cbbf2d610af09ab2fe96/transformed/lifecycle-livedata-core-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.5.1/1fdb7349701e9cf2f0a69fc10642b6fef6bb3e12/lifecycle-common-2.5.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.5.1"/>
  <library
      name="androidx.annotation:annotation:1.3.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation/1.3.0/21f49f5f9b85fc49de712539f79123119740595/annotation-1.3.0.jar"
      resolved="androidx.annotation:annotation:1.3.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/bafd4fea3c0e71d770aa62543a9c335a/transformed/jetified-annotation-experimental-1.3.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/bafd4fea3c0e71d770aa62543a9c335a/transformed/jetified-annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/ede8b3f5e28300c4649516ca57472712/transformed/jetified-imagepipeline-okhttp3-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/ede8b3f5e28300c4649516ca57472712/transformed/jetified-imagepipeline-okhttp3-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/okhttp-urlconnection/4.9.2/3b9e64d3d56370bc7488ed8b336d17a8013cb336/okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.9.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/okhttp/4.9.2/5302714ee9320b64cf65ed865e5f65981ef9ba46/okhttp-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp:4.9.2"/>
  <library
      name="com.squareup.okio:okio:2.9.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okio/okio/2.9.0/dcc813b08ce5933f8bdfd1dfbab4ad4bd170e7a/okio-jvm-2.9.0.jar"
      resolved="com.squareup.okio:okio:2.9.0"/>
  <library
      name="com.facebook.fresco:fresco:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/933f76b141196cd54504511aefb9b9b2/transformed/jetified-fresco-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:fresco:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/933f76b141196cd54504511aefb9b9b2/transformed/jetified-fresco-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/9f00cb1dddeb481e7494b0807a546157/transformed/jetified-middleware-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:middleware:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/9f00cb1dddeb481e7494b0807a546157/transformed/jetified-middleware-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-common:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/599c3ba0e70823f3cea26991a86edc84/transformed/jetified-ui-common-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:ui-common:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/599c3ba0e70823f3cea26991a86edc84/transformed/jetified-ui-common-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fbcore:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/1e0d4c7f9dbcd2cf2415044efad5230e/transformed/jetified-fbcore-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:fbcore:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/1e0d4c7f9dbcd2cf2415044efad5230e/transformed/jetified-fbcore-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/1013e5a595ff449c60e16901abe2fd21/transformed/jetified-imagepipeline-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:imagepipeline:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/1013e5a595ff449c60e16901abe2fd21/transformed/jetified-imagepipeline-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/60b19591055bd3adc58ba40ac9983721/transformed/jetified-imagepipeline-base-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/60b19591055bd3adc58ba40ac9983721/transformed/jetified-imagepipeline-base-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.6.1/97fd74ccf54a863d221956ffcd21835e168e2aaa/kotlinx-coroutines-core-jvm-1.6.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.6.1/4e61fcdcc508cbaa37c4a284a50205d7c7767e37/kotlinx-coroutines-android-1.6.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.8.21/67f57e154437cd9e6e9cf368394b95814836ff88/kotlin-stdlib-jdk8-1.8.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.8.21/7473b8cd3c0ef9932345baf569bc398e8a717046/kotlin-stdlib-jdk7-1.8.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.22/d6c44cd08d8f3f9bece8101216dbe6553365c6e3/kotlin-stdlib-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.22"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="androidx.tracing:tracing:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/1ce6b8a5af6b360917c842088e42f9f6/transformed/jetified-tracing-1.1.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/1ce6b8a5af6b360917c842088e42f9f6/transformed/jetified-tracing-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/00f4a06f40517531a9fb60b323603dad/transformed/jetified-autofill-1.1.0/jars/classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/00f4a06f40517531a9fb60b323603dad/transformed/jetified-autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/ebc02de9cfe94c88a048ee8bd1c7f3d3/transformed/jetified-fbjni-0.6.0/jars/classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.6.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/ebc02de9cfe94c88a048ee8bd1c7f3d3/transformed/jetified-fbjni-0.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.10.5@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/a746068b9f65d031275a2c2971ab8133/transformed/jetified-soloader-0.10.5/jars/classes.jar"
      resolved="com.facebook.soloader:soloader:0.10.5"
      folder="/Users/<USER>/.gradle/caches/transforms-4/a746068b9f65d031275a2c2971ab8133/transformed/jetified-soloader-0.10.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.10.5@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.facebook.soloader/nativeloader/0.10.5/492cc5082540e19b29328f2f56c53255cb6e7cc6/nativeloader-0.10.5.jar"
      resolved="com.facebook.soloader:nativeloader:0.10.5"/>
  <library
      name="com.facebook.soloader:annotation:0.10.5@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.facebook.soloader/annotation/0.10.5/dc58463712cb3e5f03d8ee5ac9743b9ced9afa77/annotation-0.10.5.jar"
      resolved="com.facebook.soloader:annotation:0.10.5"/>
  <library
      name="com.facebook.fresco:drawee:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/e7ae402290eae2c4d89ad3f46b62c499/transformed/jetified-drawee-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:drawee:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/e7ae402290eae2c4d89ad3f46b62c499/transformed/jetified-drawee-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.facebook.infer.annotation/infer-annotation/0.18.0/27539793fe93ed7d92b6376281c16cda8278ab2f/infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.findbugs/jsr305/3.0.2/25ea2e8b0c338a877313bd4672d3fe056ea78f0d/jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-annotations-jvm/1.3.72/7dba6c57de526588d8080317bda0c14cd88c8055/kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/d915851380c11b9829d1e89a5b573849/transformed/jetified-imagepipeline-native-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/d915851380c11b9829d1e89a5b573849/transformed/jetified-imagepipeline-native-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/a2d16c24908edcd0c742dfd5e0a83c67/transformed/jetified-memory-type-ashmem-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/a2d16c24908edcd0c742dfd5e0a83c67/transformed/jetified-memory-type-ashmem-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/855334259653e4a8a0ff2351362447c4/transformed/jetified-memory-type-native-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:memory-type-native:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/855334259653e4a8a0ff2351362447c4/transformed/jetified-memory-type-native-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/d0d9a1e74e167ee21f7bbb89744c97a1/transformed/jetified-memory-type-java-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:memory-type-java:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/d0d9a1e74e167ee21f7bbb89744c97a1/transformed/jetified-memory-type-java-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/004aaa666f7e7dc55fcc6541e6cd934f/transformed/jetified-nativeimagefilters-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/004aaa666f7e7dc55fcc6541e6cd934f/transformed/jetified-nativeimagefilters-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/365a9d29d6dfb7a091423e0d01dd0916/transformed/jetified-nativeimagetranscoder-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/365a9d29d6dfb7a091423e0d01dd0916/transformed/jetified-nativeimagetranscoder-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.facebook.yoga/proguard-annotations/1.19.0/fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63/proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/javax.inject/javax.inject/1/6975da39a7040257bd51d21a231b76c915872d38/javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/96e198cd87407af3e3b9c2f8e8984d33/transformed/jetified-emoji2-views-helper-1.2.0/jars/classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/96e198cd87407af3e3b9c2f8e8984d33/transformed/jetified-emoji2-views-helper-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/d9a34b4b0891154d374f763b7390d37e/transformed/jetified-emoji2-1.2.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-4/d9a34b4b0891154d374f763b7390d37e/transformed/jetified-emoji2-1.2.0/jars/libs/repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-4/d9a34b4b0891154d374f763b7390d37e/transformed/jetified-emoji2-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/b03d71348b5f7c1b9185ad7a41a7e05f/transformed/jetified-lifecycle-process-2.4.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-4/b03d71348b5f7c1b9185ad7a41a7e05f/transformed/jetified-lifecycle-process-2.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/ab767466f2ca9c1d3512660c3ee6a257/transformed/jetified-startup-runtime-1.1.1/jars/classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="/Users/<USER>/.gradle/caches/transforms-4/ab767466f2ca9c1d3512660c3ee6a257/transformed/jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:soloader:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-4/9451841aa5f2294b906c2f2889525190/transformed/jetified-soloader-3.1.3/jars/classes.jar"
      resolved="com.facebook.fresco:soloader:3.1.3"
      folder="/Users/<USER>/.gradle/caches/transforms-4/9451841aa5f2294b906c2f2889525190/transformed/jetified-soloader-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.resourceinspection/resourceinspection-annotation/1.0.1/8c21f8ff5d96d5d52c948707f7e4d6ca6773feef/resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.0.0/c1e77e3ee6f4643b77496a1ddf7a2eef1aefdaa1/concurrent-futures-1.0.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.0.0"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.parse.bolts/bolts-tasks/1.4.0/d85884acf6810a3bbbecb587f239005cbc846dc4/bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/1.0/c949a840a6acbc5268d088e47b04177bf90b3cad/listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
