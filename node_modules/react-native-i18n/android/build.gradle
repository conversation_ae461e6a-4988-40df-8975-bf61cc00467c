apply plugin: 'com.android.library'

def _ext = rootProject.ext

def _compileSdkVersion = _ext.has('compileSdkVersion') ? _ext.compileSdkVersion : 26
def _buildToolsVersion = _ext.has('buildToolsVersion') ? _ext.buildToolsVersion : '26.0.3'
def _minSdkVersion = _ext.has('minSdkVersion') ? _ext.minSdkVersion : 16
def _targetSdkVersion = _ext.has('targetSdkVersion') ? _ext.targetSdkVersion : 26

android {
  compileSdkVersion _compileSdkVersion
  buildToolsVersion _buildToolsVersion

  defaultConfig {
    minSdkVersion _minSdkVersion
    targetSdkVersion _targetSdkVersion
  }

  lintOptions {
    abortOnError false
  }
}

dependencies {
  implementation "com.facebook.react:react-native:+" // From node_modules
}
