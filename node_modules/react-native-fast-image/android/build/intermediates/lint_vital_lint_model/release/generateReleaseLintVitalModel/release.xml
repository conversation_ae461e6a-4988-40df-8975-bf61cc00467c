<variant
    name="release"
    package="com.dylanvann.fastimage"
    minSdkVersion="23"
    targetSdkVersion="34"
    mergedManifest="build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml"
    manifestMergeReport="build/outputs/logs/manifest-merger-release-report.txt"
    proguardFiles="build/intermediates/default_proguard_files/global/proguard-android.txt-8.4.0"
    partialResultsDir="build/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/release/java:src/main/kotlin:src/release/kotlin"
        resDirectories="src/main/res:src/release/res"
        assetsDirectories="src/main/assets:src/release/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build/intermediates/javac/release/compileReleaseJavaWithJavac/classes:build/intermediates/compile_r_class_jar/release/generateReleaseRFile/R.jar"
      type="MAIN"
      applicationId="com.dylanvann.fastimage"
      generatedSourceFolders="build/generated/ap_generated_sources/release/out:build/generated/source/buildConfig/release"
      generatedResourceFolders="build/generated/res/resValues/release"
      desugaredMethodsFiles="/Users/<USER>/.gradle/caches/transforms-4/98d1e43d0e67512b150a16919edf7e80/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
