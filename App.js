import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { NavigationContainer } from '@react-navigation/native';
import { createStore, combineReducers, applyMiddleware } from 'redux';
import { Provider } from 'react-redux';
import ReduxThunk from 'redux-thunk';




import Splash from './src/screens/Splash';
import Login from './src/screens/Login';
import Home from './src/screens/Home';
import Live from './src/screens/Live';
import Regestration from './src/screens/Regestration';
import ConfirmationCode from './src/screens/ConfirmationCode';
import Cart from './src/screens/Cart';
import CompleteOrder0 from './src/screens/CompleteOrder0';
import CompleteOrder1 from './src/screens/CompleteOrder1';
import CompleteOrder2 from './src/screens/CompleteOrder2';
import CompleteOrder3 from './src/screens/CompleteOrder3';
import Map from './src/screens/Map';
import AddressDetails from './src/screens/AddressDetails';
import Addresses from './src/screens/Addresses';
import TermsAndConditions from './src/screens/TermsAndConditions';
import More from './src/screens/More';
import Favourites from './src/screens/Favourites';
import PartnershipOrders from './src/screens/PartnershipOrders';
import PartnershipOrdersDetails from './src/screens/PartnershipOrdersDetails';
import OrderTracking from './src/screens/OrderTracking';
import MoveOrderToSlaughter from './src/screens/MoveOrderToSlaughter';
import MyOrders from './src/screens/MyOrders';
import ChangePassword from './src/screens/ChangePassword';
import KnowMore from './src/screens/KnowMore';
import ArticleDetails from './src/screens/ArticleDetails';
import MyProfile from './src/screens/MyProfile';
import Notifications from './src/screens/Notifications';
import ForgetPassword from './src/screens/ForgetPassword';
import ForgetPasswordChange from './src/screens/ForgetPasswordChange';
import ComplaintsAndSuggestions from './src/screens/ComplaintsAndSuggestions';
import TraderAllContent from './src/screens/TraderAllContent';
import TwistingInTheMarket from './src/screens/TwistingInTheMarket';
import Test from './src/screens/Test';


import {
  LogBox,
  StatusBar,
} from 'react-native';

// Removed deprecated imports
import auth from './Store/Reducers/auth';
import farms from './Store/Reducers/farms';
import addresses from './Store/Reducers/addresses';
import cart from './Store/Reducers/cart';
import orders from './Store/Reducers/orders';
import settings from './Store/Reducers/settings';
import { Red } from './src/components/Styles';
const Stack = createStackNavigator();


function MyStack() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
      initialRouteName={'Splash'}
    >
      <Stack.Screen name="Splash" component={Splash} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Login" component={Login} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Home" component={Home} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Live" component={Live} />
      <Stack.Screen name="Regestration" component={Regestration} />
      <Stack.Screen name="ConfirmationCode" component={ConfirmationCode} />
      <Stack.Screen name="Cart" component={Cart} />
      <Stack.Screen name="CompleteOrder0" component={CompleteOrder0} />
      <Stack.Screen name="CompleteOrder1" component={CompleteOrder1} />
      <Stack.Screen name="CompleteOrder2" component={CompleteOrder2} />
      <Stack.Screen name="CompleteOrder3" component={CompleteOrder3} />
      <Stack.Screen name="Map" component={Map} />
      <Stack.Screen name="AddressDetails" component={AddressDetails} />
      <Stack.Screen name="Addresses" component={Addresses} />
      <Stack.Screen name="TermsAndConditions" component={TermsAndConditions} />
      <Stack.Screen name="More" component={More} />
      <Stack.Screen name="Favourites" component={Favourites} />
      <Stack.Screen name="PartnershipOrders" component={PartnershipOrders} />
      <Stack.Screen name="PartnershipOrdersDetails" component={PartnershipOrdersDetails} />
      <Stack.Screen name="OrderTracking" component={OrderTracking} />
      <Stack.Screen name="MoveOrderToSlaughter" component={MoveOrderToSlaughter} />
      <Stack.Screen name="MyOrders" component={MyOrders} />
      <Stack.Screen name="ChangePassword" component={ChangePassword} />
      <Stack.Screen name="KnowMore" component={KnowMore} />
      <Stack.Screen name="ArticleDetails" component={ArticleDetails} />
      <Stack.Screen name="MyProfile" component={MyProfile} />
      <Stack.Screen name="Notifications" component={Notifications} />
      <Stack.Screen name="ForgetPassword" component={ForgetPassword} />
      <Stack.Screen name="ForgetPasswordChange" component={ForgetPasswordChange} />
      <Stack.Screen name="ComplaintsAndSuggestions" component={ComplaintsAndSuggestions} />
      <Stack.Screen name="Test" component={Test} />
      <Stack.Screen name="TraderAllContent" component={TraderAllContent} />
      <Stack.Screen name="TwistingInTheMarket" component={TwistingInTheMarket} />

    </Stack.Navigator>
  );
}

const rootReducer = combineReducers({
  auth: auth,
  farms: farms,
  addresses: addresses,
  cart: cart,
  orders: orders,
  settings: settings,
});

const store = createStore(rootReducer, applyMiddleware(ReduxThunk));

const App = () => {
  LogBox.ignoreLogs(['Warning: ...']); // Ignore log notification by message
  LogBox.ignoreAllLogs();//Ignore all log notifications

  return (
    <>
      <StatusBar
        backgroundColor={Red}
        barStyle="light-content"
      />
      <Provider store={store}>
        <NavigationContainer>
          <MyStack />
        </NavigationContainer>
      </Provider>
    </>
  );
};

export default App;
