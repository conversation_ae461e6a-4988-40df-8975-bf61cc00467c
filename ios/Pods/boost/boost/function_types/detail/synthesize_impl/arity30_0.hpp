
// (C) Copyright <PERSON>
//
// Use modification and distribution are subject to the boost Software License,
// Version 1.0. (See http://www.boost.org/LICENSE_1_0.txt).

//------------------------------------------------------------------------------

// no include guards, this file is intended for multiple inclusion

// input:  BOOST_FT_syntax     type macro to use
// input:  BOOST_FT_cc         empty or cc specifier 
// input:  BOOST_FT_ell        empty or "..."
// input:  BOOST_FT_cv         empty or cv qualifiers
// input:  BOOST_FT_flags      single decimal integer encoding the flags
// output: BOOST_FT_n          number of component types (arity+1)
// output: BOOST_FT_arity      current arity
// output: BOOST_FT_type       macro that expands to the type
// output: BOOST_FT_tplargs(p) template arguments with given prefix
// output: BOOST_FT_params(p)  parameters with given prefix

# include <boost/function_types/detail/synthesize_impl/arity20_0.hpp>
# define BOOST_FT_make_type(flags,cc,arity) BOOST_FT_make_type_impl(flags,cc,arity)
# define BOOST_FT_make_type_impl(flags,cc,arity) make_type_ ## flags ## _ ## cc ## _ ## arity
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,21)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 22 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,21) 
< typename mpl::deref< iter_0 > ::type 
, typename mpl::deref< iter_1 > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,22)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 23 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,22) 
< typename mpl::deref< iter_0 > ::type 
, typename mpl::deref< iter_1 > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,23)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 24 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,23) 
< typename mpl::deref< iter_0 > ::type 
, typename mpl::deref< iter_1 > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,24)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 25 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,24) 
< typename mpl::deref< iter_0 > ::type 
, typename mpl::deref< iter_1 > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,25)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 26 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,25) 
< typename mpl::deref< iter_0 > ::type 
, typename mpl::deref< iter_1 > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,26)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 27 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,26) 
< typename mpl::deref< iter_0 > ::type 
, typename mpl::deref< iter_1 > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,27)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 28 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,27) 
< typename mpl::deref< iter_0 > ::type 
, typename mpl::deref< iter_1 > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,28)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 29 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,28) 
< typename mpl::deref< iter_0 > ::type 
, typename mpl::deref< iter_1 > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,29)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 30 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,29) 
< typename mpl::deref< iter_0 > ::type 
, typename mpl::deref< iter_1 > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,30)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 31 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,30) 
< typename mpl::deref< iter_0 > ::type 
, typename mpl::deref< iter_1 > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
> ::type type;
};
};
# undef BOOST_FT_make_type
# undef BOOST_FT_make_type_impl

