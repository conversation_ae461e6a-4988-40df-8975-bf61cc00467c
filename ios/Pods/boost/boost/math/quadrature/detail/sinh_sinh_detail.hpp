// Copyright <PERSON>, 2017
// Use, modification and distribution are subject to the
// Boost Software License, Version 1.0.
// (See accompanying file LICENSE_1_0.txt
// or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_MATH_QUADRATURE_DETAIL_SINH_SINH_DETAIL_HPP
#define BOOST_MATH_QUADRATURE_DETAIL_SINH_SINH_DETAIL_HPP

#include <cmath>
#include <string>
#include <vector>
#include <typeinfo>
#include <boost/math/constants/constants.hpp>
#include <boost/math/tools/atomic.hpp>
#include <boost/math/policies/error_handling.hpp>
#include <boost/math/special_functions/trunc.hpp>
#include <boost/math/tools/config.hpp>

#ifdef BOOST_HAS_THREADS
#include <mutex>
#endif

namespace boost{ namespace math{ namespace quadrature { namespace detail{


// Returns the sinh-sinh quadrature of a function f over the entire real line

template<class Real, class Policy>
class sinh_sinh_detail
{
   static const int initializer_selector =
      !std::numeric_limits<Real>::is_specialized || (std::numeric_limits<Real>::radix != 2) ?
      0 :
      (std::numeric_limits<Real>::digits < 30) && (std::numeric_limits<Real>::max_exponent <= 128) ?
      1 :
      (std::numeric_limits<Real>::digits <= std::numeric_limits<double>::digits) && (std::numeric_limits<Real>::max_exponent <= std::numeric_limits<double>::max_exponent) ?
      2 :
      (std::numeric_limits<Real>::digits <= std::numeric_limits<long double>::digits) && (std::numeric_limits<Real>::max_exponent <= 16384) ?
      3 :
#ifdef BOOST_HAS_FLOAT128
      (std::numeric_limits<Real>::digits <= 113) && (std::numeric_limits<Real>::max_exponent <= 16384) ?
      4 :
#endif
      0;
public:
    sinh_sinh_detail(size_t max_refinements);

    template<class F>
    auto integrate(const F f, Real tolerance, Real* error, Real* L1, std::size_t* levels) const ->decltype(std::declval<F>()(std::declval<Real>()));

private:

   const std::vector<Real>& get_abscissa_row(std::size_t n)const
   {
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
      if (m_committed_refinements.load() < n)
         extend_refinements();
      BOOST_MATH_ASSERT(m_committed_refinements.load() >= n);
#else
      if (m_committed_refinements < n)
         extend_refinements();
      BOOST_MATH_ASSERT(m_committed_refinements >= n);
#endif
      return m_abscissas[n];
   }
   const std::vector<Real>& get_weight_row(std::size_t n)const
   {
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
      if (m_committed_refinements.load() < n)
         extend_refinements();
      BOOST_MATH_ASSERT(m_committed_refinements.load() >= n);
#else
      if (m_committed_refinements < n)
         extend_refinements();
      BOOST_MATH_ASSERT(m_committed_refinements >= n);
#endif
      return m_weights[n];
   }
   void init(const std::integral_constant<int, 0>&);
   void init(const std::integral_constant<int, 1>&);
   void init(const std::integral_constant<int, 2>&);
   void init(const std::integral_constant<int, 3>&);
#ifdef BOOST_HAS_FLOAT128
   void init(const std::integral_constant<int, 4>&);
#endif

   void extend_refinements()const
   {
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
      std::lock_guard<std::mutex> guard(m_mutex);
#endif
      //
      // Check some other thread hasn't got here after we read the atomic variable, but before we got here:
      //
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
      if (m_committed_refinements.load() >= m_max_refinements)
         return;
#else
      if (m_committed_refinements >= m_max_refinements)
         return;
#endif

      using std::ldexp;
      using std::ceil;
      using std::sinh;
      using std::cosh;
      using std::exp;
      using constants::half_pi;

      std::size_t row = ++m_committed_refinements;

      Real h = ldexp(Real(1), -static_cast<int>(row));
      size_t k = static_cast<size_t>(boost::math::lltrunc(ceil(m_t_max / (2 * h))));
      m_abscissas[row].reserve(k);
      m_weights[row].reserve(k);
      Real arg = h;
      while (arg < m_t_max)
      {
         Real tmp = half_pi<Real>()*sinh(arg);
         Real x = sinh(tmp);
         m_abscissas[row].emplace_back(x);
         Real w = cosh(arg)*half_pi<Real>()*cosh(tmp);
         m_weights[row].emplace_back(w);
         arg += 2 * h;
      }
   }

   Real m_t_max;

   mutable std::vector<std::vector<Real>> m_abscissas;
   mutable std::vector<std::vector<Real>> m_weights;
   std::size_t                       m_max_refinements;
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   mutable boost::math::detail::atomic_unsigned_type      m_committed_refinements{};
   mutable std::mutex m_mutex;
#else
   mutable unsigned                  m_committed_refinements;
#endif
};

template<class Real, class Policy>
sinh_sinh_detail<Real, Policy>::sinh_sinh_detail(size_t max_refinements)
   : m_abscissas(max_refinements), m_weights(max_refinements), m_max_refinements(max_refinements)
{
   init(std::integral_constant<int, initializer_selector>());
}

template<class Real, class Policy>
template<class F>
auto sinh_sinh_detail<Real, Policy>::integrate(const F f, Real tolerance, Real* error, Real* L1, std::size_t* levels) const ->decltype(std::declval<F>()(std::declval<Real>()))
{
    using std::abs;
    using std::sqrt;
    using boost::math::constants::half;
    using boost::math::constants::half_pi;

    static const char* function = "boost::math::quadrature::sinh_sinh<%1%>::integrate";

    typedef decltype(f(static_cast<Real>(0))) K;
    static_assert(!std::is_integral<K>::value,
                  "The return type cannot be integral, it must be either a real or complex floating point type.");
    K y_max = f(boost::math::tools::max_value<Real>());
    if(abs(y_max) > boost::math::tools::epsilon<Real>())
    {
        return static_cast<K>(policies::raise_domain_error(function,
           "The function you are trying to integrate does not go to zero at infinity, and instead evaluates to %1%", y_max, Policy()));
    }

    K y_min = f(-boost::math::tools::max_value<Real>());
    if(abs(y_min) > boost::math::tools::epsilon<Real>())
    {
        return static_cast<K>(policies::raise_domain_error(function,
           "The function you are trying to integrate does not go to zero at -infinity, and instead evaluates to %1%", y_max, Policy()));
    }

    // Get the party started with two estimates of the integral:
    K I0 = f(0)*half_pi<Real>();
    Real L1_I0 = abs(I0);
    for(size_t i = 0; i < m_abscissas[0].size(); ++i)
    {
        Real x = m_abscissas[0][i];
        K yp = f(x);
        K ym = f(-x);
        I0 += (yp + ym)*m_weights[0][i];
        L1_I0 += (abs(yp)+abs(ym))*m_weights[0][i];
    }

    // Uncomment the estimates to work the convergence on the command line.
    // std::cout << std::setprecision(std::numeric_limits<Real>::digits10);
    // std::cout << "First estimate : " << I0 << std::endl;
    K I1 = I0;
    Real L1_I1 = L1_I0;
    for (size_t i = 0; i < m_abscissas[1].size(); ++i)
    {
        Real x= m_abscissas[1][i];
        K yp = f(x);
        K ym = f(-x);
        I1 += (yp + ym)*m_weights[1][i];
        L1_I1 += (abs(yp) + abs(ym))*m_weights[1][i];
    }

    I1 *= half<Real>();
    L1_I1 *= half<Real>();
    Real err = abs(I0 - I1);
    // std::cout << "Second estimate: " << I1 << " Error estimate at level " << 1 << " = " << err << std::endl;

    size_t i = 2;
    for(; i <= m_max_refinements; ++i)
    {
        I0 = I1;
        L1_I0 = L1_I1;

        I1 = half<Real>()*I0;
        L1_I1 = half<Real>()*L1_I0;
        Real h = static_cast<Real>(1) / static_cast<Real>(1 << i);
        K sum = 0;
        Real absum = 0;

        Real abterm1 = 1;
        Real eps = boost::math::tools::epsilon<Real>()*L1_I1;

        auto abscissa_row = get_abscissa_row(i);
        auto weight_row = get_weight_row(i);

        for(size_t j = 0; j < abscissa_row.size(); ++j)
        {
            Real x = abscissa_row[j];
            K yp = f(x);
            K ym = f(-x);
            sum += (yp + ym)*weight_row[j];
            Real abterm0 = (abs(yp) + abs(ym))*weight_row[j];
            absum += abterm0;

            // We require two consecutive terms to be < eps in case we hit a zero of f.
            if (x > static_cast<Real>(100) && abterm0 < eps && abterm1 < eps)
            {
                break;
            }
            abterm1 = abterm0;
        }

        I1 += sum*h;
        L1_I1 += absum*h;
        err = abs(I0 - I1);
        // std::cout << "Estimate:        " << I1 << " Error estimate at level " << i  << " = " << err << std::endl;
        if (!(boost::math::isfinite)(L1_I1))
        {
            const char* err_msg = "The sinh_sinh quadrature evaluated your function at a singular point, leading to the value %1%.\n"
               "sinh_sinh quadrature cannot handle singularities in the domain.\n"
               "If you are sure your function has no singularities, please submit a bug against boost.math\n";
            return static_cast<K>(policies::raise_evaluation_error(function, err_msg, I1, Policy()));
        }
        if (err <= tolerance*L1_I1)
        {
            break;
        }
    }

    if (error)
    {
        *error = err;
    }

    if (L1)
    {
        *L1 = L1_I1;
    }

    if (levels)
    {
       *levels = i;
    }

    return I1;
}

template<class Real, class Policy>
void sinh_sinh_detail<Real, Policy>::init(const std::integral_constant<int, 0>&)
{
   using std::log;
   using std::sqrt;
   using std::cosh;
   using std::sinh;
   using std::ceil;
   using boost::math::constants::two_div_pi;
   using boost::math::constants::half_pi;

   m_committed_refinements = 4;

   // t_max is chosen to make g'(t_max) ~ sqrt(max) (g' grows faster than g).
   // This will allow some flexibility on the users part; they can at least square a number function without overflow.
   // But there is no unique choice; the further out we can evaluate the function, the better we can do on slowly decaying integrands.
   m_t_max = log(2 * two_div_pi<Real>()*log(2 * two_div_pi<Real>()*sqrt(tools::max_value<Real>())));

   for (size_t i = 0; i <= 4; ++i)
   {
      Real h = static_cast<Real>(1) / static_cast<Real>(1 << i);
      size_t k = static_cast<size_t>(boost::math::lltrunc(ceil(m_t_max / (2 * h))));
      m_abscissas[i].reserve(k);
      m_weights[i].reserve(k);
      // We don't add 0 to the abscissas; that's treated as a special case.
      Real arg = h;
      while (arg < m_t_max)
      {
         Real tmp = half_pi<Real>()*sinh(arg);
         Real x = sinh(tmp);
         m_abscissas[i].emplace_back(x);
         Real w = cosh(arg)*half_pi<Real>()*cosh(tmp);
         m_weights[i].emplace_back(w);

         if (i != 0)
         {
            arg += 2 * h;
         }
         else
         {
            arg += h;
         }
      }
   }
}

template<class Real, class Policy>
void sinh_sinh_detail<Real, Policy>::init(const std::integral_constant<int, 1>&)
{
   m_abscissas = {
      { 3.08828742e+00f, 1.48993185e+02f, 3.41228925e+06f, 2.06932577e+18f, },
      { 9.13048763e-01f, 1.41578929e+01f, 6.70421552e+03f, 9.64172533e+10f, },
      { 4.07297690e-01f, 1.68206671e+00f, 6.15089799e+00f, 4.00396235e+01f, 7.92920025e+02f, 1.02984971e+05f, 3.03862311e+08f, 1.56544547e+14f, },
      { 1.98135272e-01f, 6.40155674e-01f, 1.24892870e+00f, 2.26608084e+00f, 4.29646270e+00f, 9.13029039e+00f, 2.31110765e+01f, 7.42770603e+01f, 3.26720921e+02f, 2.15948569e+03f, 2.41501526e+04f, 5.31819400e+05f, 2.80058686e+07f, 4.52406508e+09f, 3.08561257e+12f, 1.33882673e+16f, },
      { 9.83967894e-02f, 3.00605618e-01f, 5.19857979e-01f, 7.70362083e-01f, 1.07131137e+00f, 1.45056976e+00f, 1.95077855e+00f, 2.64003177e+00f, 3.63137237e+00f, 5.11991533e+00f, 7.45666098e+00f, 1.13022613e+01f, 1.79641069e+01f, 3.01781070e+01f, 5.40387580e+01f, 1.04107731e+02f, 2.18029520e+02f, 5.02155699e+02f, 1.28862131e+03f, 3.73921687e+03f, 1.24750730e+04f, 4.87639975e+04f, 2.28145658e+05f, 1.30877796e+06f, 9.46084663e+06f, 8.88883120e+07f, 1.12416883e+09f, 1.99127673e+10f, 5.16743469e+11f, 2.06721881e+13f, 1.35061503e+15f, 1.53854066e+17f, },
      { 4.91151004e-02f, 1.48013150e-01f, 2.48938814e-01f, 3.53325424e-01f, 4.62733557e-01f, 5.78912068e-01f, 7.03870253e-01f, 8.39965859e-01f, 9.90015066e-01f, 1.15743257e+00f, 1.34641276e+00f, 1.56216711e+00f, 1.81123885e+00f, 2.10192442e+00f, 2.44484389e+00f, 2.85372075e+00f, 3.34645891e+00f, 3.94664582e+00f, 4.68567310e+00f, 5.60576223e+00f, 6.76433234e+00f, 8.24038318e+00f, 1.01439436e+01f, 1.26302471e+01f, 1.59213040e+01f, 2.03392186e+01f, 2.63584645e+01f, 3.46892633e+01f, 4.64129147e+01f, 6.32055079e+01f, 8.77149726e+01f, 1.24209693e+02f, 1.79718635e+02f, 2.66081728e+02f, 4.03727303e+02f, 6.28811307e+02f, 1.00707984e+03f, 1.66156823e+03f, 2.82965144e+03f, 4.98438627e+03f, 9.10154693e+03f, 1.72689266e+04f, 3.41309958e+04f, 7.04566898e+04f, 1.52340422e+05f, 3.46047978e+05f, 8.28472421e+05f, 2.09759615e+06f, 5.63695080e+06f, 1.61407141e+07f, 4.94473068e+07f, 1.62781052e+08f, 5.78533297e+08f, 2.23083854e+09f, 9.38239131e+09f, 4.32814954e+10f, 2.20307274e+11f, 1.24524507e+12f, 7.86900053e+12f, 5.59953143e+13f, 4.52148695e+14f, 4.17688952e+15f, 4.45286776e+16f, 5.52914285e+17f, 8.07573252e+18f, },
      { 2.45471558e-02f, 7.37246687e-02f, 1.23152531e-01f, 1.73000138e-01f, 2.23440665e-01f, 2.74652655e-01f, 3.26821679e-01f, 3.80142101e-01f, 4.34818964e-01f, 4.91070037e-01f, 5.49128046e-01f, 6.09243132e-01f, 6.71685571e-01f, 7.36748805e-01f, 8.04752842e-01f, 8.76048080e-01f, 9.51019635e-01f, 1.03009224e+00f, 1.11373586e+00f, 1.20247203e+00f, 1.29688123e+00f, 1.39761124e+00f, 1.50538689e+00f, 1.62102121e+00f, 1.74542840e+00f, 1.87963895e+00f, 2.02481711e+00f, 2.18228138e+00f, 2.35352849e+00f, 2.54026147e+00f, 2.74442267e+00f, 2.96823279e+00f, 3.21423687e+00f, 3.48535896e+00f, 3.78496698e+00f, 4.11695014e+00f, 4.48581137e+00f, 4.89677825e+00f, 5.35593629e+00f, 5.87038976e+00f, 6.44845619e+00f, 7.09990245e+00f, 7.83623225e+00f, 8.67103729e+00f, 9.62042778e+00f, 1.07035620e+01f, 1.19433001e+01f, 1.33670142e+01f, 1.50075962e+01f, 1.69047155e+01f, 1.91063967e+01f, 2.16710044e+01f, 2.46697527e+01f, 2.81898903e+01f, 3.23387613e+01f, 3.72490076e+01f, 4.30852608e+01f, 5.00527965e+01f, 5.84087761e+01f, 6.84769282e+01f, 8.06668178e+01f, 9.54992727e+01f, 1.13640120e+02f, 1.35945194e+02f, 1.63520745e+02f, 1.97804969e+02f, 2.40678754e+02f, 2.94617029e+02f, 3.62896953e+02f, 4.49886178e+02f, 5.61444735e+02f, 7.05489247e+02f, 8.92790773e+02f, 1.13811142e+03f, 1.46183599e+03f, 1.89233262e+03f, 2.46939604e+03f, 3.24931157e+03f, 4.31236711e+03f, 5.77409475e+03f, 7.80224724e+03f, 1.06426753e+04f, 1.46591538e+04f, 2.03952854e+04f, 2.86717062e+04f, 4.07403376e+04f, 5.85318231e+04f, 8.50568927e+04f, 1.25064927e+05f, 1.86137394e+05f, 2.80525578e+05f, 4.28278249e+05f, 6.62634051e+05f, 1.03944324e+06f, 1.65385743e+06f, 2.67031565e+06f, 4.37721203e+06f, 7.28807171e+06f, 1.23317299e+07f, 2.12155729e+07f, 3.71308625e+07f, 6.61457938e+07f, 1.20005529e+08f, 2.21862941e+08f, 4.18228294e+08f, 8.04370413e+08f, 1.57939299e+09f, 3.16812242e+09f, 6.49660681e+09f, 1.36285199e+10f, 2.92686390e+10f, 6.43979867e+10f, 1.45275523e+11f, 3.36285446e+11f, 7.99420279e+11f, 1.95326423e+12f, 4.90958187e+12f, 1.27062273e+13f, 3.38907099e+13f, 9.32508403e+13f, 2.64948942e+14f, 7.78129518e+14f, 2.36471505e+15f, 7.44413803e+15f, 2.43021724e+16f, 8.23706864e+16f, 2.90211705e+17f, 1.06415768e+18f, 4.06627711e+18f, },
      { 1.22722792e-02f, 3.68272289e-02f, 6.14133763e-02f, 8.60515971e-02f, 1.10762884e-01f, 1.35568393e-01f, 1.60489494e-01f, 1.85547813e-01f, 2.10765290e-01f, 2.36164222e-01f, 2.61767321e-01f, 2.87597761e-01f, 3.13679240e-01f, 3.40036029e-01f, 3.66693040e-01f, 3.93675878e-01f, 4.21010910e-01f, 4.48725333e-01f, 4.76847237e-01f, 5.05405685e-01f, 5.34430786e-01f, 5.63953775e-01f, 5.94007101e-01f, 6.24624511e-01f, 6.55841151e-01f, 6.87693662e-01f, 7.20220285e-01f, 7.53460977e-01f, 7.87457528e-01f, 8.22253686e-01f, 8.57895297e-01f, 8.94430441e-01f, 9.31909591e-01f, 9.70385775e-01f, 1.00991475e+00f, 1.05055518e+00f, 1.09236885e+00f, 1.13542087e+00f, 1.17977990e+00f, 1.22551840e+00f, 1.27271289e+00f, 1.32144424e+00f, 1.37179794e+00f, 1.42386447e+00f, 1.47773961e+00f, 1.53352485e+00f, 1.59132774e+00f, 1.65126241e+00f, 1.71344993e+00f, 1.77801893e+00f, 1.84510605e+00f, 1.91485658e+00f, 1.98742510e+00f, 2.06297613e+00f, 2.14168493e+00f, 2.22373826e+00f, 2.30933526e+00f, 2.39868843e+00f, 2.49202464e+00f, 2.58958621e+00f, 2.69163219e+00f, 2.79843963e+00f, 2.91030501e+00f, 3.02754584e+00f, 3.15050230e+00f, 3.27953915e+00f, 3.41504770e+00f, 3.55744805e+00f, 3.70719145e+00f, 3.86476298e+00f, 4.03068439e+00f, 4.20551725e+00f, 4.38986641e+00f, 4.58438376e+00f, 4.78977239e+00f, 5.00679110e+00f, 5.23625945e+00f, 5.47906320e+00f, 5.73616037e+00f, 6.00858792e+00f, 6.29746901e+00f, 6.60402117e+00f, 6.92956515e+00f, 7.27553483e+00f, 7.64348809e+00f, 8.03511888e+00f, 8.45227058e+00f, 8.89695079e+00f, 9.37134780e+00f, 9.87784877e+00f, 1.04190601e+01f, 1.09978298e+01f, 1.16172728e+01f, 1.22807990e+01f, 1.29921443e+01f, 1.37554055e+01f, 1.45750793e+01f, 1.54561061e+01f, 1.64039187e+01f, 1.74244972e+01f, 1.85244301e+01f, 1.97109839e+01f, 2.09921804e+01f, 2.23768845e+01f, 2.38749023e+01f, 2.54970927e+01f, 2.72554930e+01f, 2.91634608e+01f, 3.12358351e+01f, 3.34891185e+01f, 3.59416839e+01f, 3.86140099e+01f, 4.15289481e+01f, 4.47120276e+01f, 4.81918020e+01f, 5.20002465e+01f, 5.61732106e+01f, 6.07509371e+01f, 6.57786566e+01f, 7.13072704e+01f, 7.73941341e+01f, 8.41039609e+01f, 9.15098607e+01f, 9.96945411e+01f, 1.08751694e+02f, 1.18787600e+02f, 1.29922990e+02f, 1.42295202e+02f, 1.56060691e+02f, 1.71397955e+02f, 1.88510933e+02f, 2.07632988e+02f, 2.29031559e+02f, 2.53013612e+02f, 2.79932028e+02f, 3.10193130e+02f, 3.44265522e+02f, 3.82690530e+02f, 4.26094527e+02f, 4.75203518e+02f, 5.30860437e+02f, 5.94045681e+02f, 6.65901543e+02f, 7.47761337e+02f, 8.41184173e+02f, 9.47996570e+02f, 1.07034233e+03f, 1.21074246e+03f, 1.37216724e+03f, 1.55812321e+03f, 1.77275819e+03f, 2.02098849e+03f, 2.30865326e+03f, 2.64270219e+03f, 3.03142418e+03f, 3.48472668e+03f, 4.01447750e+03f, 4.63492426e+03f, 5.36320995e+03f, 6.22000841e+03f, 7.23030933e+03f, 8.42439022e+03f, 9.83902287e+03f, 1.15189746e+04f, 1.35188810e+04f, 1.59055875e+04f, 1.87610857e+04f, 2.21862046e+04f, 2.63052621e+04f, 3.12719440e+04f, 3.72767546e+04f, 4.45564828e+04f, 5.34062659e+04f, 6.41950058e+04f, 7.73851264e+04f, 9.35579699e+04f, 1.13446538e+05f, 1.37977827e+05f, 1.68327749e+05f, 2.05992575e+05f, 2.52882202e+05f, 3.11442272e+05f, 3.84814591e+05f, 4.77048586e+05f, 5.93380932e+05f, 7.40606619e+05f, 9.27573047e+05f, 1.16584026e+06f, 1.47056632e+06f, 1.86169890e+06f, 2.36558487e+06f, 3.01715270e+06f, 3.86288257e+06f, 4.96486431e+06f, 6.40636283e+06f, 8.29948185e+06f, 1.07957589e+07f, 1.41008733e+07f, 1.84951472e+07f, 2.43622442e+07f, 3.22295113e+07f, 4.28249388e+07f, 5.71579339e+07f, 7.66343793e+07f, 1.03221273e+08f, 1.39683399e+08f, 1.89925150e+08f, 2.59486540e+08f, 3.56266474e+08f, 4.91582541e+08f, 6.81731647e+08f, 9.50299811e+08f, 1.33159830e+09f, 1.87580198e+09f, 2.65667391e+09f, 3.78324022e+09f, 5.41753185e+09f, 7.80169537e+09f, 1.12996537e+10f, 1.64614916e+10f, 2.41235400e+10f, 3.55648690e+10f, 5.27534501e+10f, 7.87357211e+10f, 1.18256902e+11f, 1.78754944e+11f, 2.71963306e+11f, 4.16512215e+11f, 6.42178186e+11f, 9.96872550e+11f, 1.55821233e+12f, 2.45280998e+12f, 3.88865623e+12f, 6.20986899e+12f, 9.98992422e+12f, 1.61915800e+13f, 2.64432452e+13f, 4.35201885e+13f, 7.21888469e+13f, 1.20699764e+14f, 2.03448372e+14f, 3.45755310e+14f, 5.92524851e+14f, 1.02405779e+15f, 1.78517405e+15f, 3.13930699e+15f, 5.56985627e+15f, 9.97176335e+15f, 1.80168749e+16f, 3.28570986e+16f, 6.04901854e+16f, 1.12437528e+17f, 2.11044513e+17f, 4.00073701e+17f, 7.66084936e+17f, 1.48201877e+18f, 2.89694543e+18f, 5.72279017e+18f, 1.14268996e+19f, },
   };
   m_weights = {
      { 7.86824160e+00f, 8.80516388e+02f, 5.39627832e+07f, 8.87651190e+19f, },
      { 2.39852428e+00f, 5.24459642e+01f, 6.45788782e+04f, 2.50998524e+12f, },
      { 1.74936958e+00f, 3.97965898e+00f, 1.84851460e+01f, 1.86488072e+02f, 5.97420570e+03f, 1.27041264e+06f, 6.16419301e+09f, 5.23085003e+15f, },
      { 1.61385906e+00f, 1.99776729e+00f, 3.02023198e+00f, 5.47764184e+00f, 1.17966092e+01f, 3.03550485e+01f, 9.58442179e+01f, 3.89387024e+02f, 2.17919325e+03f, 1.83920812e+04f, 2.63212061e+05f, 7.42729651e+06f, 5.01587565e+08f, 1.03961087e+11f, 9.10032891e+13f, 5.06865116e+17f, },
      { 1.58146596e+00f, 1.66914991e+00f, 1.85752319e+00f, 2.17566262e+00f, 2.67590138e+00f, 3.44773868e+00f, 4.64394654e+00f, 6.53020450e+00f, 9.58228502e+00f, 1.46836141e+01f, 2.35444955e+01f, 3.96352727e+01f, 7.03763521e+01f, 1.32588012e+02f, 2.66962565e+02f, 5.79374920e+02f, 1.36869193e+03f, 3.55943572e+03f, 1.03218668e+04f, 3.38662130e+04f, 1.27816626e+05f, 5.65408251e+05f, 2.99446204e+06f, 1.94497502e+07f, 1.59219301e+08f, 1.69428882e+09f, 2.42715618e+10f, 4.87031785e+11f, 1.43181966e+13f, 6.48947152e+14f, 4.80375775e+16f, 6.20009636e+18f, },
      { 1.57345777e+00f, 1.59489276e+00f, 1.63853652e+00f, 1.70598041e+00f, 1.79972439e+00f, 1.92332285e+00f, 2.08159737e+00f, 2.28093488e+00f, 2.52969785e+00f, 2.83878478e+00f, 3.22239575e+00f, 3.69908136e+00f, 4.29318827e+00f, 5.03686536e+00f, 5.97287114e+00f, 7.15853842e+00f, 8.67142780e+00f, 1.06174736e+01f, 1.31428500e+01f, 1.64514563e+01f, 2.08309945e+01f, 2.66923599e+01f, 3.46299351e+01f, 4.55151836e+01f, 6.06440809e+01f, 8.19729692e+01f, 1.12502047e+02f, 1.56909655e+02f, 2.22620435e+02f, 3.21638549e+02f, 4.73757451e+02f, 7.12299455e+02f, 1.09460965e+03f, 1.72169779e+03f, 2.77592491e+03f, 4.59523007e+03f, 7.82342759e+03f, 1.37235744e+04f, 2.48518896e+04f, 4.65553875e+04f, 9.04176678e+04f, 1.82484396e+05f, 3.83680026e+05f, 8.42627197e+05f, 1.93843257e+06f, 4.68511285e+06f, 1.19352867e+07f, 3.21564375e+07f, 9.19600893e+07f, 2.80222318e+08f, 9.13611083e+08f, 3.20091090e+09f, 1.21076526e+10f, 4.96902475e+10f, 2.22431575e+11f, 1.09212534e+12f, 5.91688298e+12f, 3.55974344e+13f, 2.39435365e+14f, 1.81355107e+15f, 1.55873671e+16f, 1.53271488e+17f, 1.73927478e+18f, 2.29884122e+19f, 3.57403070e+20f, },
      { 1.57146132e+00f, 1.57679017e+00f, 1.58749564e+00f, 1.60367396e+00f, 1.62547113e+00f, 1.65308501e+00f, 1.68676814e+00f, 1.72683132e+00f, 1.77364814e+00f, 1.82766042e+00f, 1.88938482e+00f, 1.95942057e+00f, 2.03845873e+00f, 2.12729290e+00f, 2.22683194e+00f, 2.33811466e+00f, 2.46232715e+00f, 2.60082286e+00f, 2.75514621e+00f, 2.92706011e+00f, 3.11857817e+00f, 3.33200254e+00f, 3.56996830e+00f, 3.83549565e+00f, 4.13205150e+00f, 4.46362211e+00f, 4.83479919e+00f, 5.25088196e+00f, 5.71799849e+00f, 6.24325042e+00f, 6.83488580e+00f, 7.50250620e+00f, 8.25731548e+00f, 9.11241941e+00f, 1.00831875e+01f, 1.11876913e+01f, 1.24472371e+01f, 1.38870139e+01f, 1.55368872e+01f, 1.74323700e+01f, 1.96158189e+01f, 2.21379089e+01f, 2.50594593e+01f, 2.84537038e+01f, 3.24091185e+01f, 3.70329629e+01f, 4.24557264e+01f, 4.88367348e+01f, 5.63712464e+01f, 6.52994709e+01f, 7.59180776e+01f, 8.85949425e+01f, 1.03788130e+02f, 1.22070426e+02f, 1.44161210e+02f, 1.70968019e+02f, 2.03641059e+02f, 2.43645006e+02f, 2.92854081e+02f, 3.53678602e+02f, 4.29234308e+02f, 5.23570184e+02f, 6.41976690e+02f, 7.91405208e+02f, 9.81042209e+02f, 1.22309999e+03f, 1.53391256e+03f, 1.93546401e+03f, 2.45753455e+03f, 3.14073373e+03f, 4.04081819e+03f, 5.23488160e+03f, 6.83029446e+03f, 8.97771323e+03f, 1.18901592e+04f, 1.58712239e+04f, 2.13571111e+04f, 2.89798371e+04f, 3.96630673e+04f, 5.47687519e+04f, 7.63235654e+04f, 1.07371915e+05f, 1.52531667e+05f, 2.18877843e+05f, 3.17362450e+05f, 4.65120153e+05f, 6.89253766e+05f, 1.03311989e+06f, 1.56688798e+06f, 2.40549203e+06f, 3.73952896e+06f, 5.88912115e+06f, 9.39904635e+06f, 1.52090328e+07f, 2.49628719e+07f, 4.15775926e+07f, 7.03070537e+07f, 1.20759856e+08f, 2.10788251e+08f, 3.74104720e+08f, 6.75449459e+08f, 1.24131674e+09f, 2.32331003e+09f, 4.43117602e+09f, 8.61744649e+09f, 1.70983691e+10f, 3.46357452e+10f, 7.16760712e+10f, 1.51634762e+11f, 3.28172932e+11f, 7.27110260e+11f, 1.65049955e+12f, 3.84133815e+12f, 9.17374427e+12f, 2.24990195e+13f, 5.67153509e+13f, 1.47074225e+14f, 3.92701252e+14f, 1.08063998e+15f, 3.06767147e+15f, 8.99238679e+15f, 2.72472254e+16f, 8.54294612e+16f, 2.77461372e+17f, 9.34529948e+17f, 3.26799612e+18f, 1.18791443e+19f, 4.49405341e+19f, 1.77170665e+20f, },
      { 1.57096255e+00f, 1.57229290e+00f, 1.57495658e+00f, 1.57895955e+00f, 1.58431079e+00f, 1.59102230e+00f, 1.59910918e+00f, 1.60858966e+00f, 1.61948515e+00f, 1.63182037e+00f, 1.64562338e+00f, 1.66092569e+00f, 1.67776241e+00f, 1.69617233e+00f, 1.71619809e+00f, 1.73788633e+00f, 1.76128784e+00f, 1.78645779e+00f, 1.81345587e+00f, 1.84234658e+00f, 1.87319943e+00f, 1.90608922e+00f, 1.94109632e+00f, 1.97830698e+00f, 2.01781368e+00f, 2.05971547e+00f, 2.10411838e+00f, 2.15113585e+00f, 2.20088916e+00f, 2.25350798e+00f, 2.30913084e+00f, 2.36790578e+00f, 2.42999091e+00f, 2.49555516e+00f, 2.56477893e+00f, 2.63785496e+00f, 2.71498915e+00f, 2.79640147e+00f, 2.88232702e+00f, 2.97301705e+00f, 3.06874019e+00f, 3.16978367e+00f, 3.27645477e+00f, 3.38908227e+00f, 3.50801806e+00f, 3.63363896e+00f, 3.76634859e+00f, 3.90657947e+00f, 4.05479525e+00f, 4.21149322e+00f, 4.37720695e+00f, 4.55250922e+00f, 4.73801517e+00f, 4.93438579e+00f, 5.14233166e+00f, 5.36261713e+00f, 5.59606472e+00f, 5.84356014e+00f, 6.10605759e+00f, 6.38458564e+00f, 6.68025373e+00f, 6.99425915e+00f, 7.32789480e+00f, 7.68255767e+00f, 8.05975815e+00f, 8.46113023e+00f, 8.88844279e+00f, 9.34361190e+00f, 9.82871448e+00f, 1.03460033e+01f, 1.08979234e+01f, 1.14871305e+01f, 1.21165112e+01f, 1.27892047e+01f, 1.35086281e+01f, 1.42785033e+01f, 1.51028871e+01f, 1.59862046e+01f, 1.69332867e+01f, 1.79494108e+01f, 1.90403465e+01f, 2.02124072e+01f, 2.14725057e+01f, 2.28282181e+01f, 2.42878539e+01f, 2.58605342e+01f, 2.75562800e+01f, 2.93861096e+01f, 3.13621485e+01f, 3.34977526e+01f, 3.58076454e+01f, 3.83080730e+01f, 4.10169773e+01f, 4.39541917e+01f, 4.71416602e+01f, 5.06036855e+01f, 5.43672075e+01f, 5.84621188e+01f, 6.29216205e+01f, 6.77826252e+01f, 7.30862125e+01f, 7.88781469e+01f, 8.52094636e+01f, 9.21371360e+01f, 9.97248336e+01f, 1.08043785e+02f, 1.17173764e+02f, 1.27204209e+02f, 1.38235512e+02f, 1.50380485e+02f, 1.63766039e+02f, 1.78535118e+02f, 1.94848913e+02f, 2.12889407e+02f, 2.32862309e+02f, 2.55000432e+02f, 2.79567594e+02f, 3.06863126e+02f, 3.37227087e+02f, 3.71046310e+02f, 4.08761417e+02f, 4.50874968e+02f, 4.97960949e+02f, 5.50675821e+02f, 6.09771424e+02f, 6.76110054e+02f, 7.50682104e+02f, 8.34626760e+02f, 9.29256285e+02f, 1.03608458e+03f, 1.15686082e+03f, 1.29360914e+03f, 1.44867552e+03f, 1.62478326e+03f, 1.82509876e+03f, 2.05330964e+03f, 2.31371761e+03f, 2.61134924e+03f, 2.95208799e+03f, 3.34283233e+03f, 3.79168493e+03f, 4.30817984e+03f, 4.90355562e+03f, 5.59108434e+03f, 6.38646863e+03f, 7.30832183e+03f, 8.37874981e+03f, 9.62405722e+03f, 1.10756067e+04f, 1.27708661e+04f, 1.47546879e+04f, 1.70808754e+04f, 1.98141031e+04f, 2.30322789e+04f, 2.68294532e+04f, 3.13194118e+04f, 3.66401221e+04f, 4.29592484e+04f, 5.04810088e+04f, 5.94547213e+04f, 7.01854788e+04f, 8.30475173e+04f, 9.85009981e+04f, 1.17113127e+05f, 1.39584798e+05f, 1.66784302e+05f, 1.99790063e+05f, 2.39944995e+05f, 2.88925794e+05f, 3.48831531e+05f, 4.22297220e+05f, 5.12639825e+05f, 6.24046488e+05f, 7.61817907e+05f, 9.32683930e+05f, 1.14521401e+06f, 1.41035265e+06f, 1.74212004e+06f, 2.15853172e+06f, 2.68280941e+06f, 3.34498056e+06f, 4.18399797e+06f, 5.25055801e+06f, 6.61086017e+06f, 8.35163942e+06f, 1.05869253e+07f, 1.34671524e+07f, 1.71914827e+07f, 2.20245345e+07f, 2.83191730e+07f, 3.65476782e+07f, 4.73445266e+07f, 6.15653406e+07f, 8.03684303e+07f, 1.05328028e+08f, 1.38592169e+08f, 1.83103699e+08f, 2.42910946e+08f, 3.23606239e+08f, 4.32947522e+08f, 5.81743297e+08f, 7.85117979e+08f, 1.06432920e+09f, 1.44938958e+09f, 1.98286647e+09f, 2.72541431e+09f, 3.76386796e+09f, 5.22313881e+09f, 7.28378581e+09f, 1.02080964e+10f, 1.43789932e+10f, 2.03583681e+10f, 2.89749983e+10f, 4.14577375e+10f, 5.96383768e+10f, 8.62622848e+10f, 1.25466705e+11f, 1.83521298e+11f, 2.69981221e+11f, 3.99492845e+11f, 5.94638056e+11f, 8.90440997e+11f, 1.34155194e+12f, 2.03376855e+12f, 3.10262796e+12f, 4.76359832e+12f, 7.36142036e+12f, 1.14512696e+13f, 1.79331419e+13f, 2.82758550e+13f, 4.48929705e+13f, 7.17780287e+13f, 1.15585510e+14f, 1.87483389e+14f, 3.06351036e+14f, 5.04340065e+14f, 8.36616340e+14f, 1.39855635e+15f, 2.35633575e+15f, 4.00176517e+15f, 6.85137513e+15f, 1.18269011e+16f, 2.05867353e+16f, 3.61396878e+16f, 6.39911218e+16f, 1.14301619e+17f, 2.05988138e+17f, 3.74584679e+17f, 6.87444303e+17f, 1.27340764e+18f, 2.38124192e+18f, 4.49583562e+18f, 8.57144202e+18f, 1.65044358e+19f, 3.21010035e+19f, 6.30778012e+19f, 1.25240403e+20f, 2.51300530e+20f, 5.09677626e+20f, },
   };
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   m_committed_refinements = static_cast<boost::math::detail::atomic_unsigned_integer_type>(m_weights.size() - 1);
#else
   m_committed_refinements = m_weights.size() - 1;
#endif
   m_t_max = 4.03936524f;
   if (m_max_refinements >= m_abscissas.size())
   {
      m_abscissas.resize(m_max_refinements + 1);
      m_weights.resize(m_max_refinements + 1);
   }
   else
   {
      m_max_refinements = m_abscissas.size() - 1;
   }
}

template<class Real, class Policy>
void sinh_sinh_detail<Real, Policy>::init(const std::integral_constant<int, 2>&)
{
   m_abscissas = {
      { 3.088287417976322866e+00, 1.489931846492091580e+02, 3.412289247883437102e+06, 2.069325766042617791e+18, 2.087002407609475560e+50, 2.019766160717908151e+137, },
      { 9.130487626376696748e-01, 1.415789294662811592e+01, 6.704215516223276482e+03, 9.641725327150499415e+10, 2.508950760085778485e+30, 1.447263535710337145e+83, },
      { 4.072976900657586902e-01, 1.682066707021148743e+00, 6.150897986386729515e+00, 4.003962351929400222e+01, 7.929200247931026321e+02, 1.029849713330979583e+05, 3.038623109252438574e+08, 1.565445474362494869e+14, 4.042465098430219104e+23, 1.321706827429658179e+39, 4.991231782099557998e+64, 7.352943850359875966e+106, },
      { 1.981352722514781726e-01, 6.401556735005260177e-01, 1.248928698253977663e+00, 2.266080840944321232e+00, 4.296462696702327381e+00, 9.130290387099955696e+00, 2.311107653864279933e+01, 7.427706034324012430e+01, 3.267209207115258917e+02, 2.159485694311818716e+03, 2.415015262896413060e+04, 5.318194002756929158e+05, 2.800586857217043323e+07, 4.524065079794338780e+09, 3.085612573980677122e+12, 1.338826733015807478e+16, 6.254617176562341381e+20, 6.182098535814164754e+26, 3.077293649788458067e+34, 2.348957289370104303e+44, 1.148543197899469758e+57, 2.255300070010069868e+73, 1.877919500569195394e+94, 1.367473887938624280e+121, },
      { 9.839678940067320339e-02, 3.006056176599550351e-01, 5.198579789949384900e-01, 7.703620832988877009e-01, 1.071311369641311830e+00, 1.450569758088998445e+00, 1.950778549520360334e+00, 2.640031773695551468e+00, 3.631372373667412273e+00, 5.119915330903350570e+00, 7.456660981404883289e+00, 1.130226126889972624e+01, 1.796410692472772550e+01, 3.017810704601898222e+01, 5.403875800312370567e+01, 1.041077314477469548e+02, 2.180295201202628077e+02, 5.021556986259101646e+02, 1.288621310998222420e+03, 3.739216870800548324e+03, 1.247507297020191232e+04, 4.876399753226692124e+04, 2.281456582219130122e+05, 1.308777960064843017e+06, 9.460846634209664077e+06, 8.888831203637279622e+07, 1.124168828974344134e+09, 1.991276729532144470e+10, 5.167434691060984650e+11, 2.067218814203990888e+13, 1.350615033184100406e+15, 1.538540662836508188e+17, 3.290747290540350661e+19, 1.437291381884498816e+22, 1.409832445530347286e+25, 3.459135480277971441e+28, 2.398720582340954092e+32, 5.398806604617292960e+36, 4.613340002580628610e+41, 1.787685909667902457e+47, 3.841984370124338536e+53, 5.752797955708583700e+60, 7.771812038427286551e+68, 1.269673044204081626e+78, 3.495676773765731568e+88, 2.362519474971692445e+100, 6.002143893273651123e+113, 9.290716303464155539e+128, 1.514442238033847090e+146, },
      { 4.911510035029024930e-02, 1.480131496743607333e-01, 2.489388137406836857e-01, 3.533254236926684378e-01, 4.627335566122353259e-01, 5.789120681640963067e-01, 7.038702533860627799e-01, 8.399658591446505688e-01, 9.900150664244376147e-01, 1.157432570143699131e+00, 1.346412759185361763e+00, 1.562167113901335551e+00, 1.811238852782323380e+00, 2.101924419006550301e+00, 2.444843885584197934e+00, 2.853720746632915024e+00, 3.346458910955350787e+00, 3.946645821057838387e+00, 4.685673101596678529e+00, 5.605762230908151175e+00, 6.764332336830574204e+00, 8.240383175379985221e+00, 1.014394356129857730e+01, 1.263024714338892472e+01, 1.592130395780345258e+01, 2.033921861921857185e+01, 2.635846445760633752e+01, 3.468926333224152409e+01, 4.641291467019728963e+01, 6.320550793890424203e+01, 8.771497261808906374e+01, 1.242096926240411498e+02, 1.797186347845127557e+02, 2.660817283327900190e+02, 4.037273029575712841e+02, 6.288113066545908703e+02, 1.007079837507490594e+03, 1.661568229185114288e+03, 2.829651440786582598e+03, 4.984386266585669139e+03, 9.101546927647810893e+03, 1.726892655475049727e+04, 3.413099578778601190e+04, 7.045668977053092802e+04, 1.523404217761279128e+05, 3.460479782897947414e+05, 8.284724209233183002e+05, 2.097596146601193946e+06, 5.636950798861273236e+06, 1.614071410855607245e+07, 4.944730678915060360e+07, 1.627810516820991356e+08, 5.785332971632280838e+08, 2.230838540681955690e+09, 9.382391306064739643e+09, 4.328149544776551692e+10, 2.203072744049242904e+11, 1.245245067109136413e+12, 7.869000534957822375e+12, 5.599531432979422461e+13, 4.521486949902090877e+14, 4.176889516548293265e+15, 4.452867759650496656e+16, 5.529142853140498068e+17, 8.075732516562854275e+18, 1.402046916260468698e+20, 2.925791412832239850e+21, 7.426433029335410886e+22, 2.321996331245735364e+24, 9.064194250638442432e+25, 4.481279048819445609e+27, 2.849046304726990645e+29, 2.367381159183355975e+31, 2.615825578455121227e+33, 3.914764948263290808e+35, 8.092042448555929219e+37, 2.358921320940630332e+40, 9.915218648535332591e+42, 6.152851059342658764e+45, 5.780276340144515388e+48, 8.443751734186488626e+51, 1.973343350899766708e+55, 7.605247378556219980e+58, 4.992057104939510418e+62, 5.775863423903912316e+66, 1.221808201945355603e+71, 4.912917230387133816e+75, 3.913971813732202372e+80, 6.456388069905286787e+85, 2.311225068528010358e+91, 1.887458157719431339e+97, 3.708483165438453094e+103, 1.855198812283538635e+110, 2.509787873171705318e+117, 9.790423755591216617e+124, 1.179088807944050747e+133, 4.714631846722476620e+141, 6.762657785959713240e+150, },
      { 2.454715583629863651e-02, 7.372466873903346224e-02, 1.231525309416766543e-01, 1.730001377719248556e-01, 2.234406649596860001e-01, 2.746526549718518258e-01, 3.268216792980646669e-01, 3.801421009804789245e-01, 4.348189637215614948e-01, 4.910700365099428407e-01, 5.491280459480215441e-01, 6.092431324382654397e-01, 6.716855712021148069e-01, 7.367488049067938643e-01, 8.047528416336950644e-01, 8.760480802482050705e-01, 9.510196351823332253e-01, 1.030092244532470067e+00, 1.113735859588680765e+00, 1.202472030918058876e+00, 1.296881226496863751e+00, 1.397611241828373026e+00, 1.505386891360545205e+00, 1.621021205894798030e+00, 1.745428403369044572e+00, 1.879638952031029331e+00, 2.024817107609328524e+00, 2.182281382147884181e+00, 2.353528494823881355e+00, 2.540261468229626457e+00, 2.744422672171478111e+00, 2.968232787190606619e+00, 3.214236869520657666e+00, 3.485358957907730467e+00, 3.784966983117372821e+00, 4.116950138940295100e+00, 4.485811369388231710e+00, 4.896778246562001812e+00, 5.355936290826725948e+00, 5.870389762600956907e+00, 6.448456189131117605e+00, 7.099902452679558236e+00, 7.836232253282841261e+00, 8.671037293575230635e+00, 9.620427777985990363e+00, 1.070356198876799531e+01, 1.194330008139441022e+01, 1.336701421038499647e+01, 1.500759615914396343e+01, 1.690471548203528376e+01, 1.910639668731689597e+01, 2.167100443216577994e+01, 2.466975274695099197e+01, 2.818989025157845355e+01, 3.233876132429401745e+01, 3.724900758097245740e+01, 4.308526084907741997e+01, 5.005279647654703975e+01, 5.840877607253876528e+01, 6.847692821534239862e+01, 8.066681777060714848e+01, 9.549927270200249260e+01, 1.136401195769487885e+02, 1.359451944976603209e+02, 1.635207451879744447e+02, 1.978049687912586950e+02, 2.406787535889776661e+02, 2.946170292930555023e+02, 3.628969532147125333e+02, 4.498861782715596902e+02, 5.614447353133496106e+02, 7.054892470899271429e+02, 8.927907732799964116e+02, 1.138111424979478376e+03, 1.461835991563605367e+03, 1.892332623444716186e+03, 2.469396036186133479e+03, 3.249311569298824731e+03, 4.312367113170283012e+03, 5.774094754500139661e+03, 7.802247237500851845e+03, 1.064267530975806972e+04, 1.465915383535674990e+04, 2.039528541239754835e+04, 2.867170622421556265e+04, 4.074033762183453297e+04, 5.853182310596923393e+04, 8.505689265265206640e+04, 1.250649269847856615e+05, 1.861373943166749766e+05, 2.805255777452010927e+05, 4.282782486084761748e+05, 6.626340506127657304e+05, 1.039443239650339565e+06, 1.653857426112961316e+06, 2.670315650125279161e+06, 4.377212026624358795e+06, 7.288071713698413821e+06, 1.233172993400331694e+07, 2.121557285769933699e+07, 3.713086254861535383e+07, 6.614579377352135534e+07, 1.200055291694917110e+08, 2.218629410296880690e+08, 4.182282939928687703e+08, 8.043704132493714804e+08, 1.579392989425668114e+09, 3.168122415524104635e+09, 6.496606811549861323e+09, 1.362851988356444486e+10, 2.926863897008707708e+10, 6.439798665209493735e+10, 1.452755233772903022e+11, 3.362854459389246576e+11, 7.994202785433479271e+11, 1.953264233362291960e+12, 4.909581868242554569e+12, 1.270622730765015610e+13, 3.389070986742985764e+13, 9.325084030208844833e+13, 2.649489423834534140e+14, 7.781295184094957195e+14, 2.364715052527355639e+15, 7.444138031465958255e+15, 2.430217240684749635e+16, 8.237068641534357762e+16, 2.902117050664548840e+17, 1.064157679404037013e+18, 4.066277106061960017e+18, 1.621274233630359097e+19, 6.754156830915450013e+19, 2.944056841733781919e+20, 1.344640139549107817e+21, 6.444586158944723300e+21, 3.246218667554608934e+22, 1.721234579556653533e+23, 9.622533890240474391e+23, 5.681407260417956671e+24, 3.548890779995928184e+25, 2.349506425672269562e+26, 1.651618130605205643e+27, 1.235147426493113059e+28, 9.845947239792057550e+28, 8.383130781984610418e+29, 7.639649461399172445e+30, 7.467862732233885201e+31, 7.847691482004993660e+32, 8.886032557626454704e+33, 1.086734890678302436e+35, 1.438967777036538458e+36, 2.068168865475603521e+37, 3.234885320223912385e+38, 5.521233641542628514e+39, 1.031148231194663855e+41, 2.113272035816365982e+42, 4.766724345485077520e+43, 1.186961550990218287e+45, 3.273172169205847573e+46, 1.002821226769167753e+48, 3.424933903935156479e+49, 1.308436017026428736e+51, 5.611378330048420503e+52, 2.711424806327139291e+54, 1.481771793644066442e+56, 9.194282071042778804e+57, 6.503661455875355562e+59, 5.266329986868627303e+61, 4.902662807969347359e+63, 5.270511057289557050e+65, 6.572856511670583316e+67, 9.553956030013225387e+69, 1.626491911159411616e+72, 3.259410915500951223e+74, 7.728460318113614280e+76, 2.179881996905918059e+79, 7.354484388371505915e+81, 2.984831270803957746e+84, 1.465828267813438962e+87, 8.763355972629864261e+89, 6.417909665847831130e+92, 5.794958649229893510e+95, 6.494224472311908365e+98, 9.095000156016433698e+101, 1.603058498455299102e+105, 3.582099119119320529e+108, 1.022441227139854687e+112, 3.756872185015086057e+115, 1.791363463832849159e+119, 1.117641882039472124e+123, 9.202159565546528285e+126, 1.008716474827888568e+131, 1.485546487089301805e+135, 2.966961534830566097e+139, 8.114207284664369360e+143, 3.069178087507669739e+148, 1.622223681147791473e+153, },
      { 1.227227917054637830e-02, 3.682722894492590471e-02, 6.141337626871079991e-02, 8.605159708778207907e-02, 1.107628840017845446e-01, 1.355683934957785482e-01, 1.604894937454335489e-01, 1.855478131645089496e-01, 2.107652898670700524e-01, 2.361642222214626268e-01, 2.617673206785495261e-01, 2.875977610631342900e-01, 3.136792395249035647e-01, 3.400360293536632770e-01, 3.666930398731810193e-01, 3.936758776386451797e-01, 4.210109101746846268e-01, 4.487253325041450341e-01, 4.768472367324829462e-01, 5.054056849688209375e-01, 5.344307858825229079e-01, 5.639537752137267134e-01, 5.940071005777549000e-01, 6.246245109268716053e-01, 6.558411510586397969e-01, 6.876936615883514922e-01, 7.202202848338683401e-01, 7.534609770949572224e-01, 7.874575278460963461e-01, 8.222536864020499377e-01, 8.578952966595825808e-01, 8.944304405668593009e-01, 9.319095910247435485e-01, 9.703857749817920659e-01, 1.009914747547728584e+00, 1.050555178019083150e+00, 1.092368848786092579e+00, 1.135420868172514300e+00, 1.179779898350424466e+00, 1.225518399571142610e+00, 1.272712892062026473e+00, 1.321444237057985065e+00, 1.371797938567245953e+00, 1.423864467614384096e+00, 1.477739610861208115e+00, 1.533524845679288858e+00, 1.591327743938355098e+00, 1.651262406984310076e+00, 1.713449934511288211e+00, 1.778018930286256858e+00, 1.845106047964720870e+00, 1.914856580544951899e+00, 1.987425097349017093e+00, 2.062976132795275283e+00, 2.141684931642916785e+00, 2.223738255848994521e+00, 2.309335258687213796e+00, 2.398688432341103821e+00, 2.492024635808356095e+00, 2.589586210645122756e+00, 2.691632192846832444e+00, 2.798439630014497291e+00, 2.910305013902562652e+00, 3.027545839497364963e+00, 3.150502302946919722e+00, 3.279539151967394330e+00, 3.415047703805410611e+00, 3.557448047456550733e+00, 3.707191448649779817e+00, 3.864762978128342125e+00, 4.030684386016531344e+00, 4.205517247588613835e+00, 4.389866408585172458e+00, 4.584383761391930748e+00, 4.789772386950687695e+00, 5.006791101261363264e+00, 5.236259449815274050e+00, 5.479063198337523150e+00, 5.736160373884817415e+00, 6.008587916728619858e+00, 6.297469010648863048e+00, 6.604021167380929133e+00, 6.929565150124677837e+00, 7.275534831383860972e+00, 7.643488092123492064e+00, 8.035118882502459288e+00, 8.452270579478188130e+00, 8.896950793641785313e+00, 9.371347797016395173e+00, 9.877848765573446033e+00, 1.041906005527762037e+01, 1.099782975900831706e+01, 1.161727282423952258e+01, 1.228079904848924611e+01, 1.299214431196691048e+01, 1.375540545535625881e+01, 1.457507926620621316e+01, 1.545610610104852468e+01, 1.640391874338302925e+01, 1.742449718154208970e+01, 1.852443008688437526e+01, 1.971098388378266494e+01, 2.099218043080961648e+01, 2.237688448013982946e+01, 2.387490225270073820e+01, 2.549709266380430464e+01, 2.725549296232531555e+01, 2.916346081119624987e+01, 3.123583514423284962e+01, 3.348911849136805118e+01, 3.594168387985465099e+01, 3.861400990307230737e+01, 4.152894811329303023e+01, 4.471202755441533396e+01, 4.819180202224910174e+01, 5.200024654361558757e+01, 5.617321062537384494e+01, 6.075093706918782079e+01, 6.577865661168003966e+01, 7.130727037357721343e+01, 7.739413413465805794e+01, 8.410396085269633392e+01, 9.150986068496734448e+01, 9.969454113547704016e+01, 1.087516939426018897e+02, 1.187876000643037532e+02, 1.299229897614516371e+02, 1.422952015056372537e+02, 1.560606914665002671e+02, 1.713979549326432406e+02, 1.885109325154830073e+02, 2.076329877740125935e+02, 2.290315594654587370e+02, 2.530136115655676467e+02, 2.799320282398896912e+02, 3.101931299766730890e+02, 3.442655222107529892e+02, 3.826905303289378387e+02, 4.260945266207607701e+02, 4.752035175892902045e+02, 5.308604366239058864e+02, 5.940456805372995009e+02, 6.659015428338778262e+02, 7.477613367309153870e+02, 8.411841730471343023e+02, 9.479965698013741524e+02, 1.070342331375881840e+03, 1.210742457518582660e+03, 1.372167241552205820e+03, 1.558123212187692722e+03, 1.772758188662716282e+03, 2.020988485411862984e+03, 2.308653259329163157e+03, 2.642702189813684273e+03, 3.031424182869210212e+03, 3.484726676985756018e+03, 4.014477504733973505e+03, 4.634924264049394751e+03, 5.363209949773439749e+03, 6.220008412114342803e+03, 7.230309332853029956e+03, 8.424390216735217783e+03, 9.839022871538541787e+03, 1.151897463083113988e+04, 1.351888098874374202e+04, 1.590558745460066947e+04, 1.876108572764816176e+04, 2.218620462393366275e+04, 2.630526205054915357e+04, 3.127194401941711057e+04, 3.727675461256652923e+04, 4.455648280312273249e+04, 5.340626592018903930e+04, 6.419500580388918123e+04, 7.738512642386820060e+04, 9.355796993981725963e+04, 1.134465375820669470e+05, 1.379778272209741713e+05, 1.683277485807887053e+05, 2.059925746120735305e+05, 2.528822024503158254e+05, 3.114422718347725915e+05, 3.848145913435570736e+05, 4.770485864966822643e+05, 5.933809324724740854e+05, 7.406066190351666115e+05, 9.275730471470643372e+05, 1.165840260940180415e+06, 1.470566322118246135e+06, 1.861698899014921971e+06, 2.365584870298354495e+06, 3.017152695505764877e+06, 3.862882573599929249e+06, 4.964864305589750358e+06, 6.406362829959736606e+06, 8.299481847261302115e+06, 1.079575892642401854e+07, 1.410087327474604091e+07, 1.849514724418250100e+07, 2.436224419670805500e+07, 3.222951131863941234e+07, 4.282493882385925337e+07, 5.715793394339267637e+07, 7.663437932745451635e+07, 1.032212725498489699e+08, 1.396833991976194842e+08, 1.899251497664892740e+08, 2.594865396467505851e+08, 3.562664742464501497e+08, 4.915825413172413471e+08, 6.817316470116958142e+08, 9.502998105202541438e+08, 1.331598295343277538e+09, 1.875801976010459831e+09, 2.656673907709731487e+09, 3.783240215616365909e+09, 5.417531848500136979e+09, 7.801695369892847510e+09, 1.129965368955098833e+10, 1.646149161390821924e+10, 2.412353995736687694e+10, 3.556486895431927094e+10, 5.275345014093760519e+10, 7.873572108325378177e+10, 1.182569020317863604e+11, 1.787549442508363461e+11, 2.719633064979986142e+11, 4.165122153119897946e+11, 6.421781858205134197e+11, 9.968725497576275918e+11, 1.558212327122960399e+12, 2.452809984907093786e+12, 3.888656232828140210e+12, 6.209868990509424909e+12, 9.989924216297983665e+12, 1.619158001378611351e+13, 2.644324518669926559e+13, 4.352018847904374786e+13, 7.218884688202741709e+13, 1.206997640727349538e+14, 2.034483722445207402e+14, 3.457553102874402920e+14, 5.925248511957505706e+14, 1.024057793713038672e+15, 1.785174045941642162e+15, 3.139306988668494696e+15, 5.569856270174890128e+15, 9.971763353834460328e+15, 1.801687491114883092e+16, 3.285709858322565542e+16, 6.049018540910759710e+16, 1.124375283211369572e+17, 2.110445125952435305e+17, 4.000737007891229992e+17, 7.660849361564329309e+17, 1.482018770996176700e+18, 2.896945433910857945e+18, 5.722790165693470493e+18, 1.142689960439921462e+19, 2.306616559984106723e+19, 4.707857184616093863e+19, 9.717346347495342813e+19, 2.028735605622585444e+20, 4.284840254171000581e+20, 9.157027329021623836e+20, 1.980457834766411777e+21, 4.335604886702252004e+21, 9.609258559714223995e+21, 2.156604630608586997e+22, 4.902045909695270289e+22, 1.128749227121328467e+23, 2.633414623049930879e+23, 6.226335684490998543e+23, 1.492205279014148921e+24, 3.625768249717590109e+24, 8.933899764961444882e+24, 2.232786981682262383e+25, 5.661295336293986732e+25, 1.456616710298133142e+26, 3.803959852868488245e+26, 1.008531585603036490e+27, 2.715247425129423358e+27, 7.425071766766651967e+27, 2.062860712173225003e+28, 5.824055458799413312e+28, 1.671388836696436644e+29, 4.876830632023956392e+29, 1.447170071146107156e+30, 4.368562208925583783e+30, 1.341873806249251338e+31, 4.195251632754338682e+31, 1.335360134828214136e+32, 4.328681350715136340e+32, 1.429401866150319186e+33, 4.809736146227180696e+33, 1.649624114567602575e+34, 5.768677492419801469e+34, 2.057442854162761350e+35, 7.486423509917811063e+35, 2.780052791791155051e+36, 1.053908347660081874e+37, 4.080046334235754223e+37, 1.613553311592805373e+38, 6.520836332997615098e+38, 2.693848186257510992e+39, 1.138002408430710800e+40, 4.917748008813924613e+40, 2.174691073191358676e+41, 9.844523745430526502e+41, 4.563707467590116732e+42, 2.167352073708379137e+43, 1.054860193887170754e+44, 5.263588225566847365e+44, 2.693772458797916623e+45, 1.414506760560163074e+46, 7.624126763512016620e+46, 4.219828148762794411e+47, 2.399387665831793264e+48, 1.402139947254117434e+49, 8.424706325525422943e+49, 5.206918479942619318e+50, 3.311787866477716151e+51, 2.168683295509859155e+52, 1.462786368779206713e+53, 1.016761784575838363e+54, 7.286460995145043184e+54, 5.386194237448865407e+55, 4.108917480528740640e+56, 3.236445625945552728e+57, 2.633440652417619669e+58, 2.214702339357939268e+59, 1.926058995948268392e+60, 1.733067740414174932e+61, 1.614307160124426969e+62, 1.557464328486352138e+63, 1.557226155197192031e+64, 1.614473962707995344e+65, 1.736617406327386105e+66, 1.939201243451190521e+67, 2.249277732936622876e+68, 2.711593798719765599e+69, 3.399628732048687119e+70, 4.435389696730206291e+71, 6.025566076164003981e+72, 8.529161425383779849e+73, 1.258746322992988688e+75, 1.938112175186560210e+76, 3.115432363572610661e+77, 5.231797674434390018e+78, 9.184930207860680757e+79, 1.686929404780378772e+81, 3.243565624474232635e+82, 6.533812498930220075e+83, 1.379898823144620314e+85, 3.057650444842839916e+86, 7.114050545839171245e+87, 1.739275024442258674e+89, 4.471782915853177804e+90, 1.210036789494028144e+92, 3.448828044590862359e+93, 1.036226783750561565e+95, 3.284801914751206038e+96, 1.099514933602224638e+98, 3.889581731378242597e+99, 1.455434287901069991e+101, 5.765729934387419019e+102, 2.420349568745475582e+104, 1.077606625929777536e+106, 5.093346988695851845e+107, 2.558090824110323997e+109, 1.366512508719047964e+111, 7.771735800763526406e+112, 4.710398638793014918e+114, 3.045563885587013954e+116, 2.102762552861442993e+118, 1.551937536212596136e+120, 1.225676354426075970e+122, 1.036950946169703711e+124, 9.407885268970827717e+125, 9.163369107785093171e+127, 9.592531095671168926e+129, 1.080486293361823875e+132, 1.311034829557782450e+134, 1.715642975932639188e+136, 2.424231742707881878e+138, 3.703231223333127919e+140, 6.123225027409988902e+142, 1.097271040771196765e+145, 2.133693643241295977e+147, 4.508099184895777328e+149, 1.036252806686291189e+152, },
   };
   m_weights = {
      { 7.868241604839621507e+00, 8.805163880733011116e+02, 5.396278323520705668e+07, 8.876511896968161317e+19, 2.432791879269225553e+52, 6.399713512080202911e+139, },
      { 2.398524276302635218e+00, 5.244596423726681022e+01, 6.457887819598201760e+04, 2.509985242511374506e+12, 1.774029269327138701e+32, 2.781406115983097314e+85, },
      { 1.749369583108386852e+00, 3.979658981934607813e+00, 1.848514598574449570e+01, 1.864880718932067988e+02, 5.974205695263265855e+03, 1.270412635144623341e+06, 6.164193014295984071e+09, 5.230850031811222530e+15, 2.226260929943369774e+25, 1.199931102042181592e+41, 7.470602144275146214e+66, 1.814465860528410676e+109, },
      { 1.613859062188366173e+00, 1.997767291869673262e+00, 3.020231979908834220e+00, 5.477641843859057761e+00, 1.179660916492671672e+01, 3.035504848518598294e+01, 9.584421793794920860e+01, 3.893870238229992076e+02, 2.179193250357911344e+03, 1.839208123964132852e+04, 2.632120612599856167e+05, 7.427296507169468210e+06, 5.015875648341232356e+08, 1.039610867241544113e+11, 9.100328911818091977e+13, 5.068651163890231571e+17, 3.039966520714902616e+22, 3.857740194672007962e+28, 2.465542763666581087e+36, 2.416439449167799461e+46, 1.517091553926604149e+59, 3.825043412021411380e+75, 4.089582396821598640e+96, 3.823775894295564050e+123, },
      { 1.581465959536694744e+00, 1.669149910438534746e+00, 1.857523188595005770e+00, 2.175662623626994120e+00, 2.675901375211020564e+00, 3.447738682498791744e+00, 4.643946540355464126e+00, 6.530204496574248616e+00, 9.582285015566804961e+00, 1.468361407515440960e+01, 2.354449548740987533e+01, 3.963527273305166705e+01, 7.037635206267538547e+01, 1.325880124784838868e+02, 2.669625649541569172e+02, 5.793749198508472676e+02, 1.368691928321303605e+03, 3.559435721533130554e+03, 1.032186677270763318e+04, 3.386621302858741487e+04, 1.278166259840246830e+05, 5.654082513926693098e+05, 2.994462044781721833e+06, 1.944975023421914947e+07, 1.592193007690560588e+08, 1.694288818617459913e+09, 2.427156182311303271e+10, 4.870317848199455490e+11, 1.431819656229181793e+13, 6.489471523099301256e+14, 4.803757752508989106e+16, 6.200096361305331541e+18, 1.502568562439914899e+21, 7.436061367189688251e+23, 8.264761218677928603e+26, 2.297735027897804345e+30, 1.805449779569534997e+34, 4.604472360199061931e+38, 4.458371212030626854e+43, 1.957638261114809309e+49, 4.767368137162500764e+55, 8.088820139476721285e+62, 1.238260897349286357e+71, 2.292272505278842062e+80, 7.151392373749193549e+90, 5.476714850156044431e+102, 1.576655618370700681e+116, 2.765448595957851958e+131, 5.108051255283132673e+148, },
      { 1.573457773573108386e+00, 1.594892755038663787e+00, 1.638536515530234742e+00, 1.705980408212213620e+00, 1.799724394608737275e+00, 1.923322854425656307e+00, 2.081597373313268178e+00, 2.280934883790070511e+00, 2.529697852387704655e+00, 2.838784782552951185e+00, 3.222395745020980612e+00, 3.699081358854235112e+00, 4.293188274330526800e+00, 5.036865356322330076e+00, 5.972871140910932199e+00, 7.158538424311077564e+00, 8.671427800892076385e+00, 1.061747360297922326e+01, 1.314285002260235600e+01, 1.645145625668428040e+01, 2.083099449998189069e+01, 2.669235989791640190e+01, 3.462993514791378189e+01, 4.551518362653662579e+01, 6.064408087764392116e+01, 8.197296917485846798e+01, 1.125020468081652564e+02, 1.569096552844714123e+02, 2.226204347868638276e+02, 3.216385489504077755e+02, 4.737574505945461739e+02, 7.122994548146997637e+02, 1.094609652686376553e+03, 1.721697789176049576e+03, 2.775924909253835146e+03, 4.595230066268149347e+03, 7.823427586641573672e+03, 1.372357435269105405e+04, 2.485188961645119553e+04, 4.655538745425972783e+04, 9.041766782135686884e+04, 1.824843964862728392e+05, 3.836800264094614027e+05, 8.426271970245168026e+05, 1.938432574158782634e+06, 4.685112849356485528e+06, 1.193528667218607927e+07, 3.215643752247989316e+07, 9.196008928386600386e+07, 2.802223178457559964e+08, 9.136110825267458886e+08, 3.200910900783148591e+09, 1.210765264234723689e+10, 4.969024745093101808e+10, 2.224315751863855216e+11, 1.092125344449313660e+12, 5.916882980019919359e+12, 3.559743438494577249e+13, 2.394353652945465191e+14, 1.813551073517501917e+15, 1.558736706166165738e+16, 1.532714875555114333e+17, 1.739274776190789212e+18, 2.298841216802216313e+19, 3.574030698837762664e+20, 6.604899705451419080e+21, 1.467155879591820659e+23, 3.964094964398509381e+24, 1.319342840595348793e+26, 5.482251971340400742e+27, 2.885137894723827518e+29, 1.952539840765392110e+31, 1.727051489032222797e+33, 2.031343507095439396e+35, 3.236074146972599980e+37, 7.120487412983497200e+39, 2.209552707411017265e+42, 9.886282647791384648e+44, 6.530514048788273529e+47, 6.530706672481546528e+50, 1.015518807431281951e+54, 2.526366773162394510e+57, 1.036450519906790297e+61, 7.241966032627135861e+64, 8.919402520769714938e+68, 2.008463619152992905e+73, 8.596914764830260020e+77, 7.290599546829495220e+82, 1.280199563216419112e+88, 4.878349285603201150e+93, 4.240828248064127940e+99, 8.869771764721598720e+105, 4.723342575741417669e+112, 6.802035963326188581e+119, 2.824531180990009549e+127, 3.621049216745982252e+135, 1.541270150334942520e+144, 2.353376995174362785e+153, },
      { 1.571461316550783294e+00, 1.576790166316938345e+00, 1.587495640370383316e+00, 1.603673956341370210e+00, 1.625471125457493943e+00, 1.653085011915939302e+00, 1.686768142525911236e+00, 1.726831323537516202e+00, 1.773648138667236602e+00, 1.827660421478661448e+00, 1.889384817044018196e+00, 1.959420572855037091e+00, 2.038458728047908923e+00, 2.127292904083847225e+00, 2.226831940199076941e+00, 2.338114664555130296e+00, 2.462327148722991304e+00, 2.600822860927085164e+00, 2.755146214814554359e+00, 2.927060108424483555e+00, 3.118578166240921951e+00, 3.332002540339506630e+00, 3.569968300410740276e+00, 3.835495653996447262e+00, 4.132051496512934885e+00, 4.463622106699067881e+00, 4.834799191008006557e+00, 5.250881957765679608e+00, 5.717998490875333124e+00, 6.243250421598568105e+00, 6.834885801226541839e+00, 7.502506202789340802e+00, 8.257315484493544201e+00, 9.112419405864642634e+00, 1.008318749543997758e+01, 1.118769134993865202e+01, 1.244723705914106881e+01, 1.388701390605507587e+01, 1.553688715915900190e+01, 1.743237000680942831e+01, 1.961581894823993424e+01, 2.213790886354273806e+01, 2.505945934677137610e+01, 2.845370377742137561e+01, 3.240911845969524834e+01, 3.703296289480230161e+01, 4.245572644746267911e+01, 4.883673480337985582e+01, 5.637124640586975420e+01, 6.529947092752610340e+01, 7.591807755694122837e+01, 8.859494252391663822e+01, 1.037881295005788124e+02, 1.220704263969226746e+02, 1.441612098131200535e+02, 1.709680191245773511e+02, 2.036410593843575570e+02, 2.436450058708723643e+02, 2.928540812182076105e+02, 3.536786019152253392e+02, 4.292343083967296939e+02, 5.235701840488733027e+02, 6.419766898003024575e+02, 7.914052083668759283e+02, 9.810422089081931637e+02, 1.223099994999740393e+03, 1.533912555427112127e+03, 1.935464013605830339e+03, 2.457534549912886852e+03, 3.140733731623635519e+03, 4.040818188564651898e+03, 5.234881599712225681e+03, 6.830294457607329226e+03, 8.977713228649887143e+03, 1.189015920967326839e+04, 1.587122387044346962e+04, 2.135711106445789331e+04, 2.897983705189681437e+04, 3.966306726795547950e+04, 5.476875193750000787e+04, 7.632356539388055680e+04, 1.073719149754976951e+05, 1.525316674555574152e+05, 2.188778434744216586e+05, 3.173624496019295608e+05, 4.651201525869328462e+05, 6.892537656280580572e+05, 1.033119885120019982e+06, 1.566887981043252499e+06, 2.405492027026531795e+06, 3.739528964815910340e+06, 5.889121154895580032e+06, 9.399046351922342030e+06, 1.520903276129653518e+07, 2.496287187293576168e+07, 4.157759259963074840e+07, 7.030705366950267312e+07, 1.207598558452493366e+08, 2.107882509464846833e+08, 3.741047199023457864e+08, 6.754494594987415572e+08, 1.241316740415880537e+09, 2.323310032649552862e+09, 4.431176019026625759e+09, 8.617446487400900130e+09, 1.709836906604031513e+10, 3.463574521880171339e+10, 7.167607123799270726e+10, 1.516347620910054079e+11, 3.281729323238950526e+11, 7.271102600298280790e+11, 1.650499552378780378e+12, 3.841338149508803917e+12, 9.173744267785176575e+12, 2.249901946357519979e+13, 5.671535089900611731e+13, 1.470742250307697019e+14, 3.927012518464311775e+14, 1.080639977391212820e+15, 3.067671466720475189e+15, 8.992386789198328428e+15, 2.724722536524592111e+16, 8.542946122263389258e+16, 2.774613718725574755e+17, 9.345299479382029121e+17, 3.267996122987731882e+18, 1.187914433455468315e+19, 4.494053408418564214e+19, 1.771706652195486743e+20, 7.288102552885931527e+20, 3.132512430816625349e+21, 1.408743767951073110e+22, 6.638294268236060414e+22, 3.282543608403565013e+23, 1.705920098038394064e+24, 9.332259385148524285e+24, 5.382727175874888312e+25, 3.278954235122093249e+26, 2.113191697957458099e+27, 1.443411041499643040e+28, 1.046864394654982423e+29, 8.077319226958905700e+29, 6.643146963432616277e+30, 5.835670121359986260e+31, 5.486890296790230798e+32, 5.533726968508261614e+33, 5.999734996418352834e+34, 7.009176119466122569e+35, 8.844061966424597499e+36, 1.208226860869605961e+38, 1.791648514311063338e+39, 2.891313916713205762e+40, 5.091457860211527298e+41, 9.810630588402496553e+42, 2.074441239147378860e+44, 4.827650116937700540e+45, 1.240287939111549029e+47, 3.528782858644784616e+48, 1.115449490471696659e+50, 3.930510643328196314e+51, 1.549243712957852337e+53, 6.854998238041301002e+54, 3.417479961583207704e+56, 1.926905498641079990e+58, 1.233580963004919450e+60, 9.002819902898076915e+61, 7.521415141253441645e+63, 7.224277554900578993e+65, 8.012832830535078610e+67, 1.030999620286380369e+70, 1.546174957076748679e+72, 2.715803772613248694e+74, 5.615089920571746438e+76, 1.373667859345343337e+79, 3.997541020769625126e+81, 1.391500589339800087e+84, 5.826693844912022892e+86, 2.952274820929549096e+89, 1.821023061478466282e+92, 1.375973022137941526e+95, 1.281852367543412945e+98, 1.482130127201990503e+101, 2.141574273792435314e+104, 3.894495540947112380e+107, 8.978646362580102961e+110, 2.644131589807244050e+114, 1.002403539841913834e+118, 4.931412804903905259e+121, 3.174401112435865044e+125, 2.696624001761892390e+129, 3.049799322320447166e+133, 4.634041526818687785e+137, 9.548983134803106512e+141, 2.694404866192089829e+146, 1.051502720036395325e+151, 5.734170640626244955e+155, },
      { 1.570962550997832611e+00, 1.572292902367211961e+00, 1.574956581912666755e+00, 1.578959553636163985e+00, 1.584310789563614305e+00, 1.591022301117035107e+00, 1.599109181186160337e+00, 1.608589657109067468e+00, 1.619485154826419743e+00, 1.631820374530739318e+00, 1.645623378191125679e+00, 1.660925689395424109e+00, 1.677762406016463717e+00, 1.696172326277082973e+00, 1.716198088860732467e+00, 1.737886327791014562e+00, 1.761287842885152410e+00, 1.786457786673686420e+00, 1.813455868772335587e+00, 1.842346578792652542e+00, 1.873199428986627521e+00, 1.906089217937612619e+00, 1.941096316736779451e+00, 1.978306979221816566e+00, 2.017813678003844337e+00, 2.059715468170813895e+00, 2.104118380732327493e+00, 2.151135848063375554e+00, 2.200889163814591418e+00, 2.253507979986114202e+00, 2.309130844113053375e+00, 2.367905779785113334e+00, 2.429990914023652954e+00, 2.495555155369085590e+00, 2.564778926893134514e+00, 2.637854958747451684e+00, 2.714989145296268067e+00, 2.796401472360280536e+00, 2.882327020626578700e+00, 2.973017051860293803e+00, 3.068740185193628238e+00, 3.169783671473487386e+00, 3.276454774427328601e+00, 3.389082268266156098e+00, 3.508018062292869136e+00, 3.633638964133530274e+00, 3.766348594369884204e+00, 3.906579466636309289e+00, 4.054795248667541120e+00, 4.211493221360917802e+00, 4.377206954666462219e+00, 4.552509221059946388e+00, 4.738015169510782826e+00, 4.934385785253587887e+00, 5.142331663338191074e+00, 5.362617126899976224e+00, 5.596064724397100194e+00, 5.843560143744373307e+00, 6.106057585381734693e+00, 6.384585640900671436e+00, 6.680253728973824449e+00, 6.994259146058412709e+00, 7.327894795748901060e+00, 7.682557667824588764e+00, 8.059758146071137270e+00, 8.461130232962342889e+00, 8.888442789395671080e+00, 9.343611899025485155e+00, 9.828714479494622022e+00, 1.034600327721380625e+01, 1.089792339849122916e+01, 1.148713054801325790e+01, 1.211651116619788555e+01, 1.278920468010096321e+01, 1.350862810871281096e+01, 1.427850329305334421e+01, 1.510288705493181327e+01, 1.598620462612703196e+01, 1.693328673269081128e+01, 1.794941076780000506e+01, 1.904034654190823159e+01, 2.021240716182964334e+01, 2.147250566192247370e+01, 2.282821809199713505e+01, 2.428785385941680425e+01, 2.586053422878117785e+01, 2.755628000354674426e+01, 2.938610955221109564e+01, 3.136214849990951329e+01, 3.349775258749912582e+01, 3.580764540799625468e+01, 3.830807296872530167e+01, 4.101697730155473447e+01, 4.395419165876113623e+01, 4.714166019494196927e+01, 5.060368545366659226e+01, 5.436720746019445252e+01, 5.846211877912138439e+01, 6.292162054058128784e+01, 6.778262518512416663e+01, 7.308621254265223015e+01, 7.887814686488147292e+01, 8.520946359734658334e+01, 9.213713603387774717e+01, 9.972483357670754649e+01, 1.080437851679046426e+02, 1.171737636088621692e+02, 1.272042089988687372e+02, 1.382355124664102373e+02, 1.503804848151483311e+02, 1.637660387526102742e+02, 1.785351181233383403e+02, 1.948489131607280604e+02, 2.128894073598352670e+02, 2.328623093447990790e+02, 2.550004322843281994e+02, 2.795675942672445782e+02, 3.068631259124280934e+02, 3.372270867451200874e+02, 3.710463099965576255e+02, 4.087614170466174911e+02, 4.508749684194593670e+02, 4.979609488959773491e+02, 5.506758209385785877e+02, 6.097714244663179092e+02, 6.761100535726473685e+02, 7.506821038741422446e+02, 8.346267600518081192e+02, 9.292562845315541998e+02, 1.036084578498234728e+03, 1.156860819661897657e+03, 1.293609142453808600e+03, 1.448675521854205144e+03, 1.624783259532197615e+03, 1.825098759915318560e+03, 2.053309635972617554e+03, 2.313717614494777200e+03, 2.611349236640186999e+03, 2.952087994093624299e+03, 3.342832332560548180e+03, 3.791684927756595099e+03, 4.308179838716318955e+03, 4.903555624570201673e+03, 5.591084343634811452e+03, 6.386468625571246341e+03, 7.308321829412979440e+03, 8.378749812799703561e+03, 9.624057218749638059e+03, 1.107560666191146008e+04, 1.277086605445904388e+04, 1.475468792019489452e+04, 1.708087537417066343e+04, 1.981410309695485051e+04, 2.303227888204754908e+04, 2.682945317928632535e+04, 3.131941178398428200e+04, 3.664012209706997997e+04, 4.295924836668690170e+04, 5.048100882639843572e+04, 5.945472133180055290e+04, 7.018547875172689579e+04, 8.304751726175694003e+04, 9.850099805053575446e+04, 1.171131266261766060e+05, 1.395847982160589845e+05, 1.667843016393077556e+05, 1.997900626520524686e+05, 2.399449946032992187e+05, 2.889257939838013232e+05, 3.488315309194304548e+05, 4.222972201496778447e+05, 5.126398246369253619e+05, 6.240464876221989792e+05, 7.618179073233615941e+05, 9.326839300224119257e+05, 1.145214007774297539e+06, 1.410352646274233119e+06, 1.742120041875863385e+06, 2.158531716934287014e+06, 2.682809410126426731e+06, 3.344980563595418861e+06, 4.183997972337706048e+06, 5.250558008165501752e+06, 6.610860174141680988e+06, 8.351639423967558693e+06, 1.058692532393929900e+07, 1.346715235106239409e+07, 1.719148271024263021e+07, 2.202453449027701694e+07, 2.831917301724337797e+07, 3.654767820268344932e+07, 4.734452657230626106e+07, 6.156534063509513873e+07, 8.036843026897869248e+07, 1.053280284359690289e+08, 1.385921689084126286e+08, 1.831036985925683524e+08, 2.429109457458640820e+08, 3.236062393759667463e+08, 4.329475218599986663e+08, 5.817432967962929479e+08, 7.851179789388191786e+08, 1.064329197627075307e+09, 1.449389582912945485e+09, 1.982866469377991849e+09, 2.725414314698094324e+09, 3.763867964111621444e+09, 5.223138814950990937e+09, 7.283785810644397704e+09, 1.020809642381158743e+10, 1.437899318470510521e+10, 2.035836812543633578e+10, 2.897499827080027444e+10, 4.145773751645494878e+10, 5.963837683872426287e+10, 8.626228483915530800e+10, 1.254667045389825180e+11, 1.835212982264913186e+11, 2.699812207400151604e+11, 3.994928452151922954e+11, 5.946380558701434550e+11, 8.904409967424091107e+11, 1.341551941677775838e+12, 2.033768550332151892e+12, 3.102627959875753214e+12, 4.763598321705862063e+12, 7.361420360560813584e+12, 1.145126961456557423e+13, 1.793314186996273926e+13, 2.827585501285792232e+13, 4.489297053678444669e+13, 7.177802872658499571e+13, 1.155855098545820625e+14, 1.874833886367883093e+14, 3.063510356402174454e+14, 5.043400653005970242e+14, 8.366163396892429890e+14, 1.398556351640947289e+15, 2.356335749516164682e+15, 4.001765167382637456e+15, 6.851375128404941445e+15, 1.182690111761543990e+16, 2.058673527013806443e+16, 3.613968784314904633e+16, 6.399112184394213551e+16, 1.143016185628376923e+17, 2.059881383915666443e+17, 3.745846788353680914e+17, 6.874443034683149068e+17, 1.273407643613485314e+18, 2.381241916829895366e+18, 4.495835617307108399e+18, 8.571442024901952701e+18, 1.650443584181656965e+19, 3.210100352421317851e+19, 6.307780124442703091e+19, 1.252404031157661279e+20, 2.513005295649985394e+20, 5.096776255690838436e+20, 1.045019200016673046e+21, 2.166476479260878466e+21, 4.542138145678395463e+21, 9.632082324449137128e+21, 2.066386536688254528e+22, 4.485529785554428251e+22, 9.853879573610977508e+22, 2.191158874464374408e+23, 4.932835964390971668e+23, 1.124501529971774363e+24, 2.596269136156756008e+24, 6.072292938313625501e+24, 1.438989066308003836e+25, 3.455841956406570469e+25, 8.412655191713576490e+25, 2.076289061650816510e+26, 5.196515024640220322e+26, 1.319173194089644043e+27, 3.397455895980380794e+27, 8.879057454438503591e+27, 2.355272361492064126e+28, 6.342762007722624824e+28, 1.734531093990859705e+29, 4.817893170606830871e+29, 1.359597346490148232e+30, 3.898969689906500392e+30, 1.136542986529989936e+31, 3.368450043991780017e+31, 1.015304084709817260e+32, 3.113144376221918237e+32, 9.713072739730140403e+32, 3.084517643581725946e+33, 9.972682139820497284e+33, 3.283625052288491586e+34, 1.101378785390827536e+35, 3.764333367592714297e+35, 1.311403465938242926e+36, 4.658135710682813672e+36, 1.687517347470511392e+37, 6.237053685018323490e+37, 2.352571314427744869e+38, 9.058938240219699936e+38, 3.562249097611136071e+39, 1.430959291578558210e+40, 5.873974584984375049e+40, 2.464828549811283787e+41, 1.057649203090855628e+42, 4.642475639281078035e+42, 2.085287118272421779e+43, 9.588439985186632177e+43, 4.514982011246092280e+44, 2.177974048341973204e+45, 1.076720976822900458e+46, 5.457267432929085589e+46, 2.836869270455781134e+47, 1.513103201392011626e+48, 8.283974667225617075e+48, 4.657239491995971344e+49, 2.689796370712836937e+50, 1.596597846911970388e+51, 9.744154538256586629e+51, 6.117238394843313065e+52, 3.952049650585241827e+53, 2.628701592074258213e+54, 1.800990196502679393e+55, 1.271554462563068383e+56, 9.255880104477760711e+56, 6.949737920133919393e+57, 5.385167200769965621e+58, 4.308493668102978774e+59, 3.560951557542178371e+60, 3.041888528384649992e+61, 2.687094441930837189e+62, 2.455920538900000855e+63, 2.323648254168641537e+64, 2.277129741584892331e+65, 2.312633552913224734e+66, 2.435407592981291129e+67, 2.660910388822465246e+68, 3.018105943423533920e+69, 3.555823489510192503e+70, 4.354188877793849013e+71, 5.544975795511813315e+72, 7.348276481909886336e+73, 1.013998025722423261e+75, 1.457911462244607943e+76, 2.185488876819505295e+77, 3.418022153286623008e+78, 5.580843920601835728e+79, 9.519586502799733908e+80, 1.697573578247197786e+82, 3.166906670990180014e+83, 6.185099106418675430e+84, 1.265541134386934377e+86, 2.714828965877756899e+87, 6.110386802964494082e+88, 1.444054086171083239e+90, 3.586083726638388165e+91, 9.365231868063239600e+92, 2.574080116205122449e+94, 7.452134689862302719e+95, 2.274309903836169819e+97, 7.323011134121164749e+98, 2.489816421737932462e+100, 8.946533386359281588e+101, 3.400401372391165979e+103, 1.368288186208928217e+105, 5.834277489829591931e+106, 2.638486937672383424e+108, 1.266728882767139521e+110, 6.462225178314182803e+111, 3.506432320607573604e+113, 2.025608933943268165e+115, 1.247041677084784707e+117, 8.189865188405279038e+118, 5.743610894406099965e+120, 4.305808934084489763e+122, 3.454156966079496755e+124, 2.968316601530352737e+126, 2.735456242372183592e+128, 2.706317176690077847e+130, 2.877679916342060385e+132, 3.292412878268106390e+134, 4.057840961953725969e+136, 5.393783049105737324e+138, 7.741523901672235406e+140, 1.201209962310668456e+143, 2.017456079556807301e+145, 3.672176623483062526e+147, 7.253163798058577630e+149, 1.556591535302570570e+152, 3.634399832790394885e+154, },
   };
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   m_committed_refinements = static_cast<boost::math::detail::atomic_unsigned_integer_type>(m_weights.size() - 1);
#else
   m_committed_refinements = m_weights.size() - 1;
#endif
   m_t_max = 6.114056619798597593;
   if (m_max_refinements >= m_abscissas.size())
   {
      m_abscissas.resize(m_max_refinements + 1);
      m_weights.resize(m_max_refinements + 1);
   }
   else
   {
      m_max_refinements = m_abscissas.size() - 1;
   }
}

#if LDBL_MAX_EXP == 16384
template<class Real, class Policy>
void sinh_sinh_detail<Real, Policy>::init(const std::integral_constant<int, 3>&)
{
   m_abscissas = {
      { 3.08828741797632286606397498241221385e+00L, 1.48993184649209158013612709175719825e+02L, 3.41228924788343710247727162226946917e+06L, 2.06932576604261779073902718911207249e+18L, 2.08700240760947556038306635808129743e+50L, 2.01976616071790815078008252209994199e+137L, 5.67213444764437168603513205349222232e+373L, 3.06198394306784061113736467298565948e+1016L, },
      { 9.13048762637669674838078869945948356e-01L, 1.41578929466281159169215570833490092e+01L, 6.70421551622327648231120321610008518e+03L, 9.64172532715049941526010563818587232e+10L, 2.50895076008577848514087028569888161e+30L, 1.44726353571033714499079379626715686e+83L, 3.75263401205045334128277886549019357e+226L, 2.57846123932685341261715758547064293e+616L, 1.25169402230987931584130068460134989e+1676L, },
      { 4.07297690065758690150573950473604675e-01L, 1.68206670702114874332932330092838356e+00L, 6.15089798638672951539668935798437533e+00L, 4.00396235192940022205839866641270319e+01L, 7.92920024793102632052902471550336177e+02L, 1.02984971333097958319686053784919407e+05L, 3.03862310925243857437932941891415841e+08L, 1.56544547436249486913719231527597560e+14L, 4.04246509843021910355659662715183671e+23L, 1.32170682742965817915034577861235933e+39L, 4.99123178209955799774620736137366103e+64L, 7.35294385035987596594501676609562008e+106L, 2.45145417229813114714431821340237112e+176L, 1.03030479197459267961759023741376327e+291L, 9.88478945193556730604342445576518136e+479L, 3.74498116076433060427765453233124618e+791L, 1.90292390713026708045766158039087796e+1305L, 1.72712488231809244721915842027236657e+2152L, },
      { 1.98135272251478172615235718398538323e-01L, 6.40155673500526017670785720911319502e-01L, 1.24892869825397766254802900819319987e+00L, 2.26608084094432123181038001270985037e+00L, 4.29646269670232738148137717958679987e+00L, 9.13029038709995569641811827045549232e+00L, 2.31110765386427993316995139635470937e+01L, 7.42770603432401243012214481410240677e+01L, 3.26720920711525891697790966796945063e+02L, 2.15948569431181871552028449867301828e+03L, 2.41501526289641306037808670196966696e+04L, 5.31819400275692915826269282626844010e+05L, 2.80058685721704332307817790755562305e+07L, 4.52406507979433877971977526863358905e+09L, 3.08561257398067712180174566763237112e+12L, 1.33882673301580747789133427708622972e+16L, 6.25461717656234138147247754416652254e+20L, 6.18209853581416475394863999233554548e+26L, 3.07729364978845806686511011060697837e+34L, 2.34895728937010430290698332595743680e+44L, 1.14854319789946975790127332653552673e+57L, 2.25530007001006986846545694404767295e+73L, 1.87791950056919539419801461538703336e+94L, 1.36747388793862427976781689638282239e+121L, 4.24212177246407592514720294750151286e+155L, 8.23473099012134325391170623028607158e+199L, 6.06134211888406631001019923425936351e+256L, 6.32519197436029413652766295658658263e+329L, 3.61942292928205643948383705201563065e+423L, 8.82464433126646489632943769283758364e+543L, 3.35512488018651738642854323317447493e+698L, 1.02411434678684822400261582079656500e+897L, 7.40716670979374017620610271197945784e+1151L, 1.30455147283895162835186634827124464e+1479L, 2.02948723345659016173510134582487641e+1899L, 6.99019443250345564805134094483457477e+2438L, },
      { 9.83967894006732033862249554537934739e-02L, 3.00605617659955035122465185012715805e-01L, 5.19857978994938490000644785900799951e-01L, 7.70362083298887700858583505753306721e-01L, 1.07131136964131183026831222500748063e+00L, 1.45056975808899844502126797109703210e+00L, 1.95077854952036033400296546339240312e+00L, 2.64003177369555146770080103921424631e+00L, 3.63137237366741227331951933364601985e+00L, 5.11991533090335057003526222543242753e+00L, 7.45666098140488328926087222725984231e+00L, 1.13022612688997262443461342203136009e+01L, 1.79641069247277254966592982669611020e+01L, 3.01781070460189822236726995418068214e+01L, 5.40387580031237056709826662540066860e+01L, 1.04107731447746954767459980678311109e+02L, 2.18029520120262807725988909352359830e+02L, 5.02155698625910164594509258651311707e+02L, 1.28862131099822242045586488612672886e+03L, 3.73921687080054832376623401153054636e+03L, 1.24750729702019123239464970064060864e+04L, 4.87639975322669212414357357061002683e+04L, 2.28145658221913012154676335814417144e+05L, 1.30877796006484301664040981436471740e+06L, 9.46084663420966407698078336053299177e+06L, 8.88883120363727962245667776649286152e+07L, 1.12416882897434413447175648471746990e+09L, 1.99127672953214446985509145592687205e+10L, 5.16743469106098464964433058111638245e+11L, 2.06721881420399088776240044545119948e+13L, 1.35061503318410040604770421574064883e+15L, 1.53854066283650818848773859278941683e+17L, 3.29074729054035066052820102585085173e+19L, 1.43729138188449881561963100200796631e+22L, 1.40983244553034728562740932242727657e+25L, 3.45913548027797144127990544649862792e+28L, 2.39872058234095409213724024023509754e+32L, 5.39880660461729295997086364918754801e+36L, 4.61334000258062860955942270137550165e+41L, 1.78768590966790245678294238939560892e+47L, 3.84198437012433853552557148945213178e+53L, 5.75279795570858370009870455748773410e+60L, 7.77181203842728655149182334288749850e+68L, 1.26967304420408162635234502672130357e+78L, 3.49567677376573156773252547632961336e+88L, 2.36251947497169244454694635287097330e+100L, 6.00214389327365112307366927985587840e+113L, 9.29071630346415553922675334522602561e+128L, 1.51444223803384709004933819608345315e+146L, 4.83290204410990250226310317342789471e+165L, 6.09623012864338569215560315517200736e+187L, 6.73889214277659359787962707085479315e+212L, 1.60917893672694484647176104023542732e+241L, 2.30724123468764987944582372785293125e+273L, 6.32636439816284705814531208407868829e+309L, 1.23274815890184688792156489065830724e+351L, 7.55519778823055185901318833981345378e+397L, 7.85730655106759111844772008129590573e+450L, 9.36328400229780321059086898529742817e+510L, 1.11332545426270395766246494603539938e+579L, 1.53432069979625061288117473415446630e+656L, 3.94622553764411915381186808199466267e+743L, 4.41554290953392996069257034424439949e+842L, 7.62037777854651720247173134262034607e+954L, 1.15650294539918196985498353779647134e+1082L, 1.50761161209705395018419027031866347e+1226L, 3.03485088181756935935831645680716816e+1389L, 3.38561189018537177501905611143498298e+1574L, 1.64407968993192526511230001818530622e+1784L, 6.63148743223242467667800355907041029e+2021L, 1.15911606715877477373516464924821476e+2291L, },
      { 4.91151003502902493004859475143401791e-02L, 1.48013149674360733270682895306542340e-01L, 2.48938813740683685679302156785267041e-01L, 3.53325423692668437764413545980527251e-01L, 4.62733556612235325924240102411897269e-01L, 5.78912068164096306746325958452873475e-01L, 7.03870253386062779930091622585657886e-01L, 8.39965859144650568772715366992985538e-01L, 9.90015066424437614655918533324528544e-01L, 1.15743257014369913143250540144278056e+00L, 1.34641275918536176274682426896283388e+00L, 1.56216711390133555099139972670978661e+00L, 1.81123885278232337973709010374295886e+00L, 2.10192441900655030086142289233010995e+00L, 2.44484388558419793403331221069953203e+00L, 2.85372074663291502355627105440782037e+00L, 3.34645891095535078662878051589922609e+00L, 3.94664582105783838715586741669722603e+00L, 4.68567310159667852940801410152900804e+00L, 5.60576223090815117543658276010994803e+00L, 6.76433233683057420359243855855175747e+00L, 8.24038317537998522052755780107530197e+00L, 1.01439435612985772971719760903679427e+01L, 1.26302471433889247197016543768391586e+01L, 1.59213039578034525767683420917805218e+01L, 2.03392186192185718515730871595417962e+01L, 2.63584644576063375239063530328964372e+01L, 3.46892633322415240903243772580777748e+01L, 4.64129146701972896279022543245987086e+01L, 6.32055079389042420324823016892494512e+01L, 8.77149726180890637368701810147417239e+01L, 1.24209692624041149789460094889690346e+02L, 1.79718634784512755704082576283626343e+02L, 2.66081728332790018970422286819998479e+02L, 4.03727302957571284052623415274320934e+02L, 6.28811306654590870314912279241546792e+02L, 1.00707983750749059403228978538051896e+03L, 1.66156822918511428805794334715835287e+03L, 2.82965144078658259775800007408843245e+03L, 4.98438626658566913853681230634186899e+03L, 9.10154692764781089316711776560031531e+03L, 1.72689265547504972749956316341831556e+04L, 3.41309957877860119013952370617496464e+04L, 7.04566897705309280220071327032864763e+04L, 1.52340421776127912819322591860346971e+05L, 3.46047978289794741370038030428999755e+05L, 8.28472420923318300234685980449600520e+05L, 2.09759614660119394556971994518988102e+06L, 5.63695079886127323617800640982012165e+06L, 1.61407141085560724511058572293479599e+07L, 4.94473067891506035994359725043521250e+07L, 1.62781051682099135552732962095205423e+08L, 5.78533297163228083837980097936281368e+08L, 2.23083854068195568971189557384768608e+09L, 9.38239130606473964277252886929412279e+09L, 4.32814954477655169232818147143370442e+10L, 2.20307274404924290439096300978395664e+11L, 1.24524506710913641251205336694034299e+12L, 7.86900053495782237478847410099854059e+12L, 5.59953143297942246131549106648666176e+13L, 4.52148694990209087674492643778610401e+14L, 4.17688951654829326508268412199901397e+15L, 4.45286775965049665585698503105745526e+16L, 5.52914285314049806809138826588984682e+17L, 8.07573251656285427526228838811700627e+18L, 1.40204691626046869792238856080062664e+20L, 2.92579141283223985009947325351094360e+21L, 7.42643302933541088612408356755400686e+22L, 2.32199633124573536432554419147246735e+24L, 9.06419425063844243203436385576505269e+25L, 4.48127904881944560890552322697008678e+27L, 2.84904630472699064463968599282594753e+29L, 2.36738115918335597454764956918757980e+31L, 2.61582557845512122680388807973635504e+33L, 3.91476494826329080795479762913295296e+35L, 8.09204244855592921872201325412806040e+37L, 2.35892132094063033238721339006551614e+40L, 9.91521864853533259120934844876236731e+42L, 6.15285105934265876352030730244956160e+45L, 5.78027634014451538840307809528507880e+48L, 8.44375173418648862568787172019022001e+51L, 1.97334335089976670761175825761931164e+55L, 7.60524737855621997973474323106432459e+58L, 4.99205710493951041807363394610235299e+62L, 5.77586342390391231642398949899839793e+66L, 1.22180820194535560304052518248291191e+71L, 4.91291723038713381630505551954597084e+75L, 3.91397181373220237220120723831917341e+80L, 6.45638806990528678730571952330861768e+85L, 2.31122506852801035759561549878351498e+91L, 1.88745815771943133919251977215527414e+97L, 3.70848316543845309405007424631179900e+103L, 1.85519881228353863491690263658048910e+110L, 2.50978787317170531780713813087390423e+117L, 9.79042375559121661656574862110333881e+124L, 1.17908880794405074709159757136517906e+133L, 4.71463184672247661988055185868884361e+141L, 6.76265778595971324046101731037889126e+150L, 3.77863792934416463055998231270977695e+160L, 8.97819133628477274891556066769979914e+170L, 9.95914407403494739885505580434362313e+181L, 5.69630798679205432723450802096176313e+193L, 1.86743452325707784871951519925288377e+206L, 3.92719945859412568046939678789391943e+219L, 5.97262802266570558434581771213406728e+233L, 7.46293577265068378653739984957785219e+248L, 8.77620026161886682323882402399676732e+264L, 1.12240955327637524990782988952974327e+282L, 1.82091452946462555573948654780251641e+300L, 4.41446070956615785669448222555093749e+319L, 1.90397541953366074155779318767572880e+340L, 1.75902281129585020130874703618203168e+362L, 4.24169226543235148528312412985493520e+385L, 3.29481560149200680874049429678412127e+410L, 1.03135148368973063748422508803572335e+437L, 1.65119340908368036904973428016231291e+465L, 1.74266589416784110509614783944413581e+495L, 1.58844581452329281145953920728196433e+527L, 1.66707913260906132496128590135534752e+561L, 2.73591938942385354685476950777714705e+597L, 9.72579530056263762611950442873242859e+635L, 1.05939976357545005153997575210948938e+677L, 5.11518133258374954640812727174811520e+720L, 1.62189977683387633564608415164137138e+767L, 5.13160378352024380054652132361473058e+816L, 2.52913238575275285564889845966314333e+869L, 3.11944472707029467274111976509771118e+925L, 1.59495166899855372832309254674677663e+985L, 5.78489108811836645985010918537259212e+1048L, 2.63682062563962607583302320466728244e+1116L, 2.77639914729635446526568065100606294e+1188L, 1.29100591117079370649785145859270604e+1265L, 5.28444381883484729377275155781628201e+1346L, 3.96822241951490935324304074734991606e+1433L, 1.19450143809568524145027520220707370e+1526L, 3.31232848440880307300003548888720354e+1624L, 2.05165402811808892980123939590704909e+1729L, 7.28745294435475474950235921073537827e+1840L, 4.04980582052614743975213722266303496e+1959L, 1.02489069982330185861864247663932840e+2086L, 3.68323339665104313610706754895420191e+2220L, 6.30765607309438726154024457882187086e+2363L, },
      { 2.45471558362986365068916495863375252e-02L, 7.37246687390334622422734853496288652e-02L, 1.23152530941676654318795303724845312e-01L, 1.73000137771924855589325108501317324e-01L, 2.23440664959686000105440799041805360e-01L, 2.74652654971851825832501290298110601e-01L, 3.26821679298064666878532098621581949e-01L, 3.80142100980478924532143540106066542e-01L, 4.34818963721561494844111167726460593e-01L, 4.91070036509942840677161193401564426e-01L, 5.49128045948021544092182270959032951e-01L, 6.09243132438265439671151238479162657e-01L, 6.71685571202114806905814141850507988e-01L, 7.36748804906793864301473114817726587e-01L, 8.04752841633695064447122683633828025e-01L, 8.76048080248205070538153903222362252e-01L, 9.51019635182333225305914695670696308e-01L, 1.03009224453247006650365629888427541e+00L, 1.11373585958868076495962122155474153e+00L, 1.20247203091805887562283157205483916e+00L, 1.29688122649686375081031224280895230e+00L, 1.39761124182837302592381732283067440e+00L, 1.50538689136054520464181654424860094e+00L, 1.62102120589479802996006107398491112e+00L, 1.74542840336904457155639617870301735e+00L, 1.87963895203102933109827214352261973e+00L, 2.02481710760932852386806039721112499e+00L, 2.18228138214788418140109912591097815e+00L, 2.35352849482388135462737441996874099e+00L, 2.54026146822962645747200253699005204e+00L, 2.74442267217147811137997080576917741e+00L, 2.96823278719060661900608946565759698e+00L, 3.21423686952065766588900110700898117e+00L, 3.48535895790773046725176601804623352e+00L, 3.78496698311737282096444166343378976e+00L, 4.11695013894029509955211911245969952e+00L, 4.48581136938823171042685637747918431e+00L, 4.89677824656200181220988391922608405e+00L, 5.35593629082672594809503436864536474e+00L, 5.87038976260095690701450882557564694e+00L, 6.44845618913111760490837120481487939e+00L, 7.09990245267955823638498704195342171e+00L, 7.83623225328284126133289428371735767e+00L, 8.67103729357523063452024155765264339e+00L, 9.62042777798599036292354986394365067e+00L, 1.07035619887679953097266587864897721e+01L, 1.19433000813944102150187593166292839e+01L, 1.33670142103849964717469335941905385e+01L, 1.50075961591439634296262105146352627e+01L, 1.69047154820352837629069726738079470e+01L, 1.91063966873168959742774555727687289e+01L, 2.16710044321657799400571191555580338e+01L, 2.46697527469509919694460528848277171e+01L, 2.81898902515784535507915179415086299e+01L, 3.23387613242940174515264777910328241e+01L, 3.72490075809724574016769417176033924e+01L, 4.30852608490774199699508301986096898e+01L, 5.00527964765470397507453905247128474e+01L, 5.84087760725387652778097774354827947e+01L, 6.84769282153423986221602875181609661e+01L, 8.06668177706071484780398912645020900e+01L, 9.54992727020024926023862465179431320e+01L, 1.13640119576948788485338465195796676e+02L, 1.35945194497660320895127779474436433e+02L, 1.63520745187974444650772294136165162e+02L, 1.97804968791258694996900535479466855e+02L, 2.40678753588977666070114453459006852e+02L, 2.94617029293055502283701272741864024e+02L, 3.62896953214712533295812475420140158e+02L, 4.49886178271559690246754005443952599e+02L, 5.61444735313349610605402755114132063e+02L, 7.05489247089927142932822047183046835e+02L, 8.92790773279996411641203534672635638e+02L, 1.13811142497947837649816383892464930e+03L, 1.46183599156360536709532001371341489e+03L, 1.89233262344471618571971876282268943e+03L, 2.46939603618613347923158360306283371e+03L, 3.24931156929882473061795067124714454e+03L, 4.31236711317028301201344127304044700e+03L, 5.77409475450013966113987691727433327e+03L, 7.80224723750085184509285318864950930e+03L, 1.06426753097580697178856711472253120e+04L, 1.46591538353567498972551254799117195e+04L, 2.03952854123975483493635354197048606e+04L, 2.86717062242155626463726121489695136e+04L, 4.07403376218345329677215262695648133e+04L, 5.85318231059692339303875522971681880e+04L, 8.50568926526520663990658694675860445e+04L, 1.25064926984785661460287048343378461e+05L, 1.86137394316674976633487938076388915e+05L, 2.80525577745201092730917964861422661e+05L, 4.28278248608476174804382253986989659e+05L, 6.62634050612765730354133581004327179e+05L, 1.03944323965033956450234998906270616e+06L, 1.65385742611296131560210844780522467e+06L, 2.67031565012527916099202454680251592e+06L, 4.37721202662435879490158484533403820e+06L, 7.28807171369841382127860775464397414e+06L, 1.23317299340033169377433189565436518e+07L, 2.12155728576993369873860452866846505e+07L, 3.71308625486153538260132253550700609e+07L, 6.61457937735213553402161639469475625e+07L, 1.20005529169491711045823979192639236e+08L, 2.21862941029688069011551388945586459e+08L, 4.18228293992868770261761230703186170e+08L, 8.04370413249371480388874614907492609e+08L, 1.57939298942566811374336496311019268e+09L, 3.16812241552410463468859624242708347e+09L, 6.49660681154986132317647460889788282e+09L, 1.36285198835644448552647856869429224e+10L, 2.92686389700870770766115985553270450e+10L, 6.43979866520949373493178830943697748e+10L, 1.45275523377290302218030864454825923e+11L, 3.36285445938924657613055409970261746e+11L, 7.99420278543347927144872935893265787e+11L, 1.95326423336229196043471517599153542e+12L, 4.90958186824255456862636657247754379e+12L, 1.27062273076501561032764305894113023e+13L, 3.38907098674298576400746683674724217e+13L, 9.32508403020884483292253012776198040e+13L, 2.64948942383453414004877623922649545e+14L, 7.78129518409495719454885394657867226e+14L, 2.36471505252735563941195059995826485e+15L, 7.44413803146595825477962585154187758e+15L, 2.43021724068474963516185214140527324e+16L, 8.23706864153435776160816132466078110e+16L, 2.90211705066454883998576313308511774e+17L, 1.06415767940403701307268606275625099e+18L, 4.06627710606196001743762877107146413e+18L, 1.62127423363035909653152786372227505e+19L, 6.75415683091545001302004267324917730e+19L, 2.94405684173378191865875139697985419e+20L, 1.34464013954910781728162580030274237e+21L, 6.44458615894472329986968048792306357e+21L, 3.24621866755460893428420802873638947e+22L, 1.72123457955665353317614924039447693e+23L, 9.62253389024047439092145016046085843e+23L, 5.68140726041795667144549981860838281e+24L, 3.54889077999592818369666361009585253e+25L, 2.34950642567226956175428754094351900e+26L, 1.65161813060520564300895544060422208e+27L, 1.23514742649311305869239755896991097e+28L, 9.84594723979205755046350596451684287e+28L, 8.38313078198461041780434493882428621e+29L, 7.63964946139917244491542497186650823e+30L, 7.46786273223388520123213229503835823e+31L, 7.84769148200499366026301301860529069e+32L, 8.88603255762645470434690507794367917e+33L, 1.08673489067830243582954987160820285e+35L, 1.43896777703653845844251806933839180e+36L, 2.06816886547560352123602144529722761e+37L, 3.23488532022391238528760911223890778e+38L, 5.52123364154262851432456431974095092e+39L, 1.03114823119466385539623421556538031e+41L, 2.11327203581636598172202380321061095e+42L, 4.76672434548507751952154119062281282e+43L, 1.18696155099021828703717401094811909e+45L, 3.27317216920584757332668902845797411e+46L, 1.00282122676916775281745610945056481e+48L, 3.42493390393515647893744567642140320e+49L, 1.30843601702642873582064430681159468e+51L, 5.61137833004842050312648039721074217e+52L, 2.71142480632713929135631433480050921e+54L, 1.48177179364406644198189668782951739e+56L, 9.19428207104277880378138495715383545e+57L, 6.50366145587535556181715778862405651e+59L, 5.26632998686862730262822409628217101e+61L, 4.90266280796934735938520451826335739e+63L, 5.27051105728955705039001489033126495e+65L, 6.57285651167058331634360929791632134e+67L, 9.55395603001322538662375429270666445e+69L, 1.62649191115941161585027771743899152e+72L, 3.25941091550095122341298778408441950e+74L, 7.72846031811361427986096662411063427e+76L, 2.17988199690591805869859473392710071e+79L, 7.35448438837150591476717971591286847e+81L, 2.98483127080395774595532033544389202e+84L, 1.46582826781343896171132281512590065e+87L, 8.76335597262986426104836504228132778e+89L, 6.41790966584783113047113716478179340e+92L, 5.79495864922989350993450871503126827e+95L, 6.49422447231190836548908540847688948e+98L, 9.09500015601643369774077853623649258e+101L, 1.60305849845529910220272077472033035e+105L, 3.58209911911932052869313856359636456e+108L, 1.02244122713985468653173668554645414e+112L, 3.75687218501508605716480350749498176e+115L, 1.79136346383284915921138840751400855e+119L, 1.11764188203947212384081419514165395e+123L, 9.20215956554652828530686799399533824e+126L, 1.00871647482788856814534779759128531e+131L, 1.48554648708930180487801088228218062e+135L, 2.96696153483056609742176098165601109e+139L, 8.11420728466436936033056414100674073e+143L, 3.06917808750766973862958599324080278e+148L, 1.62222368114779147260405024572949430e+153L, 1.21094608845400598319807865894230970e+158L, 1.29069460371207764384235331328282226e+163L, 1.98663030134211110165437011771903132e+168L, 4.46757261704134429970068624260491498e+173L, 1.48564187771108280640829188970287255e+179L, 7.39669065831627851911038512117013389e+184L, 5.58477447271859974925476129175614574e+190L, 6.47976647473179732779115257999520915e+196L, 1.17117420317317171387535528766936277e+203L, 3.34428267755162742939489398076764840e+209L, 1.53076558056194845489001829008909627e+216L, 1.14010185551912954998170816176845430e+223L, 1.40319939603195121408389523582430293e+230L, 2.89974936197123389451668716314083017e+237L, 1.02284833216532051937856086912560392e+245L, 6.26387216223145817942640023700922965e+252L, 6.77735971213475345230129326178116125e+260L, 1.31920032114466459135766725110063188e+269L, 4.70640341492973980389482209817290571e+277L, 3.13724441498341266441161693520329378e+286L, 3.98571382107801782113811082484615717e+295L, 9.85039693194582447444400924997258508e+304L, 4.83687381317675554709367324551273400e+314L, 4.82285202614019890234802453942628315e+324L, 9.98703239968301934722476402798215616e+334L, 4.39579560388542750852159646726102667e+345L, 4.21213245293498773753031381258995955e+356L, 9.00647826017501992250418906434620537e+367L, 4.40820570973056960340923739077146754e+379L, 5.07036496321109968512657627671150030e+391L, 1.40820593625978850981984707085120209e+404L, 9.71170922560303314648712317222502389e+416L, 1.71185299004740150846260848238269630e+430L, 7.94539187117702846067322763172557224e+443L, 1.00135866851780201306228838620557915e+458L, 3.53720801942577272956188201390769482e+472L, 3.61856514873836509067172206947444182e+487L, 1.10885899005952624619970603738429749e+503L, 1.05391354932473165812334641476989979e+519L, 3.22052822779158301893419142803165095e+535L, 3.28354332174247719776999412908271692e+552L, 1.16054782509911106933820737153108500e+570L, 1.47919833966504706156198174688381009e+588L, 7.08133783868969672071172398101964598e+606L, 1.32792489232842472677863721400834701e+626L, 1.01864740027867286775312222487805144e+646L, 3.34261841519397423195159276540555265e+666L, 4.91359232957804830460198547632977969e+687L, 3.39338961080932963058551495328009206e+709L, 1.15643101006244038688616288204262882e+732L, 2.04580060100421448267115092746141073e+755L, 1.97956226677236405650286559215774642e+779L, 1.10576482674657666978217926798043956e+804L, 3.76975534316002585938373910751464161e+829L, 8.30723712530280347580443574905340732e+855L, 1.25551214072288994426628885709173410e+883L, 1.38340901524945057559377504414014724e+911L, 1.18367583857036159478030255830898223e+940L, 8.39312984240762796712708279978405886e+969L, 5.27445408794117779852142535860614541e+1000L, 3.14827291975085189890248083313318702e+1032L, 1.91708860929520191993037622102307238e+1065L, 1.28205230070904948620917170049585485e+1099L, 1.01601053886738575799673411634857928e+1134L, 1.03205637073787228175634527391820202e+1170L, 1.45709248520841293241982349536788740e+1207L, 3.10836026767661586110721101783586616e+1245L, 1.09211875259058272695772315187071594e+1285L, 6.90756042513315914107755467851436390e+1325L, 8.62072629674515227183116754798059355e+1367L, 2.33367455582609053817676883927592981e+1411L, 1.51088703661957707064674032871094166e+1456L, 2.58751847359969149251723060639056652e+1502L, 1.30061395127966085145757755976532379e+1550L, 2.13606222552299322654005297188550444e+1599L, 1.28040046650693999927593879574416998e+1650L, 3.14004479696111085451082602060045251e+1702L, 3.54445798947819813290733040762684700e+1756L, 2.07959058928044687396800353571401401e+1812L, 7.18928436789152221379187719747515467e+1869L, 1.66674057039586083195182422956301841e+1929L, 2.96144319154423967308042996118314268e+1990L, 4.62818622228372755683383431403798070e+2053L, 7.33344579812102490343077358675798902e+2118L, 1.36418147108226874642787854899398972e+2186L, 3.46578234556914165783357030404723460e+2255L, 1.40565670831906218450256157719581702e+2327L, 1.06915717420033634558717882909227336e+2401L, },
      { 1.22722791705463782987186673092730169e-02L, 3.68272289449259047084662870963424169e-02L, 6.14133762687107999107794380606156658e-02L, 8.60515970877820790729887694832007789e-02L, 1.10762884001784544594356091768970671e-01L, 1.35568393495778548180370153140547366e-01L, 1.60489493745433548938038568465534727e-01L, 1.85547813164508949628358434474487020e-01L, 2.10765289867070052445854835116288718e-01L, 2.36164222221462626796458521613760417e-01L, 2.61767320678549526133854585166690062e-01L, 2.87597761063134290040469640683594256e-01L, 3.13679239524903564719983726260388052e-01L, 3.40036029353663277020320913399073202e-01L, 3.66693039873181019259593465683200480e-01L, 3.93675877638645179710808224562806486e-01L, 4.21010910174684626844126582677407364e-01L, 4.48725332504145034061613569614932171e-01L, 4.76847236732482946224267058692370227e-01L, 5.05405684968820937507178521019349537e-01L, 5.34430785882522907921272727308422687e-01L, 5.63953775213726713429575640521392073e-01L, 5.94007100577754899987320597582638522e-01L, 6.24624510926871605306723866692531653e-01L, 6.55841151058639796949436231740377164e-01L, 6.87693661588351492208131027097817883e-01L, 7.20220284833868340062109216385730821e-01L, 7.53460977094957222390047011401038344e-01L, 7.87457527846096346070444259791519611e-01L, 8.22253686402049937748559808122413409e-01L, 8.57895296659582580760899947902549978e-01L, 8.94430440566859300921740259698532581e-01L, 9.31909591024743548462187459499302759e-01L, 9.70385774981792065875818381502464879e-01L, 1.00991474754772858364115613090157760e+00L, 1.05055517801908314962647763431650363e+00L, 1.09236884878609257919070489976671100e+00L, 1.13542086817251430035405051069824787e+00L, 1.17977989835042446581611499466432280e+00L, 1.22551839957114260989771330348614290e+00L, 1.27271289206202647251525071825625510e+00L, 1.32144423705798506493224886295961288e+00L, 1.37179793856724595345645859982391269e+00L, 1.42386446761438409645831411537400020e+00L, 1.47773961086120811494347321807905579e+00L, 1.53352484567928885758582112389540236e+00L, 1.59132774393835509765516748425726982e+00L, 1.65126240698431007605515051243084836e+00L, 1.71344993451128821134705183982006487e+00L, 1.77801893028625685775809622156398356e+00L, 1.84510604796472086962932266394457464e+00L, 1.91485658054495189874749078346703174e+00L, 1.98742509734901709257017841100790150e+00L, 2.06297613279527528332657028179424398e+00L, 2.14168493164291678457053660944377397e+00L, 2.22373825584899452105160804133355977e+00L, 2.30933525868721379620595550412858017e+00L, 2.39868843234110382076332419963510302e+00L, 2.49202463580835609502681428341212971e+00L, 2.58958621064512275641789028727789739e+00L, 2.69163219284683244353140722232296853e+00L, 2.79843963001449729057960489638268331e+00L, 2.91030501390256265160483634586060233e+00L, 3.02754583949736496303590546881335550e+00L, 3.15050230294691972178684607549631061e+00L, 3.27953915196739433034632435779672485e+00L, 3.41504770380541061125886820639123018e+00L, 3.55744804745655073349177433143963235e+00L, 3.70719144864977981720917442750632409e+00L, 3.86476297812834212534134362353667523e+00L, 4.03068438601653134401780732606469366e+00L, 4.20551724758861383531605318041260028e+00L, 4.38986640858517245778656669889477004e+00L, 4.58438376139193074830218319449001571e+00L, 4.78977238695068769484017601922716285e+00L, 5.00679110126136326392507134554822932e+00L, 5.23625944981527405022093792997629943e+00L, 5.47906319833752315041593490985864953e+00L, 5.73616037388481741547685088499196591e+00L, 6.00858791672861985829483499535828449e+00L, 6.29746901064886304750229195240462364e+00L, 6.60402116738092913326417425575120773e+00L, 6.92956515012467783689325375523493772e+00L, 7.27553483138386097168028487959046984e+00L, 7.64348809212349206445236344518102598e+00L, 8.03511888250245928810782085772095077e+00L, 8.45227057947818812962999536922397314e+00L, 8.89695079364178531317631891553840631e+00L, 9.37134779701639517335117345438905815e+00L, 9.87784876557344603277558247754926945e+00L, 1.04190600552776203680920477623835667e+01L, 1.09978297590083170587228134865897514e+01L, 1.16172728242395225830192073579898657e+01L, 1.22807990484892461058359016635119689e+01L, 1.29921443119669104778276499842319618e+01L, 1.37554054553562588138800381241813305e+01L, 1.45750792662062131604851118656361439e+01L, 1.54561061010485246793800779406223808e+01L, 1.64039187433830292507497745621200597e+01L, 1.74244971815420897003963134996594183e+01L, 1.85244300868843752575149709549249943e+01L, 1.97109838837826649364051644134983470e+01L, 2.09921804308096164753912694285306280e+01L, 2.23768844801398294581745473457799699e+01L, 2.38749022527007382030837433327528532e+01L, 2.54970926638043046429091560004131137e+01L, 2.72554929623253155508089897070407353e+01L, 2.91634608111962498675086489719161981e+01L, 3.12358351442328496157597338087371982e+01L, 3.34891184913680511842485226603345913e+01L, 3.59416838798546509869193271004218290e+01L, 3.86140099030723073708193155942738501e+01L, 4.15289481132930302349687386068430483e+01L, 4.47120275544153339602356644078995085e+01L, 4.81918020222491017368298630788454953e+01L, 5.20002465436155875740198471916042599e+01L, 5.61732106253738449367657038097317749e+01L, 6.07509370691878207865695555214723350e+01L, 6.57786566116800396636894823760941404e+01L, 7.13072703735772134302608508415302565e+01L, 7.73941341346580579448409414339011685e+01L, 8.41039608526963339165414121007032304e+01L, 9.15098606849673444816151204282990934e+01L, 9.96945411354770401552161275609161166e+01L, 1.08751693942601889700382157585301539e+02L, 1.18787600064303753208251167345242212e+02L, 1.29922989761451637135259469378151165e+02L, 1.42295201505637253678660735501099867e+02L, 1.56060691466500267146385748883961050e+02L, 1.71397954932643240617797882975933747e+02L, 1.88510932515483007342988031066863162e+02L, 2.07632987774012593538662727768467525e+02L, 2.29031559465458736990493497603665470e+02L, 2.53013611565567646662052901877967628e+02L, 2.79932028239889691165098106403660221e+02L, 3.10193129976673089035599193653267696e+02L, 3.44265522210752989223327890530839313e+02L, 3.82690530328937838722167127466549147e+02L, 4.26094526620760770127774483839356753e+02L, 4.75203517589290204505256185266901137e+02L, 5.30860436623905886389110002239619071e+02L, 5.94045680537299500918768911865886523e+02L, 6.65901542833877826224842736585170818e+02L, 7.47761336730915387004112664458834008e+02L, 8.41184173047134302254019501114516601e+02L, 9.47996569801374152415232558717779048e+02L, 1.07034233137588184029692740932268166e+03L, 1.21074245751858265959674123771999362e+03L, 1.37216724155220581953081355884482812e+03L, 1.55812321218769272183468357733000347e+03L, 1.77275818866271628150109400030205931e+03L, 2.02098848541186298361330491694554409e+03L, 2.30865325932916315664195536323762336e+03L, 2.64270218981368427312930076396387941e+03L, 3.03142418286921021219727597009903791e+03L, 3.48472667698575601755930501247710368e+03L, 4.01447750473397350451805793460750946e+03L, 4.63492426404939475098008035447052569e+03L, 5.36320994977343974892087068772061383e+03L, 6.22000841211434280322218569985211936e+03L, 7.23030933285302995642999721294241650e+03L, 8.42439021673521778311062568091410678e+03L, 9.83902287153854178745596238243054980e+03L, 1.15189746308311398832174294139085014e+04L, 1.35188809887437420186309997879371889e+04L, 1.59055874546006694678895534154510667e+04L, 1.87610857276481617624948313285750831e+04L, 2.21862046239336627450920062330548753e+04L, 2.63052620505491535696779646542992118e+04L, 3.12719440194171105738301413156011152e+04L, 3.72767546125665292256151356126569138e+04L, 4.45564828031227324859783992706804335e+04L, 5.34062659201890392985687357687040023e+04L, 6.41950058038891812326608231510870856e+04L, 7.73851264238682005972799669756325711e+04L, 9.35579699398172596314688114151429601e+04L, 1.13446537582066946954261668152086820e+05L, 1.37977827220974171292929858459577092e+05L, 1.68327748580788705255881136304020641e+05L, 2.05992574612073530499207561599578962e+05L, 2.52882202450315825396186949822484192e+05L, 3.11442271834772591489327431348343937e+05L, 3.84814591343557073602914002196809171e+05L, 4.77048586496682264260701809258611041e+05L, 5.93380932472474085420815642783399996e+05L, 7.40606619035166611456854888146209865e+05L, 9.27573047147064337154043416441694054e+05L, 1.16584026094018041532121013906762681e+06L, 1.47056632211824613527279037871707355e+06L, 1.86169889901492197099442712763381073e+06L, 2.36558487029835449538535481946926618e+06L, 3.01715269550576487659856266530294113e+06L, 3.86288257359992924875408921517049995e+06L, 4.96486430558975035817682789982702410e+06L, 6.40636282995973660643182932326803288e+06L, 8.29948184726130211549565432685136745e+06L, 1.07957589264240185368036619492890404e+07L, 1.41008732747460409136778616182972970e+07L, 1.84951472441825010015226156029368615e+07L, 2.43622441967080550013889014979179789e+07L, 3.22295113186394123446611158573926085e+07L, 4.28249388238592533660285754208101809e+07L, 5.71579339433926763705565010979377786e+07L, 7.66343793274545163464956180626615373e+07L, 1.03221272549848969879325123115356857e+08L, 1.39683399197619484239843178827556776e+08L, 1.89925149766489273996177039954695089e+08L, 2.59486539646750585135581953531723138e+08L, 3.56266474246450149668190479113931322e+08L, 4.91582541317241347106730357233806750e+08L, 6.81731647011695814167689014048918740e+08L, 9.50299810520254143758401252895411400e+08L, 1.33159829534327753848250753466160796e+09L, 1.87580197601045983131635998256926235e+09L, 2.65667390770973148718390348704244405e+09L, 3.78324021561636590874538087942452353e+09L, 5.41753184850013697899991815866441223e+09L, 7.80169536989284750988699417135654043e+09L, 1.12996536895509883334977946121306167e+10L, 1.64614916139082192438237472977949088e+10L, 2.41235399573668769368537292636420902e+10L, 3.55648689543192709406699873104224405e+10L, 5.27534501409376051919461374149743655e+10L, 7.87357210832537817724326568648616226e+10L, 1.18256902031786360426341053633539951e+11L, 1.78754944250836346103770384839274996e+11L, 2.71963306497998614239753953623599024e+11L, 4.16512215311989794586279802552971310e+11L, 6.42178185820513419673438138950789714e+11L, 9.96872549757627591785162935837099498e+11L, 1.55821232712296039863279650270498705e+12L, 2.45280998490709378571967041325143017e+12L, 3.88865623282814020969225986222930869e+12L, 6.20986899050942490907341584961146266e+12L, 9.98992421629798366453910164990598035e+12L, 1.61915800137861135092472802019186093e+13L, 2.64432451866992655897802160266314720e+13L, 4.35201884790437478598011793154963363e+13L, 7.21888468820274170949384690425513293e+13L, 1.20699764072734953803867091697058471e+14L, 2.03448372244520740184714594138600722e+14L, 3.45755310287440291972421764598644671e+14L, 5.92524851195750570615520553006806424e+14L, 1.02405779371303867152496937066868237e+15L, 1.78517404594164216208571127025070841e+15L, 3.13930698866849469593365078009075683e+15L, 5.56985627017489012771720073467849986e+15L, 9.97176335383446032822138577325280050e+15L, 1.80168749111488309180201148764368470e+16L, 3.28570985832256554206799328627718020e+16L, 6.04901854091075971038418094513619254e+16L, 1.12437528321136957183940077137648079e+17L, 2.11044512595243530534854315009251863e+17L, 4.00073700789122999206498186212756047e+17L, 7.66084936156432930897007145016954725e+17L, 1.48201877099617670026480296368024580e+18L, 2.89694543391085794507262308584519511e+18L, 5.72279016569347049314117808244814073e+18L, 1.14268996043992146219736887475145416e+19L, 2.30661655998410672274449611000740199e+19L, 4.70785718461609386331913071438798214e+19L, 9.71734634749534281292184698073106843e+19L, 2.02873560562258544354738566036230939e+20L, 4.28484025417100058060231110755876699e+20L, 9.15702732902162383627232801008773485e+20L, 1.98045783476641177674243338550380776e+21L, 4.33560488670225200389961701585516223e+21L, 9.60925855971422399500251930072135526e+21L, 2.15660463060858699692371033788512835e+22L, 4.90204590969527028901267517972005859e+22L, 1.12874922712132846722486415215181510e+23L, 2.63341462304993087921430516281962614e+23L, 6.22633568449099854312810660038498408e+23L, 1.49220527901414892100221726222044247e+24L, 3.62576824971759010933939677750446994e+24L, 8.93389976496144488245242728630867648e+24L, 2.23278698168226238263925345166626442e+25L, 5.66129533629398673236467837398265907e+25L, 1.45661671029813314161310452512420504e+26L, 3.80395985286848824540247897299973294e+26L, 1.00853158560303648965770564091212618e+27L, 2.71524742512942335771421173573350598e+27L, 7.42507176676665196668682930989072343e+27L, 2.06286071217322500340710263294206778e+28L, 5.82405545879941331172090843001915861e+28L, 1.67138883669643664401793246595736207e+29L, 4.87683063202395639212825400688637822e+29L, 1.44717007114610715647680482586441902e+30L, 4.36856220892558378299543245366740004e+30L, 1.34187380624925133807232471810065508e+31L, 4.19525163275433868171703825329949387e+31L, 1.33536013482821413616157861182819558e+32L, 4.32868135071513633988478443702722304e+32L, 1.42940186615031918577626278068758606e+33L, 4.80973614622718069618478648576546160e+33L, 1.64962411456760257539667516081290921e+34L, 5.76867749241980146921395997917892869e+34L, 2.05744285416276135030539237103441019e+35L, 7.48642350991781106283346301415985262e+35L, 2.78005279179115505131111680664033100e+36L, 1.05390834766008187386632631340309755e+37L, 4.08004633423575422346694643614725290e+37L, 1.61355331159280537271141620030872711e+38L, 6.52083633299761509812680604349515166e+38L, 2.69384818625751099248700493623895698e+39L, 1.13800240843071080011666704740931517e+40L, 4.91774800881392461265365745148130197e+40L, 2.17469107319135867637675221560360895e+41L, 9.84452374543052650164816403972682005e+41L, 4.56370746759011673249611086081412694e+42L, 2.16735207370837913732627450052443800e+43L, 1.05486019388717075432105322007580054e+44L, 5.26358822556684736475437714203858810e+44L, 2.69377245879791662278222405278060831e+45L, 1.41450676056016307353065763949943988e+46L, 7.62412676351201661991424110428992680e+46L, 4.21982814876279441119734812029287822e+47L, 2.39938766583179326419454951543637572e+48L, 1.40213994725411743432292186837126106e+49L, 8.42470632552542294312824936421510125e+49L, 5.20691847994261931825305937850643235e+50L, 3.31178786647771615130587348221001406e+51L, 2.16868329550985915487173624396649870e+52L, 1.46278636877920671278346292184781720e+53L, 1.01676178457583836251098427369753410e+54L, 7.28646099514504318390031595979099009e+54L, 5.38619423744886540736133132036039525e+55L, 4.10891748052874064027571987877723004e+56L, 3.23644562594555272761751761693655719e+57L, 2.63344065241761966896134121065725150e+58L, 2.21470233935793926784783273212022849e+59L, 1.92605899594826839190384056135440397e+60L, 1.73306774041417493183516299971659594e+61L, 1.61430716012442696900215232150692807e+62L, 1.55746432848635213822960416335985349e+63L, 1.55722615519719203130606727886637242e+64L, 1.61447396270799534426677571780472075e+65L, 1.73661740632738610492724345532304845e+66L, 1.93920124345119052093186699988150897e+67L, 2.24927773293662287552890832545827854e+68L, 2.71159379871976559871119761600619238e+69L, 3.39962873204868711911086284140566930e+70L, 4.43538969673020629145547409643969707e+71L, 6.02556607616400398134689309091612155e+72L, 8.52916142538377984909758513093573554e+73L, 1.25874632299298868846704179091958707e+75L, 1.93811217518656021005493439215804850e+76L, 3.11543236357261066088271223403230465e+77L, 5.23179767443439001771782033104475730e+78L, 9.18493020786068075728039142759876527e+79L, 1.68692940478037877214426031162304858e+81L, 3.24356562447423263461643222429327622e+82L, 6.53381249893022007452896440352725950e+83L, 1.37989882314462031449133502146398295e+85L, 3.05765044484283991620932285441735220e+86L, 7.11405054583917124472478320152556329e+87L, 1.73927502444225867447030753694640018e+89L, 4.47178291585317780365328749453520010e+90L, 1.21003678949402814425759810458701117e+92L, 3.44882804459086235858169840236819767e+93L, 1.03622678375056156465794049465547871e+95L, 3.28480191475120603834848118907352203e+96L, 1.09951493360222463836336540053884847e+98L, 3.88958173137824259730333790282903582e+99L, 1.45543428790106999149108053530575702e+101L, 5.76572993438741901863543434592570728e+102L, 2.42034956874547558153049344213268717e+104L, 1.07760662592977753585432532225577347e+106L, 5.09334698869585184485524316150431382e+107L, 2.55809082411032399725764692067377039e+109L, 1.36651250871904796376617702296414102e+111L, 7.77173580076352640634836399351925362e+112L, 4.71039863879301491830684051990330316e+114L, 3.04556388558701395434830609997782218e+116L, 2.10276255286144299282309486039761104e+118L, 1.55193753621259613591555622965540441e+120L, 1.22567635442607596952721498552003566e+122L, 1.03695094616970371146721395255392215e+124L, 9.40788526897082771733750170914488215e+125L, 9.16336910778509317089313688240113814e+127L, 9.59253109567116892554520172575780003e+129L, 1.08048629336182387529116109869898182e+132L, 1.31103482955778245018110865569797769e+134L, 1.71564297593263918799907327094023177e+136L, 2.42423174270788187824126863980761699e+138L, 3.70323122333312791897064549131589865e+140L, 6.12322502740998890169801438496313135e+142L, 1.09727104077119676484946099784861221e+145L, 2.13369364324129597715060714813970067e+147L, 4.50809918489577732767734472523944713e+149L, 1.03625280668629118864461701581877696e+152L, 2.59492839368798847123238762058931299e+154L, 7.08856065084285960511459463166728250e+156L, 2.11523492515131956204689196091626210e+159L, 6.90448414659433816403326070235535578e+161L, 2.46882138492543386747726128195908117e+164L, 9.68405620339094731040251680095733169e+166L, 4.17318211031646842247767173261932179e+169L, 1.97862256312448325694613011941349554e+172L, 1.03370703084875834422987498885049116e+175L, 5.95983823379168614646426490979367954e+177L, 3.79793920709700480341002000892325755e+180L, 2.67930862060725892983600714882836079e+183L, 2.09582063696050703960100191266610779e+186L, 1.82074244214148973928235757920582367e+189L, 1.75963855825241574501304502726864570e+192L, 1.89499379332809905379546394212298551e+195L, 2.27793735309853697376707463982899100e+198L, 3.06180392186430523426845441481052145e+201L, 4.60976071298011598021963844074936266e+204L, 7.78790454205446308413194702704532804e+207L, 1.47908157180353184742483342055946619e+211L, 3.16368583872054810499056635486446885e+214L, 7.63549763158546449019055671859541326e+217L, 2.08329116709848069811436257881072129e+221L, 6.43828341650089537912773112646579654e+224L, 2.25813433461443519689405569138116459e+228L, 9.00646808360283887236786414348123292e+231L, 4.09320439419929471708152017376102969e+235L, 2.12407325647515198386324613590453899e+239L, 1.26118767744139661919617876453932054e+243L, 8.58648519817790365555570528355195441e+246L, 6.71757354945431655557160007037610215e+250L, 6.05231172230061504649457274179966269e+254L, 6.29372154343611302741074349158106138e+258L, 7.57097597581835804454005529249965405e+262L, 1.05596735728342565613175490019879868e+267L, 1.71165116794080014782255848570375371e+271L, 3.23201945416757368365209341689336569e+275L, 7.12640648337469193163827718081711822e+279L, 1.83935498907936650155252747841577857e+284L, 5.57103618187823353431701691950875589e+288L, 1.98507054903582594730288556561442957e+293L, 8.34251060679185969108286852637314516e+297L, 4.14598050279815660053056892060055710e+302L, 2.44294673707515955659314553816687272e+307L, 1.71128222954502480992240259231707340e+312L, 1.42900308749681080618580596429329500e+317L, 1.42642910690539638749189376290614404e+322L, 1.70684173154227881505687976959428819e+327L, 2.45528610324097413976825941240621397e+332L, 4.25829782132342793910509184917611473e+337L, 8.93046646026781112771479518830708119e+342L, 2.27151544737307916343177075009962798e+348L, 7.02878727199449743249339960113532074e+353L, 2.65404789370370153123009487802961972e+359L, 1.22676780524208968705932842819459632e+365L, 6.96344727462083702750636558199408867e+370L, 4.86966795424296645258294731476827244e+376L, 4.20934473165669380666938166564826622e+382L, 4.51252011030278817555903939588122000e+388L, 6.01984854233799583525556662766352847e+394L, 1.00278809583154248088779511185941092e+401L, 2.09319142855536626778348201007914114e+407L, 5.49449523120000769396463190666315855e+413L, 1.82025791721843330667826837307018788e+420L, 7.63864295498675379110157901286555290e+426L, 4.07562853905456437955133694031162566e+433L, 2.77530355159307878582751292111532382e+440L, 2.42120842363222072539668100784464301e+447L, 2.71677827391519917988250533365057684e+454L, 3.93638693069501511102210790122717956e+461L, 7.39454183839498133330514511562965145e+468L, 1.80830021083203927258676228302841634e+476L, 5.78068632124848075130373030745294118e+483L, 2.42588452835387252450192322587436972e+491L, 1.34215845252903171028854514732509154e+499L, 9.83265508980143417319549250479290229e+506L, 9.58056982887069577741485477351756765e+514L, 1.24714172045678509784766926344381810e+523L, 2.17883996343750012869434076146952309e+531L, 5.13253875779605180900218275684122450e+539L, 1.63787417999262086647656267832207076e+548L, 7.11453353628067905769574986613707006e+556L, 4.22706394134172663816196292889312018e+565L, 3.45223064191878555675199989739485645e+574L, 3.89497996822823191874712723630878414e+583L, 6.10190158010921783812771061850774756e+592L, 1.33421132288849391233542855134113771e+602L, 4.09320749021551498574887738660496448e+611L, 1.77132716471266560117452274418925035e+621L, 1.08713213788405301831391213988591367e+631L, 9.51489736810900467024086259204897883e+640L, 1.19423949258625915586962226198410651e+651L, 2.16177179741543970148744161752725437e+661L, 5.67627906160460395827390123125168239e+671L, 2.17468780419782497779087319492719363e+682L, 1.22290652527655281598138666471594618e+693L, 1.01549393259398829829790551999023256e+704L, 1.25289600863132317250645757199143453e+715L, 2.31107594661956354485796436668094840e+726L, 6.41394790132987132728494077504399223e+737L, 2.69551958118799603835043225368071745e+749L, 1.72664572305999101481397361296053504e+761L, 1.69703260411568714431268560545429241e+773L, 2.57650655058878846949421879421719884e+785L, 6.08415755628422694511089641903676709e+797L, 2.25018717111577650332251351426363709e+810L, 1.31266605271635406458566527913919394e+823L, 1.21653337436496205608516398221959572e+836L, 1.80423787152105848481394522091997928e+849L, 4.31399102346512532331941966328171850e+862L, 1.67550866484958200095075315692314084e+876L, 1.06515761703845778105889680563699868e+890L, 1.11699400379133199608851392356049526e+904L, 1.94751426714424559351617923238456424e+918L, 5.69089114062808012687080164398087070e+932L, 2.80983921519267570591812248264495618e+947L, 2.36358690419951504134253704409272547e+962L, 3.41581578617948637677662230945252306e+977L, 8.55364646944583859412326553382242541e+992L, 3.74370530455423912034790817625191291e+1008L, 2.88911104228589616462565573594290893e+1024L, 3.96659525977064102663870040642245025e+1040L, 9.77693110684684436928410437465483271e+1056L, 4.36636679166615668883990202463770904e+1073L, 3.56644781478399708285654633626787310e+1090L, 5.37871519143314493436384142989505797e+1107L, 1.51231507376826438439352643482625072e+1125L, 8.00547597409214659416862336552752652e+1142L, 8.05824292014776003962551051182620642e+1160L, 1.55810667707874651581920999067306413e+1179L, 5.84685322894210562805362663900103590e+1197L, 4.30279053871861463775007328286068764e+1216L, 6.27606568696844727186804750244767860e+1235L, 1.83405691639178257209003812558712559e+1255L, 1.08562206577049472147312923532596973e+1275L, 1.31617110088183556471407953592008454e+1295L, 3.30534811890625751178778666339188597e+1315L, 1.73929710751143724152073404455311704e+1336L, 1.94017558758640506527365311633351598e+1357L, 4.64256117877888468390840687039281473e+1378L, 2.41181028167559547953976181592626603e+1400L, 2.75359028326650146526233980288420348e+1422L, 6.99538184026698121966983693939988891e+1444L, 4.00451124186760586509360315430592520e+1467L, 5.23202484555080004711423052985251790e+1490L, 1.58057811961053246035386581678791248e+1514L, 1.11871792077110492782575037665515064e+1538L, 1.88021076704060084216083677757823795e+1562L, 7.60656064089491206752552083826812435e+1586L, 7.51058336307020750806534892000369186e+1611L, 1.83554419567039732901090730190332067e+1637L, 1.12631598317224168716420027493130577e+1663L, 1.76058045324267978077071148859252887e+1689L, 7.11454090252573253550862708170430267e+1715L, 7.54447665013986581882495689819567483e+1742L, 2.13157561258614754140817896581925999e+1770L, 1.62953544267683384450064825605699335e+1798L, 3.42393824570039208761545128642814549e+1826L, 2.00910152737038919233657554807306054e+1855L, 3.34591874171713502502193846562110253e+1884L, 1.60768420173566681503057284560969387e+1914L, 2.26623321264716591681578650589551852e+1944L, 9.53208108881430895613749023747672822e+1974L, 1.21710154769951529957336846304572308e+2006L, 4.80082954212402831503263841738379326e+2037L, 5.95484092414598408997112765002169497e+2069L, 2.36496081234284780469612914593602194e+2102L, 3.06292618387296708176633714456063693e+2135L, 1.31792936450130144017383754294435167e+2169L, 1.92000470996606623202622993195686002e+2203L, 9.65401246356529853165036795448719991e+2237L, 1.70836636787270782624240314280943940e+2273L, 1.08524396756651055665425968680627268e+2309L, 2.52515527758383642102305453689375992e+2345L, 2.19655419566926380800477364631433717e+2382L, 7.29302139222844158929601074949211737e+2419L, 9.43942215540978305304206842223557338e+2457L, },
   };
   m_weights = {
      { 7.86824160483962150718731247753528972e+00L, 8.80516388073301111614873617217928009e+02L, 5.39627832352070566780640331455747505e+07L, 8.87651189696816131653619534136597185e+19L, 2.43279187926922555339407396412240848e+52L, 6.39971351208020291074628474066382445e+139L, 4.88537754925138382971529406336289926e+376L, 7.16883681174178397147232888689884595e+1019L, },
      { 2.39852427630263521821751964327863340e+00L, 5.24459642372668102196104972893810164e+01L, 6.45788781959820175983419155536535220e+04L, 2.50998524251137450575088710958358197e+12L, 1.77402926932713870128394504873348147e+32L, 2.78140611598309731397930137939524577e+85L, 1.96038425539892219077400134901764646e+229L, 3.66150166331050442649946863740591804e+619L, 4.83160223742266359875093017291942249e+1679L, },
      { 1.74936958310838685164948357553735581e+00L, 3.97965898193460781260142246747934196e+00L, 1.84851459857444957000370375296526856e+01L, 1.86488071893206798762140574520079510e+02L, 5.97420569526326585534733485801413054e+03L, 1.27041263514462334076609190768051333e+06L, 6.16419301429598407050713079962266577e+09L, 5.23085003181122252980303620742723664e+15L, 2.22626092994336977442577822584431966e+25L, 1.19993110204218159156995352671876189e+41L, 7.47060214427514621386633671542416223e+66L, 1.81446586052841067579857900138568293e+109L, 9.97368828205170536427067380214302839e+178L, 6.91104863699861841609881512683369620e+293L, 1.09318211394169115127064538702854062e+483L, 6.82844528756738528157668552351754659e+794L, 5.72058982179767644458682394627277737e+1308L, 8.56032690374774097638382410967859196e+2155L, },
      { 1.61385906218836617287347897159238284e+00L, 1.99776729186967326240633804225996920e+00L, 3.02023197990883422014272607858586524e+00L, 5.47764184385905776136260784219973923e+00L, 1.17966091649267167180700675171097629e+01L, 3.03550484851859829445106624470076282e+01L, 9.58442179379492086007230677188042206e+01L, 3.89387023822999207648300659707623000e+02L, 2.17919325035791134362027855479039721e+03L, 1.83920812396413285238691815996130729e+04L, 2.63212061259985616674528417452471371e+05L, 7.42729650716946820985576548297567563e+06L, 5.01587564834123235599460194208596041e+08L, 1.03961086724154411336246801784030341e+11L, 9.10032891181809197707572646881781254e+13L, 5.06865116389023157141527800972456655e+17L, 3.03996652071490261550678134010186516e+22L, 3.85774019467200796234141077624516370e+28L, 2.46554276366658108662483315347592086e+36L, 2.41643944916779946083480638296809418e+46L, 1.51709155392660414912154078562432491e+59L, 3.82504341202141137966304147261186085e+75L, 4.08958239682159863968142401533332977e+96L, 3.82377589429556404969214069508991010e+123L, 1.52310131188389950567280579870007714e+158L, 3.79636429591225792206340580596443044e+202L, 3.58806569407775804668752012761380285e+259L, 4.80771394235873283225580334249205764e+332L, 3.53246347094395956018650264083397114e+426L, 1.10588270611697913736223556513462127e+547L, 5.39876096236024571737551333865366627e+701L, 2.11595993056851385189640470523173475e+900L, 1.96510021934867185245970661270267904e+1155L, 4.44393205109502635384704176698510343e+1482L, 8.87699807096655545789464168899561262e+1902L, 3.92593109193326452307669545677335709e+2442L, },
      { 1.58146595953669474418006927755018553e+00L, 1.66914991043853474595928766810899114e+00L, 1.85752318859500577038805456054280667e+00L, 2.17566262362699411973750280579195666e+00L, 2.67590137521102056412774254907718688e+00L, 3.44773868249879174414308452011308777e+00L, 4.64394654035546412593428923628065357e+00L, 6.53020449657424861626175040050215115e+00L, 9.58228501556680496101434956408351228e+00L, 1.46836140751544096048943311347045852e+01L, 2.35444954874098753301967806270994218e+01L, 3.96352727330516670475446596081886449e+01L, 7.03763520626753854729866526912608057e+01L, 1.32588012478483886772029444321222706e+02L, 2.66962564954156917240259777486475719e+02L, 5.79374919850847267642551329761882844e+02L, 1.36869192832130360517783396696756604e+03L, 3.55943572153313055370459329132660088e+03L, 1.03218667727076331844496375241465304e+04L, 3.38662130285874148722242481662728477e+04L, 1.27816625984024682983932609690817446e+05L, 5.65408251392669309819682774275375355e+05L, 2.99446204478172183285915112991350446e+06L, 1.94497502342191494704216422938073734e+07L, 1.59219300769056058775193216743984073e+08L, 1.69428881861745991307405037132734657e+09L, 2.42715618231130327064401251217732118e+10L, 4.87031784819945548978505622564878836e+11L, 1.43181965622918179279761684530563788e+13L, 6.48947152309930125567236832263111486e+14L, 4.80375775250898910621624420686976943e+16L, 6.20009636130533154063228597685772759e+18L, 1.50256856243991489872472363253210358e+21L, 7.43606136718968825065770257988550430e+23L, 8.26476121867792860277670169213789988e+26L, 2.29773502789780434527854280137181506e+30L, 1.80544977956953499695878114747293447e+34L, 4.60447236019906193095622500171344417e+38L, 4.45837121203062685356208723886733392e+43L, 1.95763826111480930920135175891530462e+49L, 4.76736813716250076391436967954985413e+55L, 8.08882013947672128478290968549029064e+62L, 1.23826089734928635728800137667726115e+71L, 2.29227250527884206154485624438166937e+80L, 7.15139237374919354944182099110281069e+90L, 5.47671485015604443103262357109062276e+102L, 1.57665561837070068115746726288773175e+116L, 2.76544859595785195782707224216472471e+131L, 5.10805125528313267335507302418826239e+148L, 1.84712724771593770593326127127227089e+168L, 2.64019849791365137425805671512502957e+190L, 3.30712207665004277982044248938187504e+215L, 8.94854908018147130170828014681013313e+243L, 1.45387781135416845215757148230318159e+276L, 4.51726707740089625471551690811905600e+312L, 9.97430447590682462482304866834403173e+353L, 6.92693028486990716815899177110112886e+400L, 8.16310561774970561761094556639568748e+453L, 1.10229203674018117764688357752075301e+514L, 1.48517414340573263317638725427854581e+582L, 2.31930659130340479680373862468017961e+659L, 6.75943980795305614491339585238335763e+746L, 8.57037256903489416057847741388693004e+845L, 1.67601881139415883633596646631875881e+958L, 2.88227840597067494281761984210848274e+1085L, 4.25760590360997372209644858208981618e+1229L, 9.71180927884114514977776411099980676e+1392L, 1.22768447544318231419683656745290694e+1578L, 6.75552744777466879522724469574589977e+1787L, 3.08769330528996647489562213811332711e+2025L, 6.11556995885701147380172201043083647e+2294L, },
      { 1.57345777357310838646187303563867071e+00L, 1.59489275503866378652125176234375753e+00L, 1.63853651553023474161201404240726646e+00L, 1.70598040821221362042267472116012952e+00L, 1.79972439460873727464624167982278042e+00L, 1.92332285442565630724716834865923523e+00L, 2.08159737331326817824115844787944556e+00L, 2.28093488379007051069614759371332643e+00L, 2.52969785238770465522974588384012833e+00L, 2.83878478255295118549225363326336900e+00L, 3.22239574502098061154099310373833179e+00L, 3.69908135885423511174795221061725889e+00L, 4.29318827433052679979662014839909054e+00L, 5.03686535632233007617225633242190622e+00L, 5.97287114091093219903794699750262296e+00L, 7.15853842431107756435392592099817447e+00L, 8.67142780089207638456187671594514668e+00L, 1.06174736029792232565670017416978690e+01L, 1.31428500226023559963724543395522033e+01L, 1.64514562566842803959315507240301356e+01L, 2.08309944999818906870911848992408145e+01L, 2.66923598979164019005652953856219015e+01L, 3.46299351479137818875120770497256101e+01L, 4.55151836265366257897259922581190951e+01L, 6.06440808776439211617961123881399848e+01L, 8.19729691748584679772374579308014156e+01L, 1.12502046808165256396405270593673624e+02L, 1.56909655284471412314380735570422525e+02L, 2.22620434786863827630193937721495593e+02L, 3.21638548950407775497940425181412035e+02L, 4.73757450594546173931972158718216152e+02L, 7.12299454814699763671577043536625350e+02L, 1.09460965268637655307120839628626644e+03L, 1.72169778917604957594996919520027157e+03L, 2.77592490925383514631415480161515100e+03L, 4.59523006626814934713366494475518926e+03L, 7.82342758664157367197225266771664916e+03L, 1.37235743526910540538254088034330370e+04L, 2.48518896164511955326091118611007805e+04L, 4.65553874542597278270867281925971401e+04L, 9.04176678213568688399001015321300800e+04L, 1.82484396486272839208899824313480775e+05L, 3.83680026409461402659414641247062580e+05L, 8.42627197024516802563287310497296237e+05L, 1.93843257415878263434890786889254173e+06L, 4.68511284935648552770705724295731096e+06L, 1.19352866721860792702129481323969824e+07L, 3.21564375224798931587480096707273872e+07L, 9.19600892838660038574630990314077029e+07L, 2.80222317845755996380600711673673348e+08L, 9.13611082526745888639724836491288094e+08L, 3.20091090078314859097182280497599932e+09L, 1.21076526423472368892320719657876832e+10L, 4.96902474509310180847282308967509245e+10L, 2.22431575186385521573661995325533148e+11L, 1.09212534444931366008730317965795763e+12L, 5.91688298001991935913437013926087352e+12L, 3.55974343849457724875846980164154730e+13L, 2.39435365294546519110955303872941851e+14L, 1.81355107351750191688958030705258863e+15L, 1.55873670616616573768075986251057862e+16L, 1.53271487555511433265791312985848665e+17L, 1.73927477619078921206684554401295421e+18L, 2.29884121680221631264260395223605839e+19L, 3.57403069883776266374408947394989771e+20L, 6.60489970545141907950722926660909000e+21L, 1.46715587959182065910258109064501314e+23L, 3.96409496439850938104106380506197921e+24L, 1.31934284059534879276193138323357204e+26L, 5.48225197134040074244755342952808857e+27L, 2.88513789472382751842871762855110475e+29L, 1.95253984076539210960955400019857653e+31L, 1.72705148903222279729658337426192599e+33L, 2.03134350709543939580166226396358099e+35L, 3.23607414697259998035422932108168633e+37L, 7.12048741298349720027592249584649287e+39L, 2.20955270741101726546978031488466999e+42L, 9.88628264779138464833350749737352873e+44L, 6.53051404878827352855218417636385136e+47L, 6.53070667248154652797117704066842547e+50L, 1.01551880743128195070423638840822631e+54L, 2.52636677316239450980056816553072407e+57L, 1.03645051990679029664966085872637264e+61L, 7.24196603262713586073897504016245799e+64L, 8.91940252076971493816577515322150818e+68L, 2.00846361915299290502283821529295631e+73L, 8.59691476483026002005094018998424562e+77L, 7.29059954682949521964023913634428261e+82L, 1.28019956321641911210408802307006043e+88L, 4.87834928560320114983891893560226105e+93L, 4.24082824806412793978885646592598549e+99L, 8.86977176472159872046976711615695992e+105L, 4.72334257574141766931261505651248075e+112L, 6.80203596332618858057580041658814811e+119L, 2.82453118099000954895979587744113614e+127L, 3.62104921674598225195529453488856248e+135L, 1.54127015033494251978994281882356439e+144L, 2.35337699517436278507886852532825112e+153L, 1.39975657916895026384257958622345253e+163L, 3.54037503829826541837221286091526442e+173L, 4.18047500721395810079642214620070836e+184L, 2.54530778191403081572971577964641848e+196L, 8.88250526322285889574436025179770286e+208L, 1.98845751036127430533908054978079217e+222L, 3.21915661509946819003841034873947498e+236L, 4.28183215516658291456439674095688919e+251L, 5.36006145974509206228714676601426569e+267L, 7.29722806821457796264952588871034685e+284L, 1.26019994797636341237075623262002305e+303L, 3.25215241711283834871808603425369243e+322L, 1.49313102632599158215031971670664716e+343L, 1.46842377691796129245813472736592684e+365L, 3.76931518236156669675469780787997744e+388L, 3.11671991143285521404945273561352902e+413L, 1.03852445906488241953781523768340610e+440L, 1.76991068936691550622045923706396857e+468L, 1.98843278522413458075823481121326426e+498L, 1.92935688449069146367365017414279935e+530L, 2.15545898606278242992831028009446695e+564L, 3.76556572065746315676705102085514331e+600L, 1.42493629186903074753303377268119903e+639L, 1.65224172380259971159552714105694350e+680L, 8.49215944410421946026158352935784666e+723L, 2.86631893981320588349507785370693896e+770L, 9.65377141044877270160840903468092119e+819L, 5.06475979937176267805686278644876593e+872L, 6.64979079902917739733555562625190968e+928L, 3.61927525412094487856735509111512169e+988L, 1.39737404381894022658529893979427383e+1052L, 6.78018387620311055320186127570560827e+1119L, 7.59952044103401209205510909410129754e+1191L, 3.76162862667265940011010617225061429e+1268L, 1.63904309514033654209643026524489542e+1350L, 1.31017858257252889370615733020834140e+1437L, 4.19821396072116298475329351264459339e+1529L, 1.23923791809549425227491870065845752e+1628L, 8.17087984671936465200033730232853592e+1732L, 3.08946915513007963115203650683840400e+1844L, 1.82761916569338643673217752139790470e+1963L, 4.92348318803338950087945240543349430e+2089L, 1.88350835331965060456271693216651123e+2224L, 3.43360015586068277793841059306542129e+2367L, },
      { 1.57146131655078329437644376888801138e+00L, 1.57679016631693834484126246342345329e+00L, 1.58749564037038331646069625234555521e+00L, 1.60367395634137020950291269057600307e+00L, 1.62547112545749394267938666988196139e+00L, 1.65308501191593930171844822364141731e+00L, 1.68676814252591123625053019619255158e+00L, 1.72683132353751620184313195813656600e+00L, 1.77364813866723660171326886678529701e+00L, 1.82766042147866144821632902536146347e+00L, 1.88938481704401819562549601165115021e+00L, 1.95942057285503709111658357403615203e+00L, 2.03845872804790892275259742585569433e+00L, 2.12729290408384722476899855222432786e+00L, 2.22683194019907694104850876757126753e+00L, 2.33811466455513029581902771513343722e+00L, 2.46232714872299130380967769641847017e+00L, 2.60082286092708516436098022317536381e+00L, 2.75514621481455435937229822050004007e+00L, 2.92706010842448355516791030533685920e+00L, 3.11857816624092195149666191379786472e+00L, 3.33200254033950662971711811118318338e+00L, 3.56996830041074027550628111498923678e+00L, 3.83549565399644726181552365400966870e+00L, 4.13205149651293488468153425460318353e+00L, 4.46362210669906788115667211361364802e+00L, 4.83479919100800655701530131187647507e+00L, 5.25088195776567960767293727240320892e+00L, 5.71799849087533312421158643544587353e+00L, 6.24325042159856810533215887376856930e+00L, 6.83488580122654183866366620676303900e+00L, 7.50250620278934080195915244309172491e+00L, 8.25731548449354420110207897041998984e+00L, 9.11241940586464263380079984086513597e+00L, 1.00831874954399775830998395457182684e+01L, 1.11876913499386520162594085796167645e+01L, 1.24472370591410688131760615976965093e+01L, 1.38870139060550758665255786705519681e+01L, 1.55368871591590018952847646637454593e+01L, 1.74323700068094283108112572669239084e+01L, 1.96158189482399342378436216339159090e+01L, 2.21379088635427380602824337731297019e+01L, 2.50594593467713760968808829296439171e+01L, 2.84537037774213756120135736520435304e+01L, 3.24091184596952483433430577491101072e+01L, 3.70329628948023016142035941015485370e+01L, 4.24557264474626791146220920324192917e+01L, 4.88367348033798558171280311705362727e+01L, 5.63712464058697542020818458752120814e+01L, 6.52994709275261034008086934786702551e+01L, 7.59180775569412283714380617539343408e+01L, 8.85949425239166382162609558265821220e+01L, 1.03788129500578812415162165531670704e+02L, 1.22070426396922674625756853326498468e+02L, 1.44161209813120053456742433728771078e+02L, 1.70968019124577351122499690038707734e+02L, 2.03641059384357556999222931732850856e+02L, 2.43645005870872364316059216110059642e+02L, 2.92854081218207610543177302881458406e+02L, 3.53678601915225339202500542949917795e+02L, 4.29234308396729693907268968564609660e+02L, 5.23570184048873302665650155276206906e+02L, 6.41976689800302457508415746115180260e+02L, 7.91405208366875928332161102969868782e+02L, 9.81042208908193163688535235538889459e+02L, 1.22309999499974039331272100414750813e+03L, 1.53391255542711212726525490457861725e+03L, 1.93546401360583033885511419988831009e+03L, 2.45753454991288685207196097797062484e+03L, 3.14073373162363551894782974867325472e+03L, 4.04081818856465189750723035050638141e+03L, 5.23488159971222568136194576211612571e+03L, 6.83029445760732922582000345557383347e+03L, 8.97771322864988714349790288910627671e+03L, 1.18901592096732683947870697270695144e+04L, 1.58712238704434696215746702894136306e+04L, 2.13571110644578933143540830593360855e+04L, 2.89798370518968143718297906801146350e+04L, 3.96630672679554794954087134590560762e+04L, 5.47687519375000078688553248953764732e+04L, 7.63235653938805568016142182690471993e+04L, 1.07371914975497695062170132456450593e+05L, 1.52531667455557415180420019888676134e+05L, 2.18877843474421658617449971461581691e+05L, 3.17362449601929560775572985879585343e+05L, 4.65120152586932846156175983364596723e+05L, 6.89253765628058057206329188782990860e+05L, 1.03311988512001998186230013733752720e+06L, 1.56688798104325249948154338241641806e+06L, 2.40549202702653179507492073842261453e+06L, 3.73952896481591033994026114668796352e+06L, 5.88912115489558003244667233279563785e+06L, 9.39904635192234203007457377008255614e+06L, 1.52090327612965351790614542733698474e+07L, 2.49628718729357616773929842263043964e+07L, 4.15775925996307484022703929049831425e+07L, 7.03070536695026731176158877103497776e+07L, 1.20759855845249336607363637052870598e+08L, 2.10788250946484683324104572583041111e+08L, 3.74104719902345786397351281755078701e+08L, 6.75449459498741557237788387723071522e+08L, 1.24131674041588053743260021864117384e+09L, 2.32331003264955286246594304305695965e+09L, 4.43117601902662575879840759020751450e+09L, 8.61744648740090012951326726568665021e+09L, 1.70983690660403151330619158427281665e+10L, 3.46357452188017133850611855017361923e+10L, 7.16760712379927072634183907084755700e+10L, 1.51634762091005407908273857408800162e+11L, 3.28172932323895052570169126695951209e+11L, 7.27110260029828078950026409435831711e+11L, 1.65049955237878037840731169761272102e+12L, 3.84133814950880391685509371196326109e+12L, 9.17374426778517657484962791255187134e+12L, 2.24990194635751997880832549111511175e+13L, 5.67153508990061173097292032404781538e+13L, 1.47074225030769701852963232235912597e+14L, 3.92701251846431177545392497147427712e+14L, 1.08063997739121282020832802862957248e+15L, 3.06767146672047518894275739534732971e+15L, 8.99238678919832842771174638355746631e+15L, 2.72472253652459211109041460799535823e+16L, 8.54294612226338925817358060817490416e+16L, 2.77461371872557475533476908014408616e+17L, 9.34529947938202912146296976663431863e+17L, 3.26799612298773188163350554315742192e+18L, 1.18791443345546831451732293519928714e+19L, 4.49405340841856421397062311314705655e+19L, 1.77170665219548674305185389795833488e+20L, 7.28810255288593152697603764235601397e+20L, 3.13251243081662534865188632436805529e+21L, 1.40874376795107311038838007000777184e+22L, 6.63829426823606041441317767891166205e+22L, 3.28254360840356501289107248021554584e+23L, 1.70592009803839406409667145645666005e+24L, 9.33225938514852428454935743602712728e+24L, 5.38272717587488831207327363463407970e+25L, 3.27895423512209324910006662705296242e+26L, 2.11319169795745809927716305122705952e+27L, 1.44341104149964304009687846325713305e+28L, 1.04686439465498242316197235200069352e+29L, 8.07731922695890570023291958025986094e+29L, 6.64314696343261627707440459400107155e+30L, 5.83567012135998626006817430636714368e+31L, 5.48689029679023079776199817150941362e+32L, 5.53372696850826161418229677716330718e+33L, 5.99973499641835283449509393028972981e+34L, 7.00917611946612256941556198729562373e+35L, 8.84406196642459749869759626748371565e+36L, 1.20822686086960596115573575744502341e+38L, 1.79164851431106333799499432947126299e+39L, 2.89131391671320576216903362266762013e+40L, 5.09145786021152729801159516043566174e+41L, 9.81063058840249655325327952160218101e+42L, 2.07444123914737886022245738089968362e+44L, 4.82765011693770054022189143000576390e+45L, 1.24028793911154902904261886940649724e+47L, 3.52878285864478461616951002487254771e+48L, 1.11544949047169665881036414807872651e+50L, 3.93051064332819631369914292573124112e+51L, 1.54924371295785233686867649142553059e+53L, 6.85499823804130100151231679166543611e+54L, 3.41747996158320770357877327002949632e+56L, 1.92690549864107998957222500013304086e+58L, 1.23358096300491944958557136475962190e+60L, 9.00281990289807691528078151573158069e+61L, 7.52141514125344164522463463202808470e+63L, 7.22427755490057899279344696654950663e+65L, 8.01283283053507860974456727997488253e+67L, 1.03099962028638036919997632019550562e+70L, 1.54617495707674867923983623892142209e+72L, 2.71580377261324869371341160824868908e+74L, 5.61508992057174643750226165228505259e+76L, 1.37366785934534333692942668275259398e+79L, 3.99754102076962512613158938364235073e+81L, 1.39150058933980008745266389466237314e+84L, 5.82669384491202289249159160687839973e+86L, 2.95227482092954909582266634062763990e+89L, 1.82102306147846628152669685943631657e+92L, 1.37597302213794152564743989887492097e+95L, 1.28185236754341294523601004808033157e+98L, 1.48213012720199050337233086306566580e+101L, 2.14157427379243531419232668034147566e+104L, 3.89449554094711237984570195229015661e+107L, 8.97864636258010296104790318951217808e+110L, 2.64413158980724405044777067425347595e+114L, 1.00240353984191383430257305957041439e+118L, 4.93141280490390525871801097705197944e+121L, 3.17440111243586504420672164931106998e+125L, 2.69662400176189239027511019786185589e+129L, 3.04979932232044716644635827452454425e+133L, 4.63404152681868778475392087605038845e+137L, 9.54898313480310651224157422560754643e+141L, 2.69440486619208982853089677098686964e+146L, 1.05150272003639532507210605903884076e+151L, 5.73417064062624495509840837865743050e+155L, 4.41627650777870044428827641421915327e+160L, 4.85653500442164862297918118887566388e+165L, 7.71243776020172406235461422008761914e+170L, 1.78944284768135145352387398006192815e+176L, 6.13948504948193852500464630902178966e+181L, 3.15374719244414866832063986813364807e+187L, 2.45678229633130089072342575625791640e+193L, 2.94097956743157560377748279040964662e+199L, 5.48435772238022826264635788020315114e+205L, 1.61576755939787162649511106856450099e+212L, 7.63055674811964328822328719215905762e+218L, 5.86357963426750903008317058039636736e+225L, 7.44578009523250679265610908767109796e+232L, 1.58753358925950887380498944645468602e+240L, 5.77757179750005470017336807397418313e+247L, 3.65046885264085030960970127282511879e+255L, 4.07509702021276189043935129812053895e+263L, 8.18389073292535025618266801702554839e+271L, 3.01238089652162618211263051563523370e+280L, 2.07176624104483264053704912869659325e+289L, 2.71562735784398218302697011744202385e+298L, 6.92451661016398143622710849660362520e+307L, 3.50810155650162047040906671177815417e+317L, 3.60896781300246599975888048597671985e+327L, 7.71058277826701049982730585006218034e+337L, 3.50154660288711246272389096490054302e+348L, 3.46175331564137723439223486579386975e+359L, 7.63696466254316472130052183903629062e+370L, 3.85655304044390258996784681505914035e+382L, 4.57665692577932081843759029527968784e+394L, 1.31143566628034307965133274338127595e+407L, 9.33142940209453779649608781259673613e+419L, 1.69703443400488874615229289912465401e+433L, 8.12664327627950884948530966790210152e+446L, 1.05671342182667061564323377987059929e+461L, 3.85123350151333031094457679550412807e+475L, 4.06487630836024540948583469098247829e+490L, 1.28516517984503545753808504263016648e+506L, 1.26025754690341684764700072541766676e+522L, 3.97331612896914344554453503890576277e+538L, 4.17965516524315248567041014799432863e+555L, 1.52416658652941127494202186163265994e+573L, 2.00432202283496263048589917253126663e+591L, 9.89983817930114056039301724151237036e+609L, 1.91539340453423886973992445388091844e+629L, 1.51593315523153267229971976448703684e+649L, 5.13233111267478387648166311677061530e+669L, 7.78392571224632702458474933380523830e+690L, 5.54632077048043201021342066119640603e+712L, 1.95012604607548018918112113273623192e+735L, 3.55940947870861006830654543397286882e+758L, 3.55349341454925685547195460273265331e+782L, 2.04795692045857243226580641802720017e+807L, 7.20348918974342452885439546130773017e+832L, 1.63778937282231874342883712446170710e+859L, 2.55384218509638087014670377175798088e+886L, 2.90332357089198897009653578594580665e+914L, 2.56300421336271050326091753908665515e+943L, 1.87504708602076913446948153935468528e+973L, 1.21573089114719501597325465689193734e+1004L, 7.48693407429699486755736805187051392e+1035L, 4.70376397570597947605315058283235338e+1068L, 3.24549375035327607806235059706162609e+1102L, 2.65365798053210741189633750297123713e+1137L, 2.78113361306514574568406809189880354e+1173L, 4.05114001708803517429009145027606492e+1210L, 8.91647476482514121118371429882160487e+1248L, 3.23223851331898724884680382253593571e+1288L, 2.10925909168894371224120829576314259e+1329L, 2.71594389750881594498007515800169231e+1371L, 7.58558239286242519535157411695442827e+1414L, 5.06701669031717221741926320252846248e+1459L, 8.95314256637167381172114473249653533e+1505L, 4.64314397380853725563695225910483395e+1553L, 7.86772811149376784892131781209226509e+1602L, 4.86578559687970087801384706555207321e+1653L, 1.23116054769360594876926981962811083e+1706L, 1.43383897399728293235616506956599019e+1760L, 8.67960762728500385489074199138374599e+1815L, 3.09584788327503858182429306492345781e+1873L, 7.40514657822678853879551103162851562e+1932L, 1.35750288266752565171774149402837640e+1994L, 2.18886955807028168114167639122307268e+2057L, 3.57839966436010585653478204799948336e+2122L, 6.86791017358151132323155526430183469e+2189L, 1.80021943882415618419042799861559854e+2259L, 7.53312429569410546159363413753399067e+2330L, 5.91165553375547478649257686857639971e+2404L, },
      { 1.57096255099783261137673508918980377e+00L, 1.57229290236721196082451564636254803e+00L, 1.57495658191266675503402671533627186e+00L, 1.57895955363616398471678434379990522e+00L, 1.58431078956361430514416599492388597e+00L, 1.59102230111703510742216272394562613e+00L, 1.59910918118616033722590556649394763e+00L, 1.60858965710906746764504456185444654e+00L, 1.61948515482641974319339137784305225e+00L, 1.63182037453073931785527493516038124e+00L, 1.64562337819112567903033806653906069e+00L, 1.66092568939542410935608828998468834e+00L, 1.67776240601646371733138779357535047e+00L, 1.69617232627708297296989766202480515e+00L, 1.71619808886073246730768842066355199e+00L, 1.73788632779101456236627555096372654e+00L, 1.76128784288515241044102589887617560e+00L, 1.78645778667368642025315725404601102e+00L, 1.81345586877233558659749539967614045e+00L, 1.84234657879265254187280543490489739e+00L, 1.87319942898662752121993510591579920e+00L, 1.90608921793761261888076943857856180e+00L, 1.94109631673677945144587926354356120e+00L, 1.97830697922181656566949720098719647e+00L, 2.01781367800384433737712026561151498e+00L, 2.05971546817081389507270282494773392e+00L, 2.10411838073232749275747224342524909e+00L, 2.15113584806337555354918618566085438e+00L, 2.20088916381459141771044083611555369e+00L, 2.25350797998611420231160903572594743e+00L, 2.30913084411305337537709048855358397e+00L, 2.36790577978511333384857209921738492e+00L, 2.42999091402365295350102767412517845e+00L, 2.49555515536908558968587982458158759e+00L, 2.56477892689313451429557802920827974e+00L, 2.63785495874745168415081659796280588e+00L, 2.71498914529626806741705564771250802e+00L, 2.79640147236028053619149909690973720e+00L, 2.88232702062657870038560203490158574e+00L, 2.97301705186029380311201985278736098e+00L, 3.06874018519362823755119514588303156e+00L, 3.16978367147348738564623946178566521e+00L, 3.27645477442732860056345607892739085e+00L, 3.38908226826615609816107421136979984e+00L, 3.50801806229286913554992299494422526e+00L, 3.63363896413353027399263692498221540e+00L, 3.76634859436988420367634452674074607e+00L, 3.90657946663630928939225987850575005e+00L, 4.05479524866754112035397645627643742e+00L, 4.21149322136091780158052739625729631e+00L, 4.37720695466646221916071477112304019e+00L, 4.55250922105994638849140210916632132e+00L, 4.73801516951078282566987752649050466e+00L, 4.93438578525358788671420711795537458e+00L, 5.14233166333819107447595069798421539e+00L, 5.36261712689997622445803908396748884e+00L, 5.59606472439710019418213140693069575e+00L, 5.84356014374437330661446789126222075e+00L, 6.10605758538173469317727364122669467e+00L, 6.38458564090067143612732202429488542e+00L, 6.68025372897382444864907987044889317e+00L, 6.99425914605841270881961646990609410e+00L, 7.32789479574890106033548644768353338e+00L, 7.68255766782458876413667730681699443e+00L, 8.05975814607113727007240055318338450e+00L, 8.46113023296234288939356989479671723e+00L, 8.88844278939567108027044653070693778e+00L, 9.34361189902548515485066576358196470e+00L, 9.82871447949462202212030994384949241e+00L, 1.03460032772138062549383568058709409e+01L, 1.08979233984912291622835480030167114e+01L, 1.14871305480132578973614919602781370e+01L, 1.21165111661978855517781913327317255e+01L, 1.27892046801009632078942555615288463e+01L, 1.35086281087128109623831457304510920e+01L, 1.42785032930533442126932924888187804e+01L, 1.51028870549318132669071327511660921e+01L, 1.59862046261270319640752848374642080e+01L, 1.69332867326908112807352382039426796e+01L, 1.79494107678000050622926329104204826e+01L, 1.90403465419082315888820165929975494e+01L, 2.02124071618296433363312214170232623e+01L, 2.14725056619224736964251610816575600e+01L, 2.28282180919971350546248653847563966e+01L, 2.42878538594168042463845071817020910e+01L, 2.58605342287811778487151334440784950e+01L, 2.75562800035467442565131080245429614e+01L, 2.93861095522110956389210768876799587e+01L, 3.13621484999095132865600012683857137e+01L, 3.34977525874991258153646142408367963e+01L, 3.58076454079962546803810835720597110e+01L, 3.83080729687253016658377309622401176e+01L, 4.10169773015547344709659456503154667e+01L, 4.39541916587611362308342994483809472e+01L, 4.71416601949419692729062784327679986e+01L, 5.06036854536665922553724504470269960e+01L, 5.43672074601944525232284101611170030e+01L, 5.84621187791213843904071404608607478e+01L, 6.29216205405812878410372213510070276e+01L, 6.77826251851241666263837617897404532e+01L, 7.30862125426522301461255132629846162e+01L, 7.88781468648814729209715096907821639e+01L, 8.52094635973465833426799692679733796e+01L, 9.21371360338777471668559829773781201e+01L, 9.97248335767075464896305315099053068e+01L, 1.08043785167904642576949987865910954e+02L, 1.17173763608862169247776700625586421e+02L, 1.27204208998868737227575356110541727e+02L, 1.38235512466410237338282474486031692e+02L, 1.50380484815148331050381123359927804e+02L, 1.63766038752610274212021042234298836e+02L, 1.78535118123338340289613569013808793e+02L, 1.94848913160728060357302969254459805e+02L, 2.12889407359835266977366307026062112e+02L, 2.32862309344799079018746113493039633e+02L, 2.55000432284328199435640048881527463e+02L, 2.79567594267244578195192766207761694e+02L, 3.06863125912428093434496643114651412e+02L, 3.37227086745120087399301385427960651e+02L, 3.71046309996557625549265358225650347e+02L, 4.08761417046617491055856040237506316e+02L, 4.50874968419459367009632339064163929e+02L, 4.97960948895977349122916983180817637e+02L, 5.50675820938578587679472462796822793e+02L, 6.09771424466317909206822143572649707e+02L, 6.76110053572647368547479902312230309e+02L, 7.50682103874142244645724064576848341e+02L, 8.34626760051808119187547981730818179e+02L, 9.29256284531554199823656475151338963e+02L, 1.03608457849823472804207172846963877e+03L, 1.15686081966189765730466332807313547e+03L, 1.29360914245380859999201252074469995e+03L, 1.44867552185420514387789438088656434e+03L, 1.62478325953219761549580736728832563e+03L, 1.82509875991531856022535679631713756e+03L, 2.05330963597261755441688599655848892e+03L, 2.31371761449477720025881552764731872e+03L, 2.61134923664018699944734187657483567e+03L, 2.95208799409362429898911389656789101e+03L, 3.34283233256054817982471990245472659e+03L, 3.79168492775659509883214798303432158e+03L, 4.30817983871631895490690508451026040e+03L, 4.90355562457020167332670064217006554e+03L, 5.59108434363481145162671079876733528e+03L, 6.38646862557124634146730890858054275e+03L, 7.30832182941297944038809179971510199e+03L, 8.37874981279970356111509048453001571e+03L, 9.62405721874963805881171453111047092e+03L, 1.10756066619114600841076275282388063e+04L, 1.27708660544590438787794590107117849e+04L, 1.47546879201948945213025955616435132e+04L, 1.70808753741706634268551294101897318e+04L, 1.98141030969548505096681178137364270e+04L, 2.30322788820475490754732581365999861e+04L, 2.68294531792863253502182123460963590e+04L, 3.13194117839842820030641964449539820e+04L, 3.66401220970699799668192565829190288e+04L, 4.29592483666869017028607105165763804e+04L, 5.04810088263984357248209974578040223e+04L, 5.94547213318005529011388227962133751e+04L, 7.01854787517268957919092596317475529e+04L, 8.30475172617569400265716546608431303e+04L, 9.85009980505357544638949542794405193e+04L, 1.17113126626176606026864191754698095e+05L, 1.39584798216058984483938678946454092e+05L, 1.66784301639307755633312351999980104e+05L, 1.99790062652052468605951710457597772e+05L, 2.39944994603299218686253514351518948e+05L, 2.88925793983801323167992289814829493e+05L, 3.48831530919430454806103930858415528e+05L, 4.22297220149677844682618484564386312e+05L, 5.12639824636925361908056913471196846e+05L, 6.24046487622198979196137566922551170e+05L, 7.61817907323361594136262168936978923e+05L, 9.32683930022411925704630601110030182e+05L, 1.14521400777429753902355978052778602e+06L, 1.41035264627423311876298925540665884e+06L, 1.74212004187586338510201667214170577e+06L, 2.15853171693428701405141183541166264e+06L, 2.68280941012642673073659887232388938e+06L, 3.34498056359541886091597912499527306e+06L, 4.18399797233770604788689812330719069e+06L, 5.25055800816550175243256559083639418e+06L, 6.61086017414168098803951872291131863e+06L, 8.35163942396755869310534556270941378e+06L, 1.05869253239392990034806970327887878e+07L, 1.34671523510623940861241599892229316e+07L, 1.71914827102426302145958883698307763e+07L, 2.20245344902770169422620623417295079e+07L, 2.83191730172433779681268016861978467e+07L, 3.65476782026834493187990343694010282e+07L, 4.73445265723062610640363034466641760e+07L, 6.15653406350951387288804933390908042e+07L, 8.03684302689786924828741611740519293e+07L, 1.05328028435969028854761469747289545e+08L, 1.38592168908412628618546923035162247e+08L, 1.83103698592568352374304770508191971e+08L, 2.42910945745864082034287213456845086e+08L, 3.23606239375966746282831194186614169e+08L, 4.32947521859998666323053129499091577e+08L, 5.81743296796292947920442886775592966e+08L, 7.85117978938819178602650773642870262e+08L, 1.06432919762707530726575966438229782e+09L, 1.44938958291294548467516226794019366e+09L, 1.98286646937799184909776706883985509e+09L, 2.72541431469809432350206616813747393e+09L, 3.76386796411162144400474070771015333e+09L, 5.22313881495099093715899688078276720e+09L, 7.28378581064439770444275729738468286e+09L, 1.02080964238115874250636198090464363e+10L, 1.43789931847051052135186583023967432e+10L, 2.03583681254363357780032810961889254e+10L, 2.89749982708002744366825785538936402e+10L, 4.14577375164549487752492409798515416e+10L, 5.96383768387242628650336526594122215e+10L, 8.62622848391553079951805623395652598e+10L, 1.25466704538982518000690409655705216e+11L, 1.83521298226491318558080545133554232e+11L, 2.69981220740015160361114268684563366e+11L, 3.99492845215192295441294253418780644e+11L, 5.94638055870143455023933020703444642e+11L, 8.90440996742409110650533033931653292e+11L, 1.34155194167777583831947241717631506e+12L, 2.03376855033215189170105222958089604e+12L, 3.10262795987575321360135087196684567e+12L, 4.76359832170586206290082815387968037e+12L, 7.36142036056081358397678626842794083e+12L, 1.14512696145655742338758840735549183e+13L, 1.79331418699627392634462052642542749e+13L, 2.82758550128579223200239017529280909e+13L, 4.48929705367844466861372765075631687e+13L, 7.17780287265849957064212371548037347e+13L, 1.15585509854582062520354852997620279e+14L, 1.87483388636788309288478433629583664e+14L, 3.06351035640217445409310567498133847e+14L, 5.04340065300597024230361727189343426e+14L, 8.36616339689242989007881200440488764e+14L, 1.39855635164094728875364679627485304e+15L, 2.35633574951616468243164849507855064e+15L, 4.00176516738263745645278590345795707e+15L, 6.85137512840494144543571512545601884e+15L, 1.18269011176154399046326619510431010e+16L, 2.05867352701380644281110910622942185e+16L, 3.61396878431490463311666873476678412e+16L, 6.39911218439421355095519025524482256e+16L, 1.14301618562837692261569180960886276e+17L, 2.05988138391566644299797673070467922e+17L, 3.74584678835368091393630059068193045e+17L, 6.87444303468314906803024757565005462e+17L, 1.27340764361348531366853034790770231e+18L, 2.38124191682989536626992792404294190e+18L, 4.49583561730710839927340662784958898e+18L, 8.57144202490195270096830399067728169e+18L, 1.65044358418165696532477771893166700e+19L, 3.21010035242131785085169993033188229e+19L, 6.30778012444270309076492866733769742e+19L, 1.25240403115766127899628450500249765e+20L, 2.51300529564998539443832117224536420e+20L, 5.09677625569083843571268035202853929e+20L, 1.04501920001667304566512216455267827e+21L, 2.16647647926087846601520265828431353e+21L, 4.54213814567839546278770815494416868e+21L, 9.63208232444913712819259248595549405e+21L, 2.06638653668825452816630915727527426e+22L, 4.48552978555442825059406438230470388e+22L, 9.85387957361097750825498509227133701e+22L, 2.19115887446437440814640517501285189e+23L, 4.93283596439097166796547625560314132e+23L, 1.12450152997177436346173556893175508e+24L, 2.59626913615675600812300017445220112e+24L, 6.07229293831362550112108555687653017e+24L, 1.43898906630800383562329122001513450e+25L, 3.45584195640657046905140678735606870e+25L, 8.41265519171357648977229820858109463e+25L, 2.07628906165081651016090959669418757e+26L, 5.19651502464022032237151119613068945e+26L, 1.31917319408964404296057699402007921e+27L, 3.39745589598038079361346075350889747e+27L, 8.87905745443850359109225209251728000e+27L, 2.35527236149206412607849676379867126e+28L, 6.34276200772262482389475687836384423e+28L, 1.73453109399085970484897533524593455e+29L, 4.81789317060683087119323058524624972e+29L, 1.35959734649014823198654051259347560e+30L, 3.89896968990650039174705544740914822e+30L, 1.13654298652998993600898528562905634e+31L, 3.36845004399178001650511659074612092e+31L, 1.01530408470981725989945294876828360e+32L, 3.11314437622191823730222798219255685e+32L, 9.71307273973014040273869048577801862e+32L, 3.08451764358172594571945077912559223e+33L, 9.97268213982049728423469082288644341e+33L, 3.28362505228849158612675319471610094e+34L, 1.10137878539082753552686700652535380e+35L, 3.76433336759271429732220889611944227e+35L, 1.31140346593824292621577115225698644e+36L, 4.65813571068281367213354863266427946e+36L, 1.68751734747051139203189162215633629e+37L, 6.23705368501832349017363870039959902e+37L, 2.35257131442774486893301429598817553e+38L, 9.05893824021969993626817980294413695e+38L, 3.56224909761113607077591549609142868e+39L, 1.43095929157855820977839447284784020e+40L, 5.87397458498437504922632656680942932e+40L, 2.46482854981128378684502286098690220e+41L, 1.05764920309085562822396787743332401e+42L, 4.64247563928107803530506772359734012e+42L, 2.08528711827242177927156523287132065e+43L, 9.58843998518663217709277358040461543e+43L, 4.51498201124609227956079072858258677e+44L, 2.17797404834197320411588567916752909e+45L, 1.07672097682290045825361349736107198e+46L, 5.45726743292908558875367244655615249e+46L, 2.83686927045578113375020568154351117e+47L, 1.51310320139201162564313806536303971e+48L, 8.28397466722561707458255405781541923e+48L, 4.65723949199597134403065145457979764e+49L, 2.68979637071283693718739500357715726e+50L, 1.59659784691197038762901224386099403e+51L, 9.74415453825658662874112107295166701e+51L, 6.11723839484331306452493250946377612e+52L, 3.95204965058524182677586041680204451e+53L, 2.62870159207425821306145211826683375e+54L, 1.80099019650267939321809025642439880e+55L, 1.27155446256306838318997486480890087e+56L, 9.25588010447776071059464920928086632e+56L, 6.94973792013391939342588109222317053e+57L, 5.38516720076996562080859176422021036e+58L, 4.30849366810297877444039522534393308e+59L, 3.56095155754217837059656555899190815e+60L, 3.04188852838464999204043663335571874e+61L, 2.68709444193083718922293037351168007e+62L, 2.45592053890000085507656649738841713e+63L, 2.32364825416864153745578539849845717e+64L, 2.27712974158489233058096874867974690e+65L, 2.31263355291322473374027008502422191e+66L, 2.43540759298129112939388619780518297e+67L, 2.66091038882246524570905302589811279e+68L, 3.01810594342353392025458974267831671e+69L, 3.55582348951019250289525994888733340e+70L, 4.35418887779384901315251041444022899e+71L, 5.54497579551181331524599144834616000e+72L, 7.34827648190988633565298908145357523e+73L, 1.01399802572242326074394064208241565e+75L, 1.45791146224460794340779409761344050e+76L, 2.18548887681950529537081926891986328e+77L, 3.41802215328662300798456454917771592e+78L, 5.58084392060183572830239216435019125e+79L, 9.51958650279973390758331753317257950e+80L, 1.69757357824719778562747265505252946e+82L, 3.16690667099018001388115076193097403e+83L, 6.18509910641867543038652223056425804e+84L, 1.26554113438693437654962757714903554e+86L, 2.71482896587775689871692274985919286e+87L, 6.11038680296449408162222719197467046e+88L, 1.44405408617108323852658132530082074e+90L, 3.58608372663838816495703910754396082e+91L, 9.36523186806323959966772742491929522e+92L, 2.57408011620512244881601880029319908e+94L, 7.45213468986230271920125476429174422e+95L, 2.27430990383616981910627355587444817e+97L, 7.32301113412116474943463116371095103e+98L, 2.48981642173793246167799170730006141e+100L, 8.94653338635928158843209476986997508e+101L, 3.40040137239116597862336436061445989e+103L, 1.36828818620892821730981225660157834e+105L, 5.83427748982959193060571787534606697e+106L, 2.63848693767238342424340754241860221e+108L, 1.26672888276713952132156406152434700e+110L, 6.46222517831418280306046165038174027e+111L, 3.50643232060757360375065377104810475e+113L, 2.02560893394326816509670349356333828e+115L, 1.24704167708478470702273287091045609e+117L, 8.18986518840527903795498196049278255e+118L, 5.74361089440609996487936977642920132e+120L, 4.30580893408448976261136511421423734e+122L, 3.45415696607949675499703108821962730e+124L, 2.96831660153035273688363706593385374e+126L, 2.73545624237218359223636851390580041e+128L, 2.70631717669007784745026117275242499e+130L, 2.87767991634206038473084413615742294e+132L, 3.29241287826810639047033778585737812e+134L, 4.05784096195372596931902665934287382e+136L, 5.39378304910573732382585500687737807e+138L, 7.74152390167223540621293648465491842e+140L, 1.20120996231066845642412985516875175e+143L, 2.01745607955680730064740044686640147e+145L, 3.67217662348306252636962893865111425e+147L, 7.25316379805857762968710347450256630e+149L, 1.55659153530257056999948366173645285e+152L, 3.63439983279039488510674235872123571e+154L, 9.24438760046827764031744167566698413e+156L, 2.56505460126015154661078513762260065e+159L, 7.77468767447849521051212939671796074e+161L, 2.57775340952739965069105894604883404e+164L, 9.36236559200171990439286451934391539e+166L, 3.73025928974609135812613567068392186e+169L, 1.63280699411172483017448754437467171e+172L, 7.86350685466830069681739337700194312e+174L, 4.17288659053103260909421860789088638e+177L, 2.44376468354452976432259429734905692e+180L, 1.58182604983516221850901239956537929e+183L, 1.13349408906485996833819126879692168e+186L, 9.00609317867158059779746161516574861e+188L, 7.94724581006520631483760616875503191e+191L, 7.80148734070770210787180909072341250e+194L, 8.53389980133319877537947288273508168e+197L, 1.04199907704816359699116619452657846e+201L, 1.42261945983107616739713958471131412e+204L, 2.17558254343347922345039318825837040e+207L, 3.73339221107050141050095672544726852e+210L, 7.20212971416878494124587287789857735e+213L, 1.56476087980246841023430707631510252e+217L, 3.83599289170088247519036274601375492e+220L, 1.06310518934360483187879118646180568e+224L, 3.33719954606082150504485397744546787e+227L, 1.18890644129926367056386614030943443e+231L, 4.81657405194041613555936914144916344e+234L, 2.22347874123114616622582251703709887e+238L, 1.17199258523361213934517364652872480e+242L, 7.06839671131322838482398929154588609e+245L, 4.88812662946732326430886472870070228e+249L, 3.88441192207630934518298772235960268e+253L, 3.55483921979499015020713995580889030e+257L, 3.75484510816250928148708038190395303e+261L, 4.58798720933643387311573482445886110e+265L, 6.49989966050568126079524541048529496e+269L, 1.07018103272413666142402906179157598e+274L, 2.05258799808849457042833394124187317e+278L, 4.59710306190259625117029822247574448e+282L, 1.20521640888055636836192268959955157e+287L, 3.70784262206985650361726040790594581e+291L, 1.34198324051735290325452259089861845e+296L, 5.72866922601176092550850127280325304e+300L, 2.89181208605318627652310091586335359e+305L, 1.73078309894901278503683645288680165e+310L, 1.23150483936231719457381379774516673e+315L, 1.04456022524616506171048138618772005e+320L, 1.05909848234544418123752752792015094e+325L, 1.28725694153849592436889057738754649e+330L, 1.88087499295689639308903342601320607e+335L, 3.31344446614783992260480183208324732e+340L, 7.05835740270200829603933852702222207e+345L, 1.82360609116987101874149506325271866e+351L, 5.73167527316999715111499624522524490e+356L, 2.19834460849656433024448179756162490e+362L, 1.03213196012299389398122351217909554e+368L, 5.95090459142945173642877532664315820e+373L, 4.22711326689560391486862730288730260e+379L, 3.71146073815998244096864189178283440e+385L, 4.04143285030597516778349567433491877e+391L, 5.47630493200706306950643242188138639e+397L, 9.26610183846830899934754118194991047e+403L, 1.96463867218278875638951764233831097e+410L, 5.23826401399289488725708314794013273e+416L, 1.76269979359229195206462702562496087e+423L, 7.51358974574222029659643352716721030e+429L, 4.07203690042780566335825621191079282e+436L, 2.81652392485993763998682010256080428e+443L, 2.49586432083962995754217338958145192e+450L, 2.84464997550187838898841424132533307e+457L, 4.18656911271834268911740204159815337e+464L, 7.98835944729121866190974499356597568e+471L, 1.98427885725374523364086923738598523e+479L, 6.44313780728812316513345459942694301e+486L, 2.74646447436726496994650106679472135e+494L, 1.54345331097536381511922890966406624e+502L, 1.14854061177091306735355343708426610e+510L, 1.13671799925771100929831048483235430e+518L, 1.50301408173820906903238314130252343e+526L, 2.66721742401349021977265127553699936e+534L, 6.38191727827371990119668697526910696e+542L, 2.06864189323929509313332139375745418e+551L, 9.12718925758572793769909253655546363e+559L, 5.50827087084343716443556503345322384e+568L, 4.56943096951975523591483538202467049e+577L, 5.23664812769936727945516120554813767e+586L, 8.33295831969513770916127078276468196e+595L, 1.85073603040006635508770638738735213e+605L, 5.76725912854578779706265298662279369e+614L, 2.53507221317613667093309510235117825e+624L, 1.58037348241924263546145926074674624e+634L, 1.40497098317887844492262106048698980e+644L, 1.79118532756424559114150403858023467e+654L, 3.29340233091271285034548969456076701e+664L, 8.78384225704905897945003526718634352e+674L, 3.41824790512159692619674889187554870e+685L, 1.95247619547378795632466874007982924e+696L, 1.64685615123975037569921507294781182e+707L, 2.06385512057306929298128483493412318e+718L, 3.86691162964076111860523245198584961e+729L, 1.09008736553561747232882422664193409e+741L, 4.65333391196364819411904308657257877e+752L, 3.02768585771015415481330091322447912e+764L, 3.02262040534180824988954474846992913e+776L, 4.66133722640174567906974646266839244e+788L, 1.11806119156969171229108916233644999e+801L, 4.20019655376202192049844032418033555e+813L, 2.48880615873773868126893696694455625e+826L, 2.34286193142054643224145247339492426e+839L, 3.52941162196327496812531706159176164e+852L, 8.57183106082165907783781027353105401e+865L, 3.38163627323872188047900717079347979e+879L, 2.18363447282104345585022834572887531e+893L, 2.32596275137292895223293068081362090e+907L, 4.11925247272574761366980289309686003e+921L, 1.22265489460258041690738897372130823e+936L, 6.13184165651799059701146847592463464e+950L, 5.23922303275545560334122760478493484e+965L, 7.69087233862553980667730597006685190e+980L, 1.95622275953749870274795206909257068e+996L, 8.69670052895401378378113763338699958e+1011L, 6.81715148318539616903616353647181598e+1027L, 9.50697711954141544724599553989093262e+1043L, 2.38019733682587370869498352841844428e+1060L, 1.07973324919847346937291516197684901e+1077L, 8.95814284819025403820872820042945790e+1093L, 1.37229192661307043018958002675397831e+1111L, 3.91918794622242371528673544888524419e+1128L, 2.10730218913863433412315532873510194e+1146L, 2.15459607934366008026324206138837528e+1164L, 4.23163839295623007237154131840451677e+1182L, 1.61294441956204958952970453918741185e+1201L, 1.20568335528354973250479511763057854e+1220L, 1.78630819666459637235669417819650103e+1239L, 5.30233997012953850434143324467683139e+1258L, 3.18800705045665419646272046839479382e+1278L, 3.92589619613994835597705628928304867e+1298L, 1.00145056975313260959947278741812233e+1319L, 5.35268808912615454171856125906228237e+1339L, 6.06491960606218539467705074145131193e+1360L, 1.47410187506507259371724909079906759e+1382L, 7.77855372825975976779550291054389078e+1403L, 9.02071339187528665617085297107535931e+1425L, 2.32776342394968427168699373890370933e+1448L, 1.35351409374730203846799381939288903e+1471L, 1.79625881886715571166142200348072039e+1494L, 5.51189494978629938318487744451579809e+1517L, 3.96270198063441303687686251379779681e+1541L, 6.76492837567767494547614386605795924e+1565L, 2.77991073110506719318274536923711255e+1590L, 2.78805951202715437087943440518839779e+1615L, 6.92116280606414755524296394791409952e+1640L, 4.31380309053366092943951122004431463e+1666L, 6.84923073262139995438889690748178280e+1692L, 2.81137418164430829179092468240459910e+1719L, 3.02821524237655652451836921974619657e+1746L, 8.69048903533522694231765189726868666e+1773L, 6.74828063313306417588175763238310325e+1801L, 1.44026065844141159256872926595644247e+1830L, 8.58426091495439408178371839859019039e+1858L, 1.45211919400936944187225846047638548e+1888L, 7.08718013380970011842483247147601395e+1917L, 1.01475962220405902523641440040104610e+1948L, 4.33542978691578087509838132034723750e+1978L, 5.62285772227295495777726116212032065e+2009L, 2.25285072921565445630031609130087435e+2041L, 2.83839050906274298341210206309759282e+2073L, 1.14501659579022194588844609345877849e+2106L, 1.50629562239998305958408533546634030e+2139L, 6.58342161306998815751663631350733541e+2172L, 9.74198999952210922892973281379349494e+2206L, 4.97552704772088889228035264268694867e+2241L, 8.94330619882842342247706838649878784e+2276L, 5.77072420156026800834371478648673792e+2312L, 1.36388225285320494361282201451878478e+2349L, 1.20508189950290985406298094670730466e+2386L, 4.06413362486490272506766749982212198e+2423L, 5.34308092015215251528601382597439463e+2461L, },
   };
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   m_committed_refinements = static_cast<boost::math::detail::atomic_unsigned_integer_type>(m_weights.size() - 1);
#else
   m_committed_refinements = m_weights.size() - 1;
#endif
   m_t_max = 8.88600744303961370002819023592353264e+00L;
   if (m_max_refinements >= m_abscissas.size())
   {
      m_abscissas.resize(m_max_refinements + 1);
      m_weights.resize(m_max_refinements + 1);
   }
   else
   {
      m_max_refinements = m_abscissas.size() - 1;
   }
}
#endif
#ifdef BOOST_HAS_FLOAT128
template<class Real, class Policy>
void sinh_sinh_detail<Real, Policy>::init(const std::integral_constant<int, 4>&)
{
   m_abscissas = {
      { 3.08828741797632286606397498241221385e+00Q, 1.48993184649209158013612709175719825e+02Q, 3.41228924788343710247727162226946917e+06Q, 2.06932576604261779073902718911207249e+18Q, 2.08700240760947556038306635808129743e+50Q, 2.01976616071790815078008252209994199e+137Q, 5.67213444764437168603513205349222232e+373Q, 3.06198394306784061113736467298565948e+1016Q, },
      { 9.13048762637669674838078869945948356e-01Q, 1.41578929466281159169215570833490092e+01Q, 6.70421551622327648231120321610008518e+03Q, 9.64172532715049941526010563818587232e+10Q, 2.50895076008577848514087028569888161e+30Q, 1.44726353571033714499079379626715686e+83Q, 3.75263401205045334128277886549019357e+226Q, 2.57846123932685341261715758547064293e+616Q, 1.25169402230987931584130068460134989e+1676Q, },
      { 4.07297690065758690150573950473604675e-01Q, 1.68206670702114874332932330092838356e+00Q, 6.15089798638672951539668935798437533e+00Q, 4.00396235192940022205839866641270319e+01Q, 7.92920024793102632052902471550336177e+02Q, 1.02984971333097958319686053784919407e+05Q, 3.03862310925243857437932941891415841e+08Q, 1.56544547436249486913719231527597560e+14Q, 4.04246509843021910355659662715183671e+23Q, 1.32170682742965817915034577861235933e+39Q, 4.99123178209955799774620736137366103e+64Q, 7.35294385035987596594501676609562008e+106Q, 2.45145417229813114714431821340237112e+176Q, 1.03030479197459267961759023741376327e+291Q, 9.88478945193556730604342445576518136e+479Q, 3.74498116076433060427765453233124618e+791Q, 1.90292390713026708045766158039087796e+1305Q, 1.72712488231809244721915842027236657e+2152Q, },
      { 1.98135272251478172615235718398538323e-01Q, 6.40155673500526017670785720911319502e-01Q, 1.24892869825397766254802900819319987e+00Q, 2.26608084094432123181038001270985037e+00Q, 4.29646269670232738148137717958679987e+00Q, 9.13029038709995569641811827045549232e+00Q, 2.31110765386427993316995139635470937e+01Q, 7.42770603432401243012214481410240677e+01Q, 3.26720920711525891697790966796945063e+02Q, 2.15948569431181871552028449867301828e+03Q, 2.41501526289641306037808670196966696e+04Q, 5.31819400275692915826269282626844010e+05Q, 2.80058685721704332307817790755562305e+07Q, 4.52406507979433877971977526863358905e+09Q, 3.08561257398067712180174566763237112e+12Q, 1.33882673301580747789133427708622972e+16Q, 6.25461717656234138147247754416652254e+20Q, 6.18209853581416475394863999233554548e+26Q, 3.07729364978845806686511011060697837e+34Q, 2.34895728937010430290698332595743680e+44Q, 1.14854319789946975790127332653552673e+57Q, 2.25530007001006986846545694404767295e+73Q, 1.87791950056919539419801461538703336e+94Q, 1.36747388793862427976781689638282239e+121Q, 4.24212177246407592514720294750151286e+155Q, 8.23473099012134325391170623028607158e+199Q, 6.06134211888406631001019923425936351e+256Q, 6.32519197436029413652766295658658263e+329Q, 3.61942292928205643948383705201563065e+423Q, 8.82464433126646489632943769283758364e+543Q, 3.35512488018651738642854323317447493e+698Q, 1.02411434678684822400261582079656500e+897Q, 7.40716670979374017620610271197945784e+1151Q, 1.30455147283895162835186634827124464e+1479Q, 2.02948723345659016173510134582487641e+1899Q, 6.99019443250345564805134094483457477e+2438Q, },
      { 9.83967894006732033862249554537934739e-02Q, 3.00605617659955035122465185012715805e-01Q, 5.19857978994938490000644785900799951e-01Q, 7.70362083298887700858583505753306721e-01Q, 1.07131136964131183026831222500748063e+00Q, 1.45056975808899844502126797109703210e+00Q, 1.95077854952036033400296546339240312e+00Q, 2.64003177369555146770080103921424631e+00Q, 3.63137237366741227331951933364601985e+00Q, 5.11991533090335057003526222543242753e+00Q, 7.45666098140488328926087222725984231e+00Q, 1.13022612688997262443461342203136009e+01Q, 1.79641069247277254966592982669611020e+01Q, 3.01781070460189822236726995418068214e+01Q, 5.40387580031237056709826662540066860e+01Q, 1.04107731447746954767459980678311109e+02Q, 2.18029520120262807725988909352359830e+02Q, 5.02155698625910164594509258651311707e+02Q, 1.28862131099822242045586488612672886e+03Q, 3.73921687080054832376623401153054636e+03Q, 1.24750729702019123239464970064060864e+04Q, 4.87639975322669212414357357061002683e+04Q, 2.28145658221913012154676335814417144e+05Q, 1.30877796006484301664040981436471740e+06Q, 9.46084663420966407698078336053299177e+06Q, 8.88883120363727962245667776649286152e+07Q, 1.12416882897434413447175648471746990e+09Q, 1.99127672953214446985509145592687205e+10Q, 5.16743469106098464964433058111638245e+11Q, 2.06721881420399088776240044545119948e+13Q, 1.35061503318410040604770421574064883e+15Q, 1.53854066283650818848773859278941683e+17Q, 3.29074729054035066052820102585085173e+19Q, 1.43729138188449881561963100200796631e+22Q, 1.40983244553034728562740932242727657e+25Q, 3.45913548027797144127990544649862792e+28Q, 2.39872058234095409213724024023509754e+32Q, 5.39880660461729295997086364918754801e+36Q, 4.61334000258062860955942270137550165e+41Q, 1.78768590966790245678294238939560892e+47Q, 3.84198437012433853552557148945213178e+53Q, 5.75279795570858370009870455748773410e+60Q, 7.77181203842728655149182334288749850e+68Q, 1.26967304420408162635234502672130357e+78Q, 3.49567677376573156773252547632961336e+88Q, 2.36251947497169244454694635287097330e+100Q, 6.00214389327365112307366927985587840e+113Q, 9.29071630346415553922675334522602561e+128Q, 1.51444223803384709004933819608345315e+146Q, 4.83290204410990250226310317342789471e+165Q, 6.09623012864338569215560315517200736e+187Q, 6.73889214277659359787962707085479315e+212Q, 1.60917893672694484647176104023542732e+241Q, 2.30724123468764987944582372785293125e+273Q, 6.32636439816284705814531208407868829e+309Q, 1.23274815890184688792156489065830724e+351Q, 7.55519778823055185901318833981345378e+397Q, 7.85730655106759111844772008129590573e+450Q, 9.36328400229780321059086898529742817e+510Q, 1.11332545426270395766246494603539938e+579Q, 1.53432069979625061288117473415446630e+656Q, 3.94622553764411915381186808199466267e+743Q, 4.41554290953392996069257034424439949e+842Q, 7.62037777854651720247173134262034607e+954Q, 1.15650294539918196985498353779647134e+1082Q, 1.50761161209705395018419027031866347e+1226Q, 3.03485088181756935935831645680716816e+1389Q, 3.38561189018537177501905611143498298e+1574Q, 1.64407968993192526511230001818530622e+1784Q, 6.63148743223242467667800355907041029e+2021Q, 1.15911606715877477373516464924821476e+2291Q, },
      { 4.91151003502902493004859475143401791e-02Q, 1.48013149674360733270682895306542340e-01Q, 2.48938813740683685679302156785267041e-01Q, 3.53325423692668437764413545980527251e-01Q, 4.62733556612235325924240102411897269e-01Q, 5.78912068164096306746325958452873475e-01Q, 7.03870253386062779930091622585657886e-01Q, 8.39965859144650568772715366992985538e-01Q, 9.90015066424437614655918533324528544e-01Q, 1.15743257014369913143250540144278056e+00Q, 1.34641275918536176274682426896283388e+00Q, 1.56216711390133555099139972670978661e+00Q, 1.81123885278232337973709010374295886e+00Q, 2.10192441900655030086142289233010995e+00Q, 2.44484388558419793403331221069953203e+00Q, 2.85372074663291502355627105440782037e+00Q, 3.34645891095535078662878051589922609e+00Q, 3.94664582105783838715586741669722603e+00Q, 4.68567310159667852940801410152900804e+00Q, 5.60576223090815117543658276010994803e+00Q, 6.76433233683057420359243855855175747e+00Q, 8.24038317537998522052755780107530197e+00Q, 1.01439435612985772971719760903679427e+01Q, 1.26302471433889247197016543768391586e+01Q, 1.59213039578034525767683420917805218e+01Q, 2.03392186192185718515730871595417962e+01Q, 2.63584644576063375239063530328964372e+01Q, 3.46892633322415240903243772580777748e+01Q, 4.64129146701972896279022543245987086e+01Q, 6.32055079389042420324823016892494512e+01Q, 8.77149726180890637368701810147417239e+01Q, 1.24209692624041149789460094889690346e+02Q, 1.79718634784512755704082576283626343e+02Q, 2.66081728332790018970422286819998479e+02Q, 4.03727302957571284052623415274320934e+02Q, 6.28811306654590870314912279241546792e+02Q, 1.00707983750749059403228978538051896e+03Q, 1.66156822918511428805794334715835287e+03Q, 2.82965144078658259775800007408843245e+03Q, 4.98438626658566913853681230634186899e+03Q, 9.10154692764781089316711776560031531e+03Q, 1.72689265547504972749956316341831556e+04Q, 3.41309957877860119013952370617496464e+04Q, 7.04566897705309280220071327032864763e+04Q, 1.52340421776127912819322591860346971e+05Q, 3.46047978289794741370038030428999755e+05Q, 8.28472420923318300234685980449600520e+05Q, 2.09759614660119394556971994518988102e+06Q, 5.63695079886127323617800640982012165e+06Q, 1.61407141085560724511058572293479599e+07Q, 4.94473067891506035994359725043521250e+07Q, 1.62781051682099135552732962095205423e+08Q, 5.78533297163228083837980097936281368e+08Q, 2.23083854068195568971189557384768608e+09Q, 9.38239130606473964277252886929412279e+09Q, 4.32814954477655169232818147143370442e+10Q, 2.20307274404924290439096300978395664e+11Q, 1.24524506710913641251205336694034299e+12Q, 7.86900053495782237478847410099854059e+12Q, 5.59953143297942246131549106648666176e+13Q, 4.52148694990209087674492643778610401e+14Q, 4.17688951654829326508268412199901397e+15Q, 4.45286775965049665585698503105745526e+16Q, 5.52914285314049806809138826588984682e+17Q, 8.07573251656285427526228838811700627e+18Q, 1.40204691626046869792238856080062664e+20Q, 2.92579141283223985009947325351094360e+21Q, 7.42643302933541088612408356755400686e+22Q, 2.32199633124573536432554419147246735e+24Q, 9.06419425063844243203436385576505269e+25Q, 4.48127904881944560890552322697008678e+27Q, 2.84904630472699064463968599282594753e+29Q, 2.36738115918335597454764956918757980e+31Q, 2.61582557845512122680388807973635504e+33Q, 3.91476494826329080795479762913295296e+35Q, 8.09204244855592921872201325412806040e+37Q, 2.35892132094063033238721339006551614e+40Q, 9.91521864853533259120934844876236731e+42Q, 6.15285105934265876352030730244956160e+45Q, 5.78027634014451538840307809528507880e+48Q, 8.44375173418648862568787172019022001e+51Q, 1.97334335089976670761175825761931164e+55Q, 7.60524737855621997973474323106432459e+58Q, 4.99205710493951041807363394610235299e+62Q, 5.77586342390391231642398949899839793e+66Q, 1.22180820194535560304052518248291191e+71Q, 4.91291723038713381630505551954597084e+75Q, 3.91397181373220237220120723831917341e+80Q, 6.45638806990528678730571952330861768e+85Q, 2.31122506852801035759561549878351498e+91Q, 1.88745815771943133919251977215527414e+97Q, 3.70848316543845309405007424631179900e+103Q, 1.85519881228353863491690263658048910e+110Q, 2.50978787317170531780713813087390423e+117Q, 9.79042375559121661656574862110333881e+124Q, 1.17908880794405074709159757136517906e+133Q, 4.71463184672247661988055185868884361e+141Q, 6.76265778595971324046101731037889126e+150Q, 3.77863792934416463055998231270977695e+160Q, 8.97819133628477274891556066769979914e+170Q, 9.95914407403494739885505580434362313e+181Q, 5.69630798679205432723450802096176313e+193Q, 1.86743452325707784871951519925288377e+206Q, 3.92719945859412568046939678789391943e+219Q, 5.97262802266570558434581771213406728e+233Q, 7.46293577265068378653739984957785219e+248Q, 8.77620026161886682323882402399676732e+264Q, 1.12240955327637524990782988952974327e+282Q, 1.82091452946462555573948654780251641e+300Q, 4.41446070956615785669448222555093749e+319Q, 1.90397541953366074155779318767572880e+340Q, 1.75902281129585020130874703618203168e+362Q, 4.24169226543235148528312412985493520e+385Q, 3.29481560149200680874049429678412127e+410Q, 1.03135148368973063748422508803572335e+437Q, 1.65119340908368036904973428016231291e+465Q, 1.74266589416784110509614783944413581e+495Q, 1.58844581452329281145953920728196433e+527Q, 1.66707913260906132496128590135534752e+561Q, 2.73591938942385354685476950777714705e+597Q, 9.72579530056263762611950442873242859e+635Q, 1.05939976357545005153997575210948938e+677Q, 5.11518133258374954640812727174811520e+720Q, 1.62189977683387633564608415164137138e+767Q, 5.13160378352024380054652132361473058e+816Q, 2.52913238575275285564889845966314333e+869Q, 3.11944472707029467274111976509771118e+925Q, 1.59495166899855372832309254674677663e+985Q, 5.78489108811836645985010918537259212e+1048Q, 2.63682062563962607583302320466728244e+1116Q, 2.77639914729635446526568065100606294e+1188Q, 1.29100591117079370649785145859270604e+1265Q, 5.28444381883484729377275155781628201e+1346Q, 3.96822241951490935324304074734991606e+1433Q, 1.19450143809568524145027520220707370e+1526Q, 3.31232848440880307300003548888720354e+1624Q, 2.05165402811808892980123939590704909e+1729Q, 7.28745294435475474950235921073537827e+1840Q, 4.04980582052614743975213722266303496e+1959Q, 1.02489069982330185861864247663932840e+2086Q, 3.68323339665104313610706754895420191e+2220Q, 6.30765607309438726154024457882187086e+2363Q, },
      { 2.45471558362986365068916495863375252e-02Q, 7.37246687390334622422734853496288652e-02Q, 1.23152530941676654318795303724845312e-01Q, 1.73000137771924855589325108501317324e-01Q, 2.23440664959686000105440799041805360e-01Q, 2.74652654971851825832501290298110601e-01Q, 3.26821679298064666878532098621581949e-01Q, 3.80142100980478924532143540106066542e-01Q, 4.34818963721561494844111167726460593e-01Q, 4.91070036509942840677161193401564426e-01Q, 5.49128045948021544092182270959032951e-01Q, 6.09243132438265439671151238479162657e-01Q, 6.71685571202114806905814141850507988e-01Q, 7.36748804906793864301473114817726587e-01Q, 8.04752841633695064447122683633828025e-01Q, 8.76048080248205070538153903222362252e-01Q, 9.51019635182333225305914695670696308e-01Q, 1.03009224453247006650365629888427541e+00Q, 1.11373585958868076495962122155474153e+00Q, 1.20247203091805887562283157205483916e+00Q, 1.29688122649686375081031224280895230e+00Q, 1.39761124182837302592381732283067440e+00Q, 1.50538689136054520464181654424860094e+00Q, 1.62102120589479802996006107398491112e+00Q, 1.74542840336904457155639617870301735e+00Q, 1.87963895203102933109827214352261973e+00Q, 2.02481710760932852386806039721112499e+00Q, 2.18228138214788418140109912591097815e+00Q, 2.35352849482388135462737441996874099e+00Q, 2.54026146822962645747200253699005204e+00Q, 2.74442267217147811137997080576917741e+00Q, 2.96823278719060661900608946565759698e+00Q, 3.21423686952065766588900110700898117e+00Q, 3.48535895790773046725176601804623352e+00Q, 3.78496698311737282096444166343378976e+00Q, 4.11695013894029509955211911245969952e+00Q, 4.48581136938823171042685637747918431e+00Q, 4.89677824656200181220988391922608405e+00Q, 5.35593629082672594809503436864536474e+00Q, 5.87038976260095690701450882557564694e+00Q, 6.44845618913111760490837120481487939e+00Q, 7.09990245267955823638498704195342171e+00Q, 7.83623225328284126133289428371735767e+00Q, 8.67103729357523063452024155765264339e+00Q, 9.62042777798599036292354986394365067e+00Q, 1.07035619887679953097266587864897721e+01Q, 1.19433000813944102150187593166292839e+01Q, 1.33670142103849964717469335941905385e+01Q, 1.50075961591439634296262105146352627e+01Q, 1.69047154820352837629069726738079470e+01Q, 1.91063966873168959742774555727687289e+01Q, 2.16710044321657799400571191555580338e+01Q, 2.46697527469509919694460528848277171e+01Q, 2.81898902515784535507915179415086299e+01Q, 3.23387613242940174515264777910328241e+01Q, 3.72490075809724574016769417176033924e+01Q, 4.30852608490774199699508301986096898e+01Q, 5.00527964765470397507453905247128474e+01Q, 5.84087760725387652778097774354827947e+01Q, 6.84769282153423986221602875181609661e+01Q, 8.06668177706071484780398912645020900e+01Q, 9.54992727020024926023862465179431320e+01Q, 1.13640119576948788485338465195796676e+02Q, 1.35945194497660320895127779474436433e+02Q, 1.63520745187974444650772294136165162e+02Q, 1.97804968791258694996900535479466855e+02Q, 2.40678753588977666070114453459006852e+02Q, 2.94617029293055502283701272741864024e+02Q, 3.62896953214712533295812475420140158e+02Q, 4.49886178271559690246754005443952599e+02Q, 5.61444735313349610605402755114132063e+02Q, 7.05489247089927142932822047183046835e+02Q, 8.92790773279996411641203534672635638e+02Q, 1.13811142497947837649816383892464930e+03Q, 1.46183599156360536709532001371341489e+03Q, 1.89233262344471618571971876282268943e+03Q, 2.46939603618613347923158360306283371e+03Q, 3.24931156929882473061795067124714454e+03Q, 4.31236711317028301201344127304044700e+03Q, 5.77409475450013966113987691727433327e+03Q, 7.80224723750085184509285318864950930e+03Q, 1.06426753097580697178856711472253120e+04Q, 1.46591538353567498972551254799117195e+04Q, 2.03952854123975483493635354197048606e+04Q, 2.86717062242155626463726121489695136e+04Q, 4.07403376218345329677215262695648133e+04Q, 5.85318231059692339303875522971681880e+04Q, 8.50568926526520663990658694675860445e+04Q, 1.25064926984785661460287048343378461e+05Q, 1.86137394316674976633487938076388915e+05Q, 2.80525577745201092730917964861422661e+05Q, 4.28278248608476174804382253986989659e+05Q, 6.62634050612765730354133581004327179e+05Q, 1.03944323965033956450234998906270616e+06Q, 1.65385742611296131560210844780522467e+06Q, 2.67031565012527916099202454680251592e+06Q, 4.37721202662435879490158484533403820e+06Q, 7.28807171369841382127860775464397414e+06Q, 1.23317299340033169377433189565436518e+07Q, 2.12155728576993369873860452866846505e+07Q, 3.71308625486153538260132253550700609e+07Q, 6.61457937735213553402161639469475625e+07Q, 1.20005529169491711045823979192639236e+08Q, 2.21862941029688069011551388945586459e+08Q, 4.18228293992868770261761230703186170e+08Q, 8.04370413249371480388874614907492609e+08Q, 1.57939298942566811374336496311019268e+09Q, 3.16812241552410463468859624242708347e+09Q, 6.49660681154986132317647460889788282e+09Q, 1.36285198835644448552647856869429224e+10Q, 2.92686389700870770766115985553270450e+10Q, 6.43979866520949373493178830943697748e+10Q, 1.45275523377290302218030864454825923e+11Q, 3.36285445938924657613055409970261746e+11Q, 7.99420278543347927144872935893265787e+11Q, 1.95326423336229196043471517599153542e+12Q, 4.90958186824255456862636657247754379e+12Q, 1.27062273076501561032764305894113023e+13Q, 3.38907098674298576400746683674724217e+13Q, 9.32508403020884483292253012776198040e+13Q, 2.64948942383453414004877623922649545e+14Q, 7.78129518409495719454885394657867226e+14Q, 2.36471505252735563941195059995826485e+15Q, 7.44413803146595825477962585154187758e+15Q, 2.43021724068474963516185214140527324e+16Q, 8.23706864153435776160816132466078110e+16Q, 2.90211705066454883998576313308511774e+17Q, 1.06415767940403701307268606275625099e+18Q, 4.06627710606196001743762877107146413e+18Q, 1.62127423363035909653152786372227505e+19Q, 6.75415683091545001302004267324917730e+19Q, 2.94405684173378191865875139697985419e+20Q, 1.34464013954910781728162580030274237e+21Q, 6.44458615894472329986968048792306357e+21Q, 3.24621866755460893428420802873638947e+22Q, 1.72123457955665353317614924039447693e+23Q, 9.62253389024047439092145016046085843e+23Q, 5.68140726041795667144549981860838281e+24Q, 3.54889077999592818369666361009585253e+25Q, 2.34950642567226956175428754094351900e+26Q, 1.65161813060520564300895544060422208e+27Q, 1.23514742649311305869239755896991097e+28Q, 9.84594723979205755046350596451684287e+28Q, 8.38313078198461041780434493882428621e+29Q, 7.63964946139917244491542497186650823e+30Q, 7.46786273223388520123213229503835823e+31Q, 7.84769148200499366026301301860529069e+32Q, 8.88603255762645470434690507794367917e+33Q, 1.08673489067830243582954987160820285e+35Q, 1.43896777703653845844251806933839180e+36Q, 2.06816886547560352123602144529722761e+37Q, 3.23488532022391238528760911223890778e+38Q, 5.52123364154262851432456431974095092e+39Q, 1.03114823119466385539623421556538031e+41Q, 2.11327203581636598172202380321061095e+42Q, 4.76672434548507751952154119062281282e+43Q, 1.18696155099021828703717401094811909e+45Q, 3.27317216920584757332668902845797411e+46Q, 1.00282122676916775281745610945056481e+48Q, 3.42493390393515647893744567642140320e+49Q, 1.30843601702642873582064430681159468e+51Q, 5.61137833004842050312648039721074217e+52Q, 2.71142480632713929135631433480050921e+54Q, 1.48177179364406644198189668782951739e+56Q, 9.19428207104277880378138495715383545e+57Q, 6.50366145587535556181715778862405651e+59Q, 5.26632998686862730262822409628217101e+61Q, 4.90266280796934735938520451826335739e+63Q, 5.27051105728955705039001489033126495e+65Q, 6.57285651167058331634360929791632134e+67Q, 9.55395603001322538662375429270666445e+69Q, 1.62649191115941161585027771743899152e+72Q, 3.25941091550095122341298778408441950e+74Q, 7.72846031811361427986096662411063427e+76Q, 2.17988199690591805869859473392710071e+79Q, 7.35448438837150591476717971591286847e+81Q, 2.98483127080395774595532033544389202e+84Q, 1.46582826781343896171132281512590065e+87Q, 8.76335597262986426104836504228132778e+89Q, 6.41790966584783113047113716478179340e+92Q, 5.79495864922989350993450871503126827e+95Q, 6.49422447231190836548908540847688948e+98Q, 9.09500015601643369774077853623649258e+101Q, 1.60305849845529910220272077472033035e+105Q, 3.58209911911932052869313856359636456e+108Q, 1.02244122713985468653173668554645414e+112Q, 3.75687218501508605716480350749498176e+115Q, 1.79136346383284915921138840751400855e+119Q, 1.11764188203947212384081419514165395e+123Q, 9.20215956554652828530686799399533824e+126Q, 1.00871647482788856814534779759128531e+131Q, 1.48554648708930180487801088228218062e+135Q, 2.96696153483056609742176098165601109e+139Q, 8.11420728466436936033056414100674073e+143Q, 3.06917808750766973862958599324080278e+148Q, 1.62222368114779147260405024572949430e+153Q, 1.21094608845400598319807865894230970e+158Q, 1.29069460371207764384235331328282226e+163Q, 1.98663030134211110165437011771903132e+168Q, 4.46757261704134429970068624260491498e+173Q, 1.48564187771108280640829188970287255e+179Q, 7.39669065831627851911038512117013389e+184Q, 5.58477447271859974925476129175614574e+190Q, 6.47976647473179732779115257999520915e+196Q, 1.17117420317317171387535528766936277e+203Q, 3.34428267755162742939489398076764840e+209Q, 1.53076558056194845489001829008909627e+216Q, 1.14010185551912954998170816176845430e+223Q, 1.40319939603195121408389523582430293e+230Q, 2.89974936197123389451668716314083017e+237Q, 1.02284833216532051937856086912560392e+245Q, 6.26387216223145817942640023700922965e+252Q, 6.77735971213475345230129326178116125e+260Q, 1.31920032114466459135766725110063188e+269Q, 4.70640341492973980389482209817290571e+277Q, 3.13724441498341266441161693520329378e+286Q, 3.98571382107801782113811082484615717e+295Q, 9.85039693194582447444400924997258508e+304Q, 4.83687381317675554709367324551273400e+314Q, 4.82285202614019890234802453942628315e+324Q, 9.98703239968301934722476402798215616e+334Q, 4.39579560388542750852159646726102667e+345Q, 4.21213245293498773753031381258995955e+356Q, 9.00647826017501992250418906434620537e+367Q, 4.40820570973056960340923739077146754e+379Q, 5.07036496321109968512657627671150030e+391Q, 1.40820593625978850981984707085120209e+404Q, 9.71170922560303314648712317222502389e+416Q, 1.71185299004740150846260848238269630e+430Q, 7.94539187117702846067322763172557224e+443Q, 1.00135866851780201306228838620557915e+458Q, 3.53720801942577272956188201390769482e+472Q, 3.61856514873836509067172206947444182e+487Q, 1.10885899005952624619970603738429749e+503Q, 1.05391354932473165812334641476989979e+519Q, 3.22052822779158301893419142803165095e+535Q, 3.28354332174247719776999412908271692e+552Q, 1.16054782509911106933820737153108500e+570Q, 1.47919833966504706156198174688381009e+588Q, 7.08133783868969672071172398101964598e+606Q, 1.32792489232842472677863721400834701e+626Q, 1.01864740027867286775312222487805144e+646Q, 3.34261841519397423195159276540555265e+666Q, 4.91359232957804830460198547632977969e+687Q, 3.39338961080932963058551495328009206e+709Q, 1.15643101006244038688616288204262882e+732Q, 2.04580060100421448267115092746141073e+755Q, 1.97956226677236405650286559215774642e+779Q, 1.10576482674657666978217926798043956e+804Q, 3.76975534316002585938373910751464161e+829Q, 8.30723712530280347580443574905340732e+855Q, 1.25551214072288994426628885709173410e+883Q, 1.38340901524945057559377504414014724e+911Q, 1.18367583857036159478030255830898223e+940Q, 8.39312984240762796712708279978405886e+969Q, 5.27445408794117779852142535860614541e+1000Q, 3.14827291975085189890248083313318702e+1032Q, 1.91708860929520191993037622102307238e+1065Q, 1.28205230070904948620917170049585485e+1099Q, 1.01601053886738575799673411634857928e+1134Q, 1.03205637073787228175634527391820202e+1170Q, 1.45709248520841293241982349536788740e+1207Q, 3.10836026767661586110721101783586616e+1245Q, 1.09211875259058272695772315187071594e+1285Q, 6.90756042513315914107755467851436390e+1325Q, 8.62072629674515227183116754798059355e+1367Q, 2.33367455582609053817676883927592981e+1411Q, 1.51088703661957707064674032871094166e+1456Q, 2.58751847359969149251723060639056652e+1502Q, 1.30061395127966085145757755976532379e+1550Q, 2.13606222552299322654005297188550444e+1599Q, 1.28040046650693999927593879574416998e+1650Q, 3.14004479696111085451082602060045251e+1702Q, 3.54445798947819813290733040762684700e+1756Q, 2.07959058928044687396800353571401401e+1812Q, 7.18928436789152221379187719747515467e+1869Q, 1.66674057039586083195182422956301841e+1929Q, 2.96144319154423967308042996118314268e+1990Q, 4.62818622228372755683383431403798070e+2053Q, 7.33344579812102490343077358675798902e+2118Q, 1.36418147108226874642787854899398972e+2186Q, 3.46578234556914165783357030404723460e+2255Q, 1.40565670831906218450256157719581702e+2327Q, 1.06915717420033634558717882909227336e+2401Q, },
      { 1.22722791705463782987186673092730169e-02Q, 3.68272289449259047084662870963424169e-02Q, 6.14133762687107999107794380606156658e-02Q, 8.60515970877820790729887694832007789e-02Q, 1.10762884001784544594356091768970671e-01Q, 1.35568393495778548180370153140547366e-01Q, 1.60489493745433548938038568465534727e-01Q, 1.85547813164508949628358434474487020e-01Q, 2.10765289867070052445854835116288718e-01Q, 2.36164222221462626796458521613760417e-01Q, 2.61767320678549526133854585166690062e-01Q, 2.87597761063134290040469640683594256e-01Q, 3.13679239524903564719983726260388052e-01Q, 3.40036029353663277020320913399073202e-01Q, 3.66693039873181019259593465683200480e-01Q, 3.93675877638645179710808224562806486e-01Q, 4.21010910174684626844126582677407364e-01Q, 4.48725332504145034061613569614932171e-01Q, 4.76847236732482946224267058692370227e-01Q, 5.05405684968820937507178521019349537e-01Q, 5.34430785882522907921272727308422687e-01Q, 5.63953775213726713429575640521392073e-01Q, 5.94007100577754899987320597582638522e-01Q, 6.24624510926871605306723866692531653e-01Q, 6.55841151058639796949436231740377164e-01Q, 6.87693661588351492208131027097817883e-01Q, 7.20220284833868340062109216385730821e-01Q, 7.53460977094957222390047011401038344e-01Q, 7.87457527846096346070444259791519611e-01Q, 8.22253686402049937748559808122413409e-01Q, 8.57895296659582580760899947902549978e-01Q, 8.94430440566859300921740259698532581e-01Q, 9.31909591024743548462187459499302759e-01Q, 9.70385774981792065875818381502464879e-01Q, 1.00991474754772858364115613090157760e+00Q, 1.05055517801908314962647763431650363e+00Q, 1.09236884878609257919070489976671100e+00Q, 1.13542086817251430035405051069824787e+00Q, 1.17977989835042446581611499466432280e+00Q, 1.22551839957114260989771330348614290e+00Q, 1.27271289206202647251525071825625510e+00Q, 1.32144423705798506493224886295961288e+00Q, 1.37179793856724595345645859982391269e+00Q, 1.42386446761438409645831411537400020e+00Q, 1.47773961086120811494347321807905579e+00Q, 1.53352484567928885758582112389540236e+00Q, 1.59132774393835509765516748425726982e+00Q, 1.65126240698431007605515051243084836e+00Q, 1.71344993451128821134705183982006487e+00Q, 1.77801893028625685775809622156398356e+00Q, 1.84510604796472086962932266394457464e+00Q, 1.91485658054495189874749078346703174e+00Q, 1.98742509734901709257017841100790150e+00Q, 2.06297613279527528332657028179424398e+00Q, 2.14168493164291678457053660944377397e+00Q, 2.22373825584899452105160804133355977e+00Q, 2.30933525868721379620595550412858017e+00Q, 2.39868843234110382076332419963510302e+00Q, 2.49202463580835609502681428341212971e+00Q, 2.58958621064512275641789028727789739e+00Q, 2.69163219284683244353140722232296853e+00Q, 2.79843963001449729057960489638268331e+00Q, 2.91030501390256265160483634586060233e+00Q, 3.02754583949736496303590546881335550e+00Q, 3.15050230294691972178684607549631061e+00Q, 3.27953915196739433034632435779672485e+00Q, 3.41504770380541061125886820639123018e+00Q, 3.55744804745655073349177433143963235e+00Q, 3.70719144864977981720917442750632409e+00Q, 3.86476297812834212534134362353667523e+00Q, 4.03068438601653134401780732606469366e+00Q, 4.20551724758861383531605318041260028e+00Q, 4.38986640858517245778656669889477004e+00Q, 4.58438376139193074830218319449001571e+00Q, 4.78977238695068769484017601922716285e+00Q, 5.00679110126136326392507134554822932e+00Q, 5.23625944981527405022093792997629943e+00Q, 5.47906319833752315041593490985864953e+00Q, 5.73616037388481741547685088499196591e+00Q, 6.00858791672861985829483499535828449e+00Q, 6.29746901064886304750229195240462364e+00Q, 6.60402116738092913326417425575120773e+00Q, 6.92956515012467783689325375523493772e+00Q, 7.27553483138386097168028487959046984e+00Q, 7.64348809212349206445236344518102598e+00Q, 8.03511888250245928810782085772095077e+00Q, 8.45227057947818812962999536922397314e+00Q, 8.89695079364178531317631891553840631e+00Q, 9.37134779701639517335117345438905815e+00Q, 9.87784876557344603277558247754926945e+00Q, 1.04190600552776203680920477623835667e+01Q, 1.09978297590083170587228134865897514e+01Q, 1.16172728242395225830192073579898657e+01Q, 1.22807990484892461058359016635119689e+01Q, 1.29921443119669104778276499842319618e+01Q, 1.37554054553562588138800381241813305e+01Q, 1.45750792662062131604851118656361439e+01Q, 1.54561061010485246793800779406223808e+01Q, 1.64039187433830292507497745621200597e+01Q, 1.74244971815420897003963134996594183e+01Q, 1.85244300868843752575149709549249943e+01Q, 1.97109838837826649364051644134983470e+01Q, 2.09921804308096164753912694285306280e+01Q, 2.23768844801398294581745473457799699e+01Q, 2.38749022527007382030837433327528532e+01Q, 2.54970926638043046429091560004131137e+01Q, 2.72554929623253155508089897070407353e+01Q, 2.91634608111962498675086489719161981e+01Q, 3.12358351442328496157597338087371982e+01Q, 3.34891184913680511842485226603345913e+01Q, 3.59416838798546509869193271004218290e+01Q, 3.86140099030723073708193155942738501e+01Q, 4.15289481132930302349687386068430483e+01Q, 4.47120275544153339602356644078995085e+01Q, 4.81918020222491017368298630788454953e+01Q, 5.20002465436155875740198471916042599e+01Q, 5.61732106253738449367657038097317749e+01Q, 6.07509370691878207865695555214723350e+01Q, 6.57786566116800396636894823760941404e+01Q, 7.13072703735772134302608508415302565e+01Q, 7.73941341346580579448409414339011685e+01Q, 8.41039608526963339165414121007032304e+01Q, 9.15098606849673444816151204282990934e+01Q, 9.96945411354770401552161275609161166e+01Q, 1.08751693942601889700382157585301539e+02Q, 1.18787600064303753208251167345242212e+02Q, 1.29922989761451637135259469378151165e+02Q, 1.42295201505637253678660735501099867e+02Q, 1.56060691466500267146385748883961050e+02Q, 1.71397954932643240617797882975933747e+02Q, 1.88510932515483007342988031066863162e+02Q, 2.07632987774012593538662727768467525e+02Q, 2.29031559465458736990493497603665470e+02Q, 2.53013611565567646662052901877967628e+02Q, 2.79932028239889691165098106403660221e+02Q, 3.10193129976673089035599193653267696e+02Q, 3.44265522210752989223327890530839313e+02Q, 3.82690530328937838722167127466549147e+02Q, 4.26094526620760770127774483839356753e+02Q, 4.75203517589290204505256185266901137e+02Q, 5.30860436623905886389110002239619071e+02Q, 5.94045680537299500918768911865886523e+02Q, 6.65901542833877826224842736585170818e+02Q, 7.47761336730915387004112664458834008e+02Q, 8.41184173047134302254019501114516601e+02Q, 9.47996569801374152415232558717779048e+02Q, 1.07034233137588184029692740932268166e+03Q, 1.21074245751858265959674123771999362e+03Q, 1.37216724155220581953081355884482812e+03Q, 1.55812321218769272183468357733000347e+03Q, 1.77275818866271628150109400030205931e+03Q, 2.02098848541186298361330491694554409e+03Q, 2.30865325932916315664195536323762336e+03Q, 2.64270218981368427312930076396387941e+03Q, 3.03142418286921021219727597009903791e+03Q, 3.48472667698575601755930501247710368e+03Q, 4.01447750473397350451805793460750946e+03Q, 4.63492426404939475098008035447052569e+03Q, 5.36320994977343974892087068772061383e+03Q, 6.22000841211434280322218569985211936e+03Q, 7.23030933285302995642999721294241650e+03Q, 8.42439021673521778311062568091410678e+03Q, 9.83902287153854178745596238243054980e+03Q, 1.15189746308311398832174294139085014e+04Q, 1.35188809887437420186309997879371889e+04Q, 1.59055874546006694678895534154510667e+04Q, 1.87610857276481617624948313285750831e+04Q, 2.21862046239336627450920062330548753e+04Q, 2.63052620505491535696779646542992118e+04Q, 3.12719440194171105738301413156011152e+04Q, 3.72767546125665292256151356126569138e+04Q, 4.45564828031227324859783992706804335e+04Q, 5.34062659201890392985687357687040023e+04Q, 6.41950058038891812326608231510870856e+04Q, 7.73851264238682005972799669756325711e+04Q, 9.35579699398172596314688114151429601e+04Q, 1.13446537582066946954261668152086820e+05Q, 1.37977827220974171292929858459577092e+05Q, 1.68327748580788705255881136304020641e+05Q, 2.05992574612073530499207561599578962e+05Q, 2.52882202450315825396186949822484192e+05Q, 3.11442271834772591489327431348343937e+05Q, 3.84814591343557073602914002196809171e+05Q, 4.77048586496682264260701809258611041e+05Q, 5.93380932472474085420815642783399996e+05Q, 7.40606619035166611456854888146209865e+05Q, 9.27573047147064337154043416441694054e+05Q, 1.16584026094018041532121013906762681e+06Q, 1.47056632211824613527279037871707355e+06Q, 1.86169889901492197099442712763381073e+06Q, 2.36558487029835449538535481946926618e+06Q, 3.01715269550576487659856266530294113e+06Q, 3.86288257359992924875408921517049995e+06Q, 4.96486430558975035817682789982702410e+06Q, 6.40636282995973660643182932326803288e+06Q, 8.29948184726130211549565432685136745e+06Q, 1.07957589264240185368036619492890404e+07Q, 1.41008732747460409136778616182972970e+07Q, 1.84951472441825010015226156029368615e+07Q, 2.43622441967080550013889014979179789e+07Q, 3.22295113186394123446611158573926085e+07Q, 4.28249388238592533660285754208101809e+07Q, 5.71579339433926763705565010979377786e+07Q, 7.66343793274545163464956180626615373e+07Q, 1.03221272549848969879325123115356857e+08Q, 1.39683399197619484239843178827556776e+08Q, 1.89925149766489273996177039954695089e+08Q, 2.59486539646750585135581953531723138e+08Q, 3.56266474246450149668190479113931322e+08Q, 4.91582541317241347106730357233806750e+08Q, 6.81731647011695814167689014048918740e+08Q, 9.50299810520254143758401252895411400e+08Q, 1.33159829534327753848250753466160796e+09Q, 1.87580197601045983131635998256926235e+09Q, 2.65667390770973148718390348704244405e+09Q, 3.78324021561636590874538087942452353e+09Q, 5.41753184850013697899991815866441223e+09Q, 7.80169536989284750988699417135654043e+09Q, 1.12996536895509883334977946121306167e+10Q, 1.64614916139082192438237472977949088e+10Q, 2.41235399573668769368537292636420902e+10Q, 3.55648689543192709406699873104224405e+10Q, 5.27534501409376051919461374149743655e+10Q, 7.87357210832537817724326568648616226e+10Q, 1.18256902031786360426341053633539951e+11Q, 1.78754944250836346103770384839274996e+11Q, 2.71963306497998614239753953623599024e+11Q, 4.16512215311989794586279802552971310e+11Q, 6.42178185820513419673438138950789714e+11Q, 9.96872549757627591785162935837099498e+11Q, 1.55821232712296039863279650270498705e+12Q, 2.45280998490709378571967041325143017e+12Q, 3.88865623282814020969225986222930869e+12Q, 6.20986899050942490907341584961146266e+12Q, 9.98992421629798366453910164990598035e+12Q, 1.61915800137861135092472802019186093e+13Q, 2.64432451866992655897802160266314720e+13Q, 4.35201884790437478598011793154963363e+13Q, 7.21888468820274170949384690425513293e+13Q, 1.20699764072734953803867091697058471e+14Q, 2.03448372244520740184714594138600722e+14Q, 3.45755310287440291972421764598644671e+14Q, 5.92524851195750570615520553006806424e+14Q, 1.02405779371303867152496937066868237e+15Q, 1.78517404594164216208571127025070841e+15Q, 3.13930698866849469593365078009075683e+15Q, 5.56985627017489012771720073467849986e+15Q, 9.97176335383446032822138577325280050e+15Q, 1.80168749111488309180201148764368470e+16Q, 3.28570985832256554206799328627718020e+16Q, 6.04901854091075971038418094513619254e+16Q, 1.12437528321136957183940077137648079e+17Q, 2.11044512595243530534854315009251863e+17Q, 4.00073700789122999206498186212756047e+17Q, 7.66084936156432930897007145016954725e+17Q, 1.48201877099617670026480296368024580e+18Q, 2.89694543391085794507262308584519511e+18Q, 5.72279016569347049314117808244814073e+18Q, 1.14268996043992146219736887475145416e+19Q, 2.30661655998410672274449611000740199e+19Q, 4.70785718461609386331913071438798214e+19Q, 9.71734634749534281292184698073106843e+19Q, 2.02873560562258544354738566036230939e+20Q, 4.28484025417100058060231110755876699e+20Q, 9.15702732902162383627232801008773485e+20Q, 1.98045783476641177674243338550380776e+21Q, 4.33560488670225200389961701585516223e+21Q, 9.60925855971422399500251930072135526e+21Q, 2.15660463060858699692371033788512835e+22Q, 4.90204590969527028901267517972005859e+22Q, 1.12874922712132846722486415215181510e+23Q, 2.63341462304993087921430516281962614e+23Q, 6.22633568449099854312810660038498408e+23Q, 1.49220527901414892100221726222044247e+24Q, 3.62576824971759010933939677750446994e+24Q, 8.93389976496144488245242728630867648e+24Q, 2.23278698168226238263925345166626442e+25Q, 5.66129533629398673236467837398265907e+25Q, 1.45661671029813314161310452512420504e+26Q, 3.80395985286848824540247897299973294e+26Q, 1.00853158560303648965770564091212618e+27Q, 2.71524742512942335771421173573350598e+27Q, 7.42507176676665196668682930989072343e+27Q, 2.06286071217322500340710263294206778e+28Q, 5.82405545879941331172090843001915861e+28Q, 1.67138883669643664401793246595736207e+29Q, 4.87683063202395639212825400688637822e+29Q, 1.44717007114610715647680482586441902e+30Q, 4.36856220892558378299543245366740004e+30Q, 1.34187380624925133807232471810065508e+31Q, 4.19525163275433868171703825329949387e+31Q, 1.33536013482821413616157861182819558e+32Q, 4.32868135071513633988478443702722304e+32Q, 1.42940186615031918577626278068758606e+33Q, 4.80973614622718069618478648576546160e+33Q, 1.64962411456760257539667516081290921e+34Q, 5.76867749241980146921395997917892869e+34Q, 2.05744285416276135030539237103441019e+35Q, 7.48642350991781106283346301415985262e+35Q, 2.78005279179115505131111680664033100e+36Q, 1.05390834766008187386632631340309755e+37Q, 4.08004633423575422346694643614725290e+37Q, 1.61355331159280537271141620030872711e+38Q, 6.52083633299761509812680604349515166e+38Q, 2.69384818625751099248700493623895698e+39Q, 1.13800240843071080011666704740931517e+40Q, 4.91774800881392461265365745148130197e+40Q, 2.17469107319135867637675221560360895e+41Q, 9.84452374543052650164816403972682005e+41Q, 4.56370746759011673249611086081412694e+42Q, 2.16735207370837913732627450052443800e+43Q, 1.05486019388717075432105322007580054e+44Q, 5.26358822556684736475437714203858810e+44Q, 2.69377245879791662278222405278060831e+45Q, 1.41450676056016307353065763949943988e+46Q, 7.62412676351201661991424110428992680e+46Q, 4.21982814876279441119734812029287822e+47Q, 2.39938766583179326419454951543637572e+48Q, 1.40213994725411743432292186837126106e+49Q, 8.42470632552542294312824936421510125e+49Q, 5.20691847994261931825305937850643235e+50Q, 3.31178786647771615130587348221001406e+51Q, 2.16868329550985915487173624396649870e+52Q, 1.46278636877920671278346292184781720e+53Q, 1.01676178457583836251098427369753410e+54Q, 7.28646099514504318390031595979099009e+54Q, 5.38619423744886540736133132036039525e+55Q, 4.10891748052874064027571987877723004e+56Q, 3.23644562594555272761751761693655719e+57Q, 2.63344065241761966896134121065725150e+58Q, 2.21470233935793926784783273212022849e+59Q, 1.92605899594826839190384056135440397e+60Q, 1.73306774041417493183516299971659594e+61Q, 1.61430716012442696900215232150692807e+62Q, 1.55746432848635213822960416335985349e+63Q, 1.55722615519719203130606727886637242e+64Q, 1.61447396270799534426677571780472075e+65Q, 1.73661740632738610492724345532304845e+66Q, 1.93920124345119052093186699988150897e+67Q, 2.24927773293662287552890832545827854e+68Q, 2.71159379871976559871119761600619238e+69Q, 3.39962873204868711911086284140566930e+70Q, 4.43538969673020629145547409643969707e+71Q, 6.02556607616400398134689309091612155e+72Q, 8.52916142538377984909758513093573554e+73Q, 1.25874632299298868846704179091958707e+75Q, 1.93811217518656021005493439215804850e+76Q, 3.11543236357261066088271223403230465e+77Q, 5.23179767443439001771782033104475730e+78Q, 9.18493020786068075728039142759876527e+79Q, 1.68692940478037877214426031162304858e+81Q, 3.24356562447423263461643222429327622e+82Q, 6.53381249893022007452896440352725950e+83Q, 1.37989882314462031449133502146398295e+85Q, 3.05765044484283991620932285441735220e+86Q, 7.11405054583917124472478320152556329e+87Q, 1.73927502444225867447030753694640018e+89Q, 4.47178291585317780365328749453520010e+90Q, 1.21003678949402814425759810458701117e+92Q, 3.44882804459086235858169840236819767e+93Q, 1.03622678375056156465794049465547871e+95Q, 3.28480191475120603834848118907352203e+96Q, 1.09951493360222463836336540053884847e+98Q, 3.88958173137824259730333790282903582e+99Q, 1.45543428790106999149108053530575702e+101Q, 5.76572993438741901863543434592570728e+102Q, 2.42034956874547558153049344213268717e+104Q, 1.07760662592977753585432532225577347e+106Q, 5.09334698869585184485524316150431382e+107Q, 2.55809082411032399725764692067377039e+109Q, 1.36651250871904796376617702296414102e+111Q, 7.77173580076352640634836399351925362e+112Q, 4.71039863879301491830684051990330316e+114Q, 3.04556388558701395434830609997782218e+116Q, 2.10276255286144299282309486039761104e+118Q, 1.55193753621259613591555622965540441e+120Q, 1.22567635442607596952721498552003566e+122Q, 1.03695094616970371146721395255392215e+124Q, 9.40788526897082771733750170914488215e+125Q, 9.16336910778509317089313688240113814e+127Q, 9.59253109567116892554520172575780003e+129Q, 1.08048629336182387529116109869898182e+132Q, 1.31103482955778245018110865569797769e+134Q, 1.71564297593263918799907327094023177e+136Q, 2.42423174270788187824126863980761699e+138Q, 3.70323122333312791897064549131589865e+140Q, 6.12322502740998890169801438496313135e+142Q, 1.09727104077119676484946099784861221e+145Q, 2.13369364324129597715060714813970067e+147Q, 4.50809918489577732767734472523944713e+149Q, 1.03625280668629118864461701581877696e+152Q, 2.59492839368798847123238762058931299e+154Q, 7.08856065084285960511459463166728250e+156Q, 2.11523492515131956204689196091626210e+159Q, 6.90448414659433816403326070235535578e+161Q, 2.46882138492543386747726128195908117e+164Q, 9.68405620339094731040251680095733169e+166Q, 4.17318211031646842247767173261932179e+169Q, 1.97862256312448325694613011941349554e+172Q, 1.03370703084875834422987498885049116e+175Q, 5.95983823379168614646426490979367954e+177Q, 3.79793920709700480341002000892325755e+180Q, 2.67930862060725892983600714882836079e+183Q, 2.09582063696050703960100191266610779e+186Q, 1.82074244214148973928235757920582367e+189Q, 1.75963855825241574501304502726864570e+192Q, 1.89499379332809905379546394212298551e+195Q, 2.27793735309853697376707463982899100e+198Q, 3.06180392186430523426845441481052145e+201Q, 4.60976071298011598021963844074936266e+204Q, 7.78790454205446308413194702704532804e+207Q, 1.47908157180353184742483342055946619e+211Q, 3.16368583872054810499056635486446885e+214Q, 7.63549763158546449019055671859541326e+217Q, 2.08329116709848069811436257881072129e+221Q, 6.43828341650089537912773112646579654e+224Q, 2.25813433461443519689405569138116459e+228Q, 9.00646808360283887236786414348123292e+231Q, 4.09320439419929471708152017376102969e+235Q, 2.12407325647515198386324613590453899e+239Q, 1.26118767744139661919617876453932054e+243Q, 8.58648519817790365555570528355195441e+246Q, 6.71757354945431655557160007037610215e+250Q, 6.05231172230061504649457274179966269e+254Q, 6.29372154343611302741074349158106138e+258Q, 7.57097597581835804454005529249965405e+262Q, 1.05596735728342565613175490019879868e+267Q, 1.71165116794080014782255848570375371e+271Q, 3.23201945416757368365209341689336569e+275Q, 7.12640648337469193163827718081711822e+279Q, 1.83935498907936650155252747841577857e+284Q, 5.57103618187823353431701691950875589e+288Q, 1.98507054903582594730288556561442957e+293Q, 8.34251060679185969108286852637314516e+297Q, 4.14598050279815660053056892060055710e+302Q, 2.44294673707515955659314553816687272e+307Q, 1.71128222954502480992240259231707340e+312Q, 1.42900308749681080618580596429329500e+317Q, 1.42642910690539638749189376290614404e+322Q, 1.70684173154227881505687976959428819e+327Q, 2.45528610324097413976825941240621397e+332Q, 4.25829782132342793910509184917611473e+337Q, 8.93046646026781112771479518830708119e+342Q, 2.27151544737307916343177075009962798e+348Q, 7.02878727199449743249339960113532074e+353Q, 2.65404789370370153123009487802961972e+359Q, 1.22676780524208968705932842819459632e+365Q, 6.96344727462083702750636558199408867e+370Q, 4.86966795424296645258294731476827244e+376Q, 4.20934473165669380666938166564826622e+382Q, 4.51252011030278817555903939588122000e+388Q, 6.01984854233799583525556662766352847e+394Q, 1.00278809583154248088779511185941092e+401Q, 2.09319142855536626778348201007914114e+407Q, 5.49449523120000769396463190666315855e+413Q, 1.82025791721843330667826837307018788e+420Q, 7.63864295498675379110157901286555290e+426Q, 4.07562853905456437955133694031162566e+433Q, 2.77530355159307878582751292111532382e+440Q, 2.42120842363222072539668100784464301e+447Q, 2.71677827391519917988250533365057684e+454Q, 3.93638693069501511102210790122717956e+461Q, 7.39454183839498133330514511562965145e+468Q, 1.80830021083203927258676228302841634e+476Q, 5.78068632124848075130373030745294118e+483Q, 2.42588452835387252450192322587436972e+491Q, 1.34215845252903171028854514732509154e+499Q, 9.83265508980143417319549250479290229e+506Q, 9.58056982887069577741485477351756765e+514Q, 1.24714172045678509784766926344381810e+523Q, 2.17883996343750012869434076146952309e+531Q, 5.13253875779605180900218275684122450e+539Q, 1.63787417999262086647656267832207076e+548Q, 7.11453353628067905769574986613707006e+556Q, 4.22706394134172663816196292889312018e+565Q, 3.45223064191878555675199989739485645e+574Q, 3.89497996822823191874712723630878414e+583Q, 6.10190158010921783812771061850774756e+592Q, 1.33421132288849391233542855134113771e+602Q, 4.09320749021551498574887738660496448e+611Q, 1.77132716471266560117452274418925035e+621Q, 1.08713213788405301831391213988591367e+631Q, 9.51489736810900467024086259204897883e+640Q, 1.19423949258625915586962226198410651e+651Q, 2.16177179741543970148744161752725437e+661Q, 5.67627906160460395827390123125168239e+671Q, 2.17468780419782497779087319492719363e+682Q, 1.22290652527655281598138666471594618e+693Q, 1.01549393259398829829790551999023256e+704Q, 1.25289600863132317250645757199143453e+715Q, 2.31107594661956354485796436668094840e+726Q, 6.41394790132987132728494077504399223e+737Q, 2.69551958118799603835043225368071745e+749Q, 1.72664572305999101481397361296053504e+761Q, 1.69703260411568714431268560545429241e+773Q, 2.57650655058878846949421879421719884e+785Q, 6.08415755628422694511089641903676709e+797Q, 2.25018717111577650332251351426363709e+810Q, 1.31266605271635406458566527913919394e+823Q, 1.21653337436496205608516398221959572e+836Q, 1.80423787152105848481394522091997928e+849Q, 4.31399102346512532331941966328171850e+862Q, 1.67550866484958200095075315692314084e+876Q, 1.06515761703845778105889680563699868e+890Q, 1.11699400379133199608851392356049526e+904Q, 1.94751426714424559351617923238456424e+918Q, 5.69089114062808012687080164398087070e+932Q, 2.80983921519267570591812248264495618e+947Q, 2.36358690419951504134253704409272547e+962Q, 3.41581578617948637677662230945252306e+977Q, 8.55364646944583859412326553382242541e+992Q, 3.74370530455423912034790817625191291e+1008Q, 2.88911104228589616462565573594290893e+1024Q, 3.96659525977064102663870040642245025e+1040Q, 9.77693110684684436928410437465483271e+1056Q, 4.36636679166615668883990202463770904e+1073Q, 3.56644781478399708285654633626787310e+1090Q, 5.37871519143314493436384142989505797e+1107Q, 1.51231507376826438439352643482625072e+1125Q, 8.00547597409214659416862336552752652e+1142Q, 8.05824292014776003962551051182620642e+1160Q, 1.55810667707874651581920999067306413e+1179Q, 5.84685322894210562805362663900103590e+1197Q, 4.30279053871861463775007328286068764e+1216Q, 6.27606568696844727186804750244767860e+1235Q, 1.83405691639178257209003812558712559e+1255Q, 1.08562206577049472147312923532596973e+1275Q, 1.31617110088183556471407953592008454e+1295Q, 3.30534811890625751178778666339188597e+1315Q, 1.73929710751143724152073404455311704e+1336Q, 1.94017558758640506527365311633351598e+1357Q, 4.64256117877888468390840687039281473e+1378Q, 2.41181028167559547953976181592626603e+1400Q, 2.75359028326650146526233980288420348e+1422Q, 6.99538184026698121966983693939988891e+1444Q, 4.00451124186760586509360315430592520e+1467Q, 5.23202484555080004711423052985251790e+1490Q, 1.58057811961053246035386581678791248e+1514Q, 1.11871792077110492782575037665515064e+1538Q, 1.88021076704060084216083677757823795e+1562Q, 7.60656064089491206752552083826812435e+1586Q, 7.51058336307020750806534892000369186e+1611Q, 1.83554419567039732901090730190332067e+1637Q, 1.12631598317224168716420027493130577e+1663Q, 1.76058045324267978077071148859252887e+1689Q, 7.11454090252573253550862708170430267e+1715Q, 7.54447665013986581882495689819567483e+1742Q, 2.13157561258614754140817896581925999e+1770Q, 1.62953544267683384450064825605699335e+1798Q, 3.42393824570039208761545128642814549e+1826Q, 2.00910152737038919233657554807306054e+1855Q, 3.34591874171713502502193846562110253e+1884Q, 1.60768420173566681503057284560969387e+1914Q, 2.26623321264716591681578650589551852e+1944Q, 9.53208108881430895613749023747672822e+1974Q, 1.21710154769951529957336846304572308e+2006Q, 4.80082954212402831503263841738379326e+2037Q, 5.95484092414598408997112765002169497e+2069Q, 2.36496081234284780469612914593602194e+2102Q, 3.06292618387296708176633714456063693e+2135Q, 1.31792936450130144017383754294435167e+2169Q, 1.92000470996606623202622993195686002e+2203Q, 9.65401246356529853165036795448719991e+2237Q, 1.70836636787270782624240314280943940e+2273Q, 1.08524396756651055665425968680627268e+2309Q, 2.52515527758383642102305453689375992e+2345Q, 2.19655419566926380800477364631433717e+2382Q, 7.29302139222844158929601074949211737e+2419Q, 9.43942215540978305304206842223557338e+2457Q, },
   };
   m_weights = {
      { 7.86824160483962150718731247753528972e+00Q, 8.80516388073301111614873617217928009e+02Q, 5.39627832352070566780640331455747505e+07Q, 8.87651189696816131653619534136597185e+19Q, 2.43279187926922555339407396412240848e+52Q, 6.39971351208020291074628474066382445e+139Q, 4.88537754925138382971529406336289926e+376Q, 7.16883681174178397147232888689884595e+1019Q, },
      { 2.39852427630263521821751964327863340e+00Q, 5.24459642372668102196104972893810164e+01Q, 6.45788781959820175983419155536535220e+04Q, 2.50998524251137450575088710958358197e+12Q, 1.77402926932713870128394504873348147e+32Q, 2.78140611598309731397930137939524577e+85Q, 1.96038425539892219077400134901764646e+229Q, 3.66150166331050442649946863740591804e+619Q, 4.83160223742266359875093017291942249e+1679Q, },
      { 1.74936958310838685164948357553735581e+00Q, 3.97965898193460781260142246747934196e+00Q, 1.84851459857444957000370375296526856e+01Q, 1.86488071893206798762140574520079510e+02Q, 5.97420569526326585534733485801413054e+03Q, 1.27041263514462334076609190768051333e+06Q, 6.16419301429598407050713079962266577e+09Q, 5.23085003181122252980303620742723664e+15Q, 2.22626092994336977442577822584431966e+25Q, 1.19993110204218159156995352671876189e+41Q, 7.47060214427514621386633671542416223e+66Q, 1.81446586052841067579857900138568293e+109Q, 9.97368828205170536427067380214302839e+178Q, 6.91104863699861841609881512683369620e+293Q, 1.09318211394169115127064538702854062e+483Q, 6.82844528756738528157668552351754659e+794Q, 5.72058982179767644458682394627277737e+1308Q, 8.56032690374774097638382410967859196e+2155Q, },
      { 1.61385906218836617287347897159238284e+00Q, 1.99776729186967326240633804225996920e+00Q, 3.02023197990883422014272607858586524e+00Q, 5.47764184385905776136260784219973923e+00Q, 1.17966091649267167180700675171097629e+01Q, 3.03550484851859829445106624470076282e+01Q, 9.58442179379492086007230677188042206e+01Q, 3.89387023822999207648300659707623000e+02Q, 2.17919325035791134362027855479039721e+03Q, 1.83920812396413285238691815996130729e+04Q, 2.63212061259985616674528417452471371e+05Q, 7.42729650716946820985576548297567563e+06Q, 5.01587564834123235599460194208596041e+08Q, 1.03961086724154411336246801784030341e+11Q, 9.10032891181809197707572646881781254e+13Q, 5.06865116389023157141527800972456655e+17Q, 3.03996652071490261550678134010186516e+22Q, 3.85774019467200796234141077624516370e+28Q, 2.46554276366658108662483315347592086e+36Q, 2.41643944916779946083480638296809418e+46Q, 1.51709155392660414912154078562432491e+59Q, 3.82504341202141137966304147261186085e+75Q, 4.08958239682159863968142401533332977e+96Q, 3.82377589429556404969214069508991010e+123Q, 1.52310131188389950567280579870007714e+158Q, 3.79636429591225792206340580596443044e+202Q, 3.58806569407775804668752012761380285e+259Q, 4.80771394235873283225580334249205764e+332Q, 3.53246347094395956018650264083397114e+426Q, 1.10588270611697913736223556513462127e+547Q, 5.39876096236024571737551333865366627e+701Q, 2.11595993056851385189640470523173475e+900Q, 1.96510021934867185245970661270267904e+1155Q, 4.44393205109502635384704176698510343e+1482Q, 8.87699807096655545789464168899561262e+1902Q, 3.92593109193326452307669545677335709e+2442Q, },
      { 1.58146595953669474418006927755018553e+00Q, 1.66914991043853474595928766810899114e+00Q, 1.85752318859500577038805456054280667e+00Q, 2.17566262362699411973750280579195666e+00Q, 2.67590137521102056412774254907718688e+00Q, 3.44773868249879174414308452011308777e+00Q, 4.64394654035546412593428923628065357e+00Q, 6.53020449657424861626175040050215115e+00Q, 9.58228501556680496101434956408351228e+00Q, 1.46836140751544096048943311347045852e+01Q, 2.35444954874098753301967806270994218e+01Q, 3.96352727330516670475446596081886449e+01Q, 7.03763520626753854729866526912608057e+01Q, 1.32588012478483886772029444321222706e+02Q, 2.66962564954156917240259777486475719e+02Q, 5.79374919850847267642551329761882844e+02Q, 1.36869192832130360517783396696756604e+03Q, 3.55943572153313055370459329132660088e+03Q, 1.03218667727076331844496375241465304e+04Q, 3.38662130285874148722242481662728477e+04Q, 1.27816625984024682983932609690817446e+05Q, 5.65408251392669309819682774275375355e+05Q, 2.99446204478172183285915112991350446e+06Q, 1.94497502342191494704216422938073734e+07Q, 1.59219300769056058775193216743984073e+08Q, 1.69428881861745991307405037132734657e+09Q, 2.42715618231130327064401251217732118e+10Q, 4.87031784819945548978505622564878836e+11Q, 1.43181965622918179279761684530563788e+13Q, 6.48947152309930125567236832263111486e+14Q, 4.80375775250898910621624420686976943e+16Q, 6.20009636130533154063228597685772759e+18Q, 1.50256856243991489872472363253210358e+21Q, 7.43606136718968825065770257988550430e+23Q, 8.26476121867792860277670169213789988e+26Q, 2.29773502789780434527854280137181506e+30Q, 1.80544977956953499695878114747293447e+34Q, 4.60447236019906193095622500171344417e+38Q, 4.45837121203062685356208723886733392e+43Q, 1.95763826111480930920135175891530462e+49Q, 4.76736813716250076391436967954985413e+55Q, 8.08882013947672128478290968549029064e+62Q, 1.23826089734928635728800137667726115e+71Q, 2.29227250527884206154485624438166937e+80Q, 7.15139237374919354944182099110281069e+90Q, 5.47671485015604443103262357109062276e+102Q, 1.57665561837070068115746726288773175e+116Q, 2.76544859595785195782707224216472471e+131Q, 5.10805125528313267335507302418826239e+148Q, 1.84712724771593770593326127127227089e+168Q, 2.64019849791365137425805671512502957e+190Q, 3.30712207665004277982044248938187504e+215Q, 8.94854908018147130170828014681013313e+243Q, 1.45387781135416845215757148230318159e+276Q, 4.51726707740089625471551690811905600e+312Q, 9.97430447590682462482304866834403173e+353Q, 6.92693028486990716815899177110112886e+400Q, 8.16310561774970561761094556639568748e+453Q, 1.10229203674018117764688357752075301e+514Q, 1.48517414340573263317638725427854581e+582Q, 2.31930659130340479680373862468017961e+659Q, 6.75943980795305614491339585238335763e+746Q, 8.57037256903489416057847741388693004e+845Q, 1.67601881139415883633596646631875881e+958Q, 2.88227840597067494281761984210848274e+1085Q, 4.25760590360997372209644858208981618e+1229Q, 9.71180927884114514977776411099980676e+1392Q, 1.22768447544318231419683656745290694e+1578Q, 6.75552744777466879522724469574589977e+1787Q, 3.08769330528996647489562213811332711e+2025Q, 6.11556995885701147380172201043083647e+2294Q, },
      { 1.57345777357310838646187303563867071e+00Q, 1.59489275503866378652125176234375753e+00Q, 1.63853651553023474161201404240726646e+00Q, 1.70598040821221362042267472116012952e+00Q, 1.79972439460873727464624167982278042e+00Q, 1.92332285442565630724716834865923523e+00Q, 2.08159737331326817824115844787944556e+00Q, 2.28093488379007051069614759371332643e+00Q, 2.52969785238770465522974588384012833e+00Q, 2.83878478255295118549225363326336900e+00Q, 3.22239574502098061154099310373833179e+00Q, 3.69908135885423511174795221061725889e+00Q, 4.29318827433052679979662014839909054e+00Q, 5.03686535632233007617225633242190622e+00Q, 5.97287114091093219903794699750262296e+00Q, 7.15853842431107756435392592099817447e+00Q, 8.67142780089207638456187671594514668e+00Q, 1.06174736029792232565670017416978690e+01Q, 1.31428500226023559963724543395522033e+01Q, 1.64514562566842803959315507240301356e+01Q, 2.08309944999818906870911848992408145e+01Q, 2.66923598979164019005652953856219015e+01Q, 3.46299351479137818875120770497256101e+01Q, 4.55151836265366257897259922581190951e+01Q, 6.06440808776439211617961123881399848e+01Q, 8.19729691748584679772374579308014156e+01Q, 1.12502046808165256396405270593673624e+02Q, 1.56909655284471412314380735570422525e+02Q, 2.22620434786863827630193937721495593e+02Q, 3.21638548950407775497940425181412035e+02Q, 4.73757450594546173931972158718216152e+02Q, 7.12299454814699763671577043536625350e+02Q, 1.09460965268637655307120839628626644e+03Q, 1.72169778917604957594996919520027157e+03Q, 2.77592490925383514631415480161515100e+03Q, 4.59523006626814934713366494475518926e+03Q, 7.82342758664157367197225266771664916e+03Q, 1.37235743526910540538254088034330370e+04Q, 2.48518896164511955326091118611007805e+04Q, 4.65553874542597278270867281925971401e+04Q, 9.04176678213568688399001015321300800e+04Q, 1.82484396486272839208899824313480775e+05Q, 3.83680026409461402659414641247062580e+05Q, 8.42627197024516802563287310497296237e+05Q, 1.93843257415878263434890786889254173e+06Q, 4.68511284935648552770705724295731096e+06Q, 1.19352866721860792702129481323969824e+07Q, 3.21564375224798931587480096707273872e+07Q, 9.19600892838660038574630990314077029e+07Q, 2.80222317845755996380600711673673348e+08Q, 9.13611082526745888639724836491288094e+08Q, 3.20091090078314859097182280497599932e+09Q, 1.21076526423472368892320719657876832e+10Q, 4.96902474509310180847282308967509245e+10Q, 2.22431575186385521573661995325533148e+11Q, 1.09212534444931366008730317965795763e+12Q, 5.91688298001991935913437013926087352e+12Q, 3.55974343849457724875846980164154730e+13Q, 2.39435365294546519110955303872941851e+14Q, 1.81355107351750191688958030705258863e+15Q, 1.55873670616616573768075986251057862e+16Q, 1.53271487555511433265791312985848665e+17Q, 1.73927477619078921206684554401295421e+18Q, 2.29884121680221631264260395223605839e+19Q, 3.57403069883776266374408947394989771e+20Q, 6.60489970545141907950722926660909000e+21Q, 1.46715587959182065910258109064501314e+23Q, 3.96409496439850938104106380506197921e+24Q, 1.31934284059534879276193138323357204e+26Q, 5.48225197134040074244755342952808857e+27Q, 2.88513789472382751842871762855110475e+29Q, 1.95253984076539210960955400019857653e+31Q, 1.72705148903222279729658337426192599e+33Q, 2.03134350709543939580166226396358099e+35Q, 3.23607414697259998035422932108168633e+37Q, 7.12048741298349720027592249584649287e+39Q, 2.20955270741101726546978031488466999e+42Q, 9.88628264779138464833350749737352873e+44Q, 6.53051404878827352855218417636385136e+47Q, 6.53070667248154652797117704066842547e+50Q, 1.01551880743128195070423638840822631e+54Q, 2.52636677316239450980056816553072407e+57Q, 1.03645051990679029664966085872637264e+61Q, 7.24196603262713586073897504016245799e+64Q, 8.91940252076971493816577515322150818e+68Q, 2.00846361915299290502283821529295631e+73Q, 8.59691476483026002005094018998424562e+77Q, 7.29059954682949521964023913634428261e+82Q, 1.28019956321641911210408802307006043e+88Q, 4.87834928560320114983891893560226105e+93Q, 4.24082824806412793978885646592598549e+99Q, 8.86977176472159872046976711615695992e+105Q, 4.72334257574141766931261505651248075e+112Q, 6.80203596332618858057580041658814811e+119Q, 2.82453118099000954895979587744113614e+127Q, 3.62104921674598225195529453488856248e+135Q, 1.54127015033494251978994281882356439e+144Q, 2.35337699517436278507886852532825112e+153Q, 1.39975657916895026384257958622345253e+163Q, 3.54037503829826541837221286091526442e+173Q, 4.18047500721395810079642214620070836e+184Q, 2.54530778191403081572971577964641848e+196Q, 8.88250526322285889574436025179770286e+208Q, 1.98845751036127430533908054978079217e+222Q, 3.21915661509946819003841034873947498e+236Q, 4.28183215516658291456439674095688919e+251Q, 5.36006145974509206228714676601426569e+267Q, 7.29722806821457796264952588871034685e+284Q, 1.26019994797636341237075623262002305e+303Q, 3.25215241711283834871808603425369243e+322Q, 1.49313102632599158215031971670664716e+343Q, 1.46842377691796129245813472736592684e+365Q, 3.76931518236156669675469780787997744e+388Q, 3.11671991143285521404945273561352902e+413Q, 1.03852445906488241953781523768340610e+440Q, 1.76991068936691550622045923706396857e+468Q, 1.98843278522413458075823481121326426e+498Q, 1.92935688449069146367365017414279935e+530Q, 2.15545898606278242992831028009446695e+564Q, 3.76556572065746315676705102085514331e+600Q, 1.42493629186903074753303377268119903e+639Q, 1.65224172380259971159552714105694350e+680Q, 8.49215944410421946026158352935784666e+723Q, 2.86631893981320588349507785370693896e+770Q, 9.65377141044877270160840903468092119e+819Q, 5.06475979937176267805686278644876593e+872Q, 6.64979079902917739733555562625190968e+928Q, 3.61927525412094487856735509111512169e+988Q, 1.39737404381894022658529893979427383e+1052Q, 6.78018387620311055320186127570560827e+1119Q, 7.59952044103401209205510909410129754e+1191Q, 3.76162862667265940011010617225061429e+1268Q, 1.63904309514033654209643026524489542e+1350Q, 1.31017858257252889370615733020834140e+1437Q, 4.19821396072116298475329351264459339e+1529Q, 1.23923791809549425227491870065845752e+1628Q, 8.17087984671936465200033730232853592e+1732Q, 3.08946915513007963115203650683840400e+1844Q, 1.82761916569338643673217752139790470e+1963Q, 4.92348318803338950087945240543349430e+2089Q, 1.88350835331965060456271693216651123e+2224Q, 3.43360015586068277793841059306542129e+2367Q, },
      { 1.57146131655078329437644376888801138e+00Q, 1.57679016631693834484126246342345329e+00Q, 1.58749564037038331646069625234555521e+00Q, 1.60367395634137020950291269057600307e+00Q, 1.62547112545749394267938666988196139e+00Q, 1.65308501191593930171844822364141731e+00Q, 1.68676814252591123625053019619255158e+00Q, 1.72683132353751620184313195813656600e+00Q, 1.77364813866723660171326886678529701e+00Q, 1.82766042147866144821632902536146347e+00Q, 1.88938481704401819562549601165115021e+00Q, 1.95942057285503709111658357403615203e+00Q, 2.03845872804790892275259742585569433e+00Q, 2.12729290408384722476899855222432786e+00Q, 2.22683194019907694104850876757126753e+00Q, 2.33811466455513029581902771513343722e+00Q, 2.46232714872299130380967769641847017e+00Q, 2.60082286092708516436098022317536381e+00Q, 2.75514621481455435937229822050004007e+00Q, 2.92706010842448355516791030533685920e+00Q, 3.11857816624092195149666191379786472e+00Q, 3.33200254033950662971711811118318338e+00Q, 3.56996830041074027550628111498923678e+00Q, 3.83549565399644726181552365400966870e+00Q, 4.13205149651293488468153425460318353e+00Q, 4.46362210669906788115667211361364802e+00Q, 4.83479919100800655701530131187647507e+00Q, 5.25088195776567960767293727240320892e+00Q, 5.71799849087533312421158643544587353e+00Q, 6.24325042159856810533215887376856930e+00Q, 6.83488580122654183866366620676303900e+00Q, 7.50250620278934080195915244309172491e+00Q, 8.25731548449354420110207897041998984e+00Q, 9.11241940586464263380079984086513597e+00Q, 1.00831874954399775830998395457182684e+01Q, 1.11876913499386520162594085796167645e+01Q, 1.24472370591410688131760615976965093e+01Q, 1.38870139060550758665255786705519681e+01Q, 1.55368871591590018952847646637454593e+01Q, 1.74323700068094283108112572669239084e+01Q, 1.96158189482399342378436216339159090e+01Q, 2.21379088635427380602824337731297019e+01Q, 2.50594593467713760968808829296439171e+01Q, 2.84537037774213756120135736520435304e+01Q, 3.24091184596952483433430577491101072e+01Q, 3.70329628948023016142035941015485370e+01Q, 4.24557264474626791146220920324192917e+01Q, 4.88367348033798558171280311705362727e+01Q, 5.63712464058697542020818458752120814e+01Q, 6.52994709275261034008086934786702551e+01Q, 7.59180775569412283714380617539343408e+01Q, 8.85949425239166382162609558265821220e+01Q, 1.03788129500578812415162165531670704e+02Q, 1.22070426396922674625756853326498468e+02Q, 1.44161209813120053456742433728771078e+02Q, 1.70968019124577351122499690038707734e+02Q, 2.03641059384357556999222931732850856e+02Q, 2.43645005870872364316059216110059642e+02Q, 2.92854081218207610543177302881458406e+02Q, 3.53678601915225339202500542949917795e+02Q, 4.29234308396729693907268968564609660e+02Q, 5.23570184048873302665650155276206906e+02Q, 6.41976689800302457508415746115180260e+02Q, 7.91405208366875928332161102969868782e+02Q, 9.81042208908193163688535235538889459e+02Q, 1.22309999499974039331272100414750813e+03Q, 1.53391255542711212726525490457861725e+03Q, 1.93546401360583033885511419988831009e+03Q, 2.45753454991288685207196097797062484e+03Q, 3.14073373162363551894782974867325472e+03Q, 4.04081818856465189750723035050638141e+03Q, 5.23488159971222568136194576211612571e+03Q, 6.83029445760732922582000345557383347e+03Q, 8.97771322864988714349790288910627671e+03Q, 1.18901592096732683947870697270695144e+04Q, 1.58712238704434696215746702894136306e+04Q, 2.13571110644578933143540830593360855e+04Q, 2.89798370518968143718297906801146350e+04Q, 3.96630672679554794954087134590560762e+04Q, 5.47687519375000078688553248953764732e+04Q, 7.63235653938805568016142182690471993e+04Q, 1.07371914975497695062170132456450593e+05Q, 1.52531667455557415180420019888676134e+05Q, 2.18877843474421658617449971461581691e+05Q, 3.17362449601929560775572985879585343e+05Q, 4.65120152586932846156175983364596723e+05Q, 6.89253765628058057206329188782990860e+05Q, 1.03311988512001998186230013733752720e+06Q, 1.56688798104325249948154338241641806e+06Q, 2.40549202702653179507492073842261453e+06Q, 3.73952896481591033994026114668796352e+06Q, 5.88912115489558003244667233279563785e+06Q, 9.39904635192234203007457377008255614e+06Q, 1.52090327612965351790614542733698474e+07Q, 2.49628718729357616773929842263043964e+07Q, 4.15775925996307484022703929049831425e+07Q, 7.03070536695026731176158877103497776e+07Q, 1.20759855845249336607363637052870598e+08Q, 2.10788250946484683324104572583041111e+08Q, 3.74104719902345786397351281755078701e+08Q, 6.75449459498741557237788387723071522e+08Q, 1.24131674041588053743260021864117384e+09Q, 2.32331003264955286246594304305695965e+09Q, 4.43117601902662575879840759020751450e+09Q, 8.61744648740090012951326726568665021e+09Q, 1.70983690660403151330619158427281665e+10Q, 3.46357452188017133850611855017361923e+10Q, 7.16760712379927072634183907084755700e+10Q, 1.51634762091005407908273857408800162e+11Q, 3.28172932323895052570169126695951209e+11Q, 7.27110260029828078950026409435831711e+11Q, 1.65049955237878037840731169761272102e+12Q, 3.84133814950880391685509371196326109e+12Q, 9.17374426778517657484962791255187134e+12Q, 2.24990194635751997880832549111511175e+13Q, 5.67153508990061173097292032404781538e+13Q, 1.47074225030769701852963232235912597e+14Q, 3.92701251846431177545392497147427712e+14Q, 1.08063997739121282020832802862957248e+15Q, 3.06767146672047518894275739534732971e+15Q, 8.99238678919832842771174638355746631e+15Q, 2.72472253652459211109041460799535823e+16Q, 8.54294612226338925817358060817490416e+16Q, 2.77461371872557475533476908014408616e+17Q, 9.34529947938202912146296976663431863e+17Q, 3.26799612298773188163350554315742192e+18Q, 1.18791443345546831451732293519928714e+19Q, 4.49405340841856421397062311314705655e+19Q, 1.77170665219548674305185389795833488e+20Q, 7.28810255288593152697603764235601397e+20Q, 3.13251243081662534865188632436805529e+21Q, 1.40874376795107311038838007000777184e+22Q, 6.63829426823606041441317767891166205e+22Q, 3.28254360840356501289107248021554584e+23Q, 1.70592009803839406409667145645666005e+24Q, 9.33225938514852428454935743602712728e+24Q, 5.38272717587488831207327363463407970e+25Q, 3.27895423512209324910006662705296242e+26Q, 2.11319169795745809927716305122705952e+27Q, 1.44341104149964304009687846325713305e+28Q, 1.04686439465498242316197235200069352e+29Q, 8.07731922695890570023291958025986094e+29Q, 6.64314696343261627707440459400107155e+30Q, 5.83567012135998626006817430636714368e+31Q, 5.48689029679023079776199817150941362e+32Q, 5.53372696850826161418229677716330718e+33Q, 5.99973499641835283449509393028972981e+34Q, 7.00917611946612256941556198729562373e+35Q, 8.84406196642459749869759626748371565e+36Q, 1.20822686086960596115573575744502341e+38Q, 1.79164851431106333799499432947126299e+39Q, 2.89131391671320576216903362266762013e+40Q, 5.09145786021152729801159516043566174e+41Q, 9.81063058840249655325327952160218101e+42Q, 2.07444123914737886022245738089968362e+44Q, 4.82765011693770054022189143000576390e+45Q, 1.24028793911154902904261886940649724e+47Q, 3.52878285864478461616951002487254771e+48Q, 1.11544949047169665881036414807872651e+50Q, 3.93051064332819631369914292573124112e+51Q, 1.54924371295785233686867649142553059e+53Q, 6.85499823804130100151231679166543611e+54Q, 3.41747996158320770357877327002949632e+56Q, 1.92690549864107998957222500013304086e+58Q, 1.23358096300491944958557136475962190e+60Q, 9.00281990289807691528078151573158069e+61Q, 7.52141514125344164522463463202808470e+63Q, 7.22427755490057899279344696654950663e+65Q, 8.01283283053507860974456727997488253e+67Q, 1.03099962028638036919997632019550562e+70Q, 1.54617495707674867923983623892142209e+72Q, 2.71580377261324869371341160824868908e+74Q, 5.61508992057174643750226165228505259e+76Q, 1.37366785934534333692942668275259398e+79Q, 3.99754102076962512613158938364235073e+81Q, 1.39150058933980008745266389466237314e+84Q, 5.82669384491202289249159160687839973e+86Q, 2.95227482092954909582266634062763990e+89Q, 1.82102306147846628152669685943631657e+92Q, 1.37597302213794152564743989887492097e+95Q, 1.28185236754341294523601004808033157e+98Q, 1.48213012720199050337233086306566580e+101Q, 2.14157427379243531419232668034147566e+104Q, 3.89449554094711237984570195229015661e+107Q, 8.97864636258010296104790318951217808e+110Q, 2.64413158980724405044777067425347595e+114Q, 1.00240353984191383430257305957041439e+118Q, 4.93141280490390525871801097705197944e+121Q, 3.17440111243586504420672164931106998e+125Q, 2.69662400176189239027511019786185589e+129Q, 3.04979932232044716644635827452454425e+133Q, 4.63404152681868778475392087605038845e+137Q, 9.54898313480310651224157422560754643e+141Q, 2.69440486619208982853089677098686964e+146Q, 1.05150272003639532507210605903884076e+151Q, 5.73417064062624495509840837865743050e+155Q, 4.41627650777870044428827641421915327e+160Q, 4.85653500442164862297918118887566388e+165Q, 7.71243776020172406235461422008761914e+170Q, 1.78944284768135145352387398006192815e+176Q, 6.13948504948193852500464630902178966e+181Q, 3.15374719244414866832063986813364807e+187Q, 2.45678229633130089072342575625791640e+193Q, 2.94097956743157560377748279040964662e+199Q, 5.48435772238022826264635788020315114e+205Q, 1.61576755939787162649511106856450099e+212Q, 7.63055674811964328822328719215905762e+218Q, 5.86357963426750903008317058039636736e+225Q, 7.44578009523250679265610908767109796e+232Q, 1.58753358925950887380498944645468602e+240Q, 5.77757179750005470017336807397418313e+247Q, 3.65046885264085030960970127282511879e+255Q, 4.07509702021276189043935129812053895e+263Q, 8.18389073292535025618266801702554839e+271Q, 3.01238089652162618211263051563523370e+280Q, 2.07176624104483264053704912869659325e+289Q, 2.71562735784398218302697011744202385e+298Q, 6.92451661016398143622710849660362520e+307Q, 3.50810155650162047040906671177815417e+317Q, 3.60896781300246599975888048597671985e+327Q, 7.71058277826701049982730585006218034e+337Q, 3.50154660288711246272389096490054302e+348Q, 3.46175331564137723439223486579386975e+359Q, 7.63696466254316472130052183903629062e+370Q, 3.85655304044390258996784681505914035e+382Q, 4.57665692577932081843759029527968784e+394Q, 1.31143566628034307965133274338127595e+407Q, 9.33142940209453779649608781259673613e+419Q, 1.69703443400488874615229289912465401e+433Q, 8.12664327627950884948530966790210152e+446Q, 1.05671342182667061564323377987059929e+461Q, 3.85123350151333031094457679550412807e+475Q, 4.06487630836024540948583469098247829e+490Q, 1.28516517984503545753808504263016648e+506Q, 1.26025754690341684764700072541766676e+522Q, 3.97331612896914344554453503890576277e+538Q, 4.17965516524315248567041014799432863e+555Q, 1.52416658652941127494202186163265994e+573Q, 2.00432202283496263048589917253126663e+591Q, 9.89983817930114056039301724151237036e+609Q, 1.91539340453423886973992445388091844e+629Q, 1.51593315523153267229971976448703684e+649Q, 5.13233111267478387648166311677061530e+669Q, 7.78392571224632702458474933380523830e+690Q, 5.54632077048043201021342066119640603e+712Q, 1.95012604607548018918112113273623192e+735Q, 3.55940947870861006830654543397286882e+758Q, 3.55349341454925685547195460273265331e+782Q, 2.04795692045857243226580641802720017e+807Q, 7.20348918974342452885439546130773017e+832Q, 1.63778937282231874342883712446170710e+859Q, 2.55384218509638087014670377175798088e+886Q, 2.90332357089198897009653578594580665e+914Q, 2.56300421336271050326091753908665515e+943Q, 1.87504708602076913446948153935468528e+973Q, 1.21573089114719501597325465689193734e+1004Q, 7.48693407429699486755736805187051392e+1035Q, 4.70376397570597947605315058283235338e+1068Q, 3.24549375035327607806235059706162609e+1102Q, 2.65365798053210741189633750297123713e+1137Q, 2.78113361306514574568406809189880354e+1173Q, 4.05114001708803517429009145027606492e+1210Q, 8.91647476482514121118371429882160487e+1248Q, 3.23223851331898724884680382253593571e+1288Q, 2.10925909168894371224120829576314259e+1329Q, 2.71594389750881594498007515800169231e+1371Q, 7.58558239286242519535157411695442827e+1414Q, 5.06701669031717221741926320252846248e+1459Q, 8.95314256637167381172114473249653533e+1505Q, 4.64314397380853725563695225910483395e+1553Q, 7.86772811149376784892131781209226509e+1602Q, 4.86578559687970087801384706555207321e+1653Q, 1.23116054769360594876926981962811083e+1706Q, 1.43383897399728293235616506956599019e+1760Q, 8.67960762728500385489074199138374599e+1815Q, 3.09584788327503858182429306492345781e+1873Q, 7.40514657822678853879551103162851562e+1932Q, 1.35750288266752565171774149402837640e+1994Q, 2.18886955807028168114167639122307268e+2057Q, 3.57839966436010585653478204799948336e+2122Q, 6.86791017358151132323155526430183469e+2189Q, 1.80021943882415618419042799861559854e+2259Q, 7.53312429569410546159363413753399067e+2330Q, 5.91165553375547478649257686857639971e+2404Q, },
      { 1.57096255099783261137673508918980377e+00Q, 1.57229290236721196082451564636254803e+00Q, 1.57495658191266675503402671533627186e+00Q, 1.57895955363616398471678434379990522e+00Q, 1.58431078956361430514416599492388597e+00Q, 1.59102230111703510742216272394562613e+00Q, 1.59910918118616033722590556649394763e+00Q, 1.60858965710906746764504456185444654e+00Q, 1.61948515482641974319339137784305225e+00Q, 1.63182037453073931785527493516038124e+00Q, 1.64562337819112567903033806653906069e+00Q, 1.66092568939542410935608828998468834e+00Q, 1.67776240601646371733138779357535047e+00Q, 1.69617232627708297296989766202480515e+00Q, 1.71619808886073246730768842066355199e+00Q, 1.73788632779101456236627555096372654e+00Q, 1.76128784288515241044102589887617560e+00Q, 1.78645778667368642025315725404601102e+00Q, 1.81345586877233558659749539967614045e+00Q, 1.84234657879265254187280543490489739e+00Q, 1.87319942898662752121993510591579920e+00Q, 1.90608921793761261888076943857856180e+00Q, 1.94109631673677945144587926354356120e+00Q, 1.97830697922181656566949720098719647e+00Q, 2.01781367800384433737712026561151498e+00Q, 2.05971546817081389507270282494773392e+00Q, 2.10411838073232749275747224342524909e+00Q, 2.15113584806337555354918618566085438e+00Q, 2.20088916381459141771044083611555369e+00Q, 2.25350797998611420231160903572594743e+00Q, 2.30913084411305337537709048855358397e+00Q, 2.36790577978511333384857209921738492e+00Q, 2.42999091402365295350102767412517845e+00Q, 2.49555515536908558968587982458158759e+00Q, 2.56477892689313451429557802920827974e+00Q, 2.63785495874745168415081659796280588e+00Q, 2.71498914529626806741705564771250802e+00Q, 2.79640147236028053619149909690973720e+00Q, 2.88232702062657870038560203490158574e+00Q, 2.97301705186029380311201985278736098e+00Q, 3.06874018519362823755119514588303156e+00Q, 3.16978367147348738564623946178566521e+00Q, 3.27645477442732860056345607892739085e+00Q, 3.38908226826615609816107421136979984e+00Q, 3.50801806229286913554992299494422526e+00Q, 3.63363896413353027399263692498221540e+00Q, 3.76634859436988420367634452674074607e+00Q, 3.90657946663630928939225987850575005e+00Q, 4.05479524866754112035397645627643742e+00Q, 4.21149322136091780158052739625729631e+00Q, 4.37720695466646221916071477112304019e+00Q, 4.55250922105994638849140210916632132e+00Q, 4.73801516951078282566987752649050466e+00Q, 4.93438578525358788671420711795537458e+00Q, 5.14233166333819107447595069798421539e+00Q, 5.36261712689997622445803908396748884e+00Q, 5.59606472439710019418213140693069575e+00Q, 5.84356014374437330661446789126222075e+00Q, 6.10605758538173469317727364122669467e+00Q, 6.38458564090067143612732202429488542e+00Q, 6.68025372897382444864907987044889317e+00Q, 6.99425914605841270881961646990609410e+00Q, 7.32789479574890106033548644768353338e+00Q, 7.68255766782458876413667730681699443e+00Q, 8.05975814607113727007240055318338450e+00Q, 8.46113023296234288939356989479671723e+00Q, 8.88844278939567108027044653070693778e+00Q, 9.34361189902548515485066576358196470e+00Q, 9.82871447949462202212030994384949241e+00Q, 1.03460032772138062549383568058709409e+01Q, 1.08979233984912291622835480030167114e+01Q, 1.14871305480132578973614919602781370e+01Q, 1.21165111661978855517781913327317255e+01Q, 1.27892046801009632078942555615288463e+01Q, 1.35086281087128109623831457304510920e+01Q, 1.42785032930533442126932924888187804e+01Q, 1.51028870549318132669071327511660921e+01Q, 1.59862046261270319640752848374642080e+01Q, 1.69332867326908112807352382039426796e+01Q, 1.79494107678000050622926329104204826e+01Q, 1.90403465419082315888820165929975494e+01Q, 2.02124071618296433363312214170232623e+01Q, 2.14725056619224736964251610816575600e+01Q, 2.28282180919971350546248653847563966e+01Q, 2.42878538594168042463845071817020910e+01Q, 2.58605342287811778487151334440784950e+01Q, 2.75562800035467442565131080245429614e+01Q, 2.93861095522110956389210768876799587e+01Q, 3.13621484999095132865600012683857137e+01Q, 3.34977525874991258153646142408367963e+01Q, 3.58076454079962546803810835720597110e+01Q, 3.83080729687253016658377309622401176e+01Q, 4.10169773015547344709659456503154667e+01Q, 4.39541916587611362308342994483809472e+01Q, 4.71416601949419692729062784327679986e+01Q, 5.06036854536665922553724504470269960e+01Q, 5.43672074601944525232284101611170030e+01Q, 5.84621187791213843904071404608607478e+01Q, 6.29216205405812878410372213510070276e+01Q, 6.77826251851241666263837617897404532e+01Q, 7.30862125426522301461255132629846162e+01Q, 7.88781468648814729209715096907821639e+01Q, 8.52094635973465833426799692679733796e+01Q, 9.21371360338777471668559829773781201e+01Q, 9.97248335767075464896305315099053068e+01Q, 1.08043785167904642576949987865910954e+02Q, 1.17173763608862169247776700625586421e+02Q, 1.27204208998868737227575356110541727e+02Q, 1.38235512466410237338282474486031692e+02Q, 1.50380484815148331050381123359927804e+02Q, 1.63766038752610274212021042234298836e+02Q, 1.78535118123338340289613569013808793e+02Q, 1.94848913160728060357302969254459805e+02Q, 2.12889407359835266977366307026062112e+02Q, 2.32862309344799079018746113493039633e+02Q, 2.55000432284328199435640048881527463e+02Q, 2.79567594267244578195192766207761694e+02Q, 3.06863125912428093434496643114651412e+02Q, 3.37227086745120087399301385427960651e+02Q, 3.71046309996557625549265358225650347e+02Q, 4.08761417046617491055856040237506316e+02Q, 4.50874968419459367009632339064163929e+02Q, 4.97960948895977349122916983180817637e+02Q, 5.50675820938578587679472462796822793e+02Q, 6.09771424466317909206822143572649707e+02Q, 6.76110053572647368547479902312230309e+02Q, 7.50682103874142244645724064576848341e+02Q, 8.34626760051808119187547981730818179e+02Q, 9.29256284531554199823656475151338963e+02Q, 1.03608457849823472804207172846963877e+03Q, 1.15686081966189765730466332807313547e+03Q, 1.29360914245380859999201252074469995e+03Q, 1.44867552185420514387789438088656434e+03Q, 1.62478325953219761549580736728832563e+03Q, 1.82509875991531856022535679631713756e+03Q, 2.05330963597261755441688599655848892e+03Q, 2.31371761449477720025881552764731872e+03Q, 2.61134923664018699944734187657483567e+03Q, 2.95208799409362429898911389656789101e+03Q, 3.34283233256054817982471990245472659e+03Q, 3.79168492775659509883214798303432158e+03Q, 4.30817983871631895490690508451026040e+03Q, 4.90355562457020167332670064217006554e+03Q, 5.59108434363481145162671079876733528e+03Q, 6.38646862557124634146730890858054275e+03Q, 7.30832182941297944038809179971510199e+03Q, 8.37874981279970356111509048453001571e+03Q, 9.62405721874963805881171453111047092e+03Q, 1.10756066619114600841076275282388063e+04Q, 1.27708660544590438787794590107117849e+04Q, 1.47546879201948945213025955616435132e+04Q, 1.70808753741706634268551294101897318e+04Q, 1.98141030969548505096681178137364270e+04Q, 2.30322788820475490754732581365999861e+04Q, 2.68294531792863253502182123460963590e+04Q, 3.13194117839842820030641964449539820e+04Q, 3.66401220970699799668192565829190288e+04Q, 4.29592483666869017028607105165763804e+04Q, 5.04810088263984357248209974578040223e+04Q, 5.94547213318005529011388227962133751e+04Q, 7.01854787517268957919092596317475529e+04Q, 8.30475172617569400265716546608431303e+04Q, 9.85009980505357544638949542794405193e+04Q, 1.17113126626176606026864191754698095e+05Q, 1.39584798216058984483938678946454092e+05Q, 1.66784301639307755633312351999980104e+05Q, 1.99790062652052468605951710457597772e+05Q, 2.39944994603299218686253514351518948e+05Q, 2.88925793983801323167992289814829493e+05Q, 3.48831530919430454806103930858415528e+05Q, 4.22297220149677844682618484564386312e+05Q, 5.12639824636925361908056913471196846e+05Q, 6.24046487622198979196137566922551170e+05Q, 7.61817907323361594136262168936978923e+05Q, 9.32683930022411925704630601110030182e+05Q, 1.14521400777429753902355978052778602e+06Q, 1.41035264627423311876298925540665884e+06Q, 1.74212004187586338510201667214170577e+06Q, 2.15853171693428701405141183541166264e+06Q, 2.68280941012642673073659887232388938e+06Q, 3.34498056359541886091597912499527306e+06Q, 4.18399797233770604788689812330719069e+06Q, 5.25055800816550175243256559083639418e+06Q, 6.61086017414168098803951872291131863e+06Q, 8.35163942396755869310534556270941378e+06Q, 1.05869253239392990034806970327887878e+07Q, 1.34671523510623940861241599892229316e+07Q, 1.71914827102426302145958883698307763e+07Q, 2.20245344902770169422620623417295079e+07Q, 2.83191730172433779681268016861978467e+07Q, 3.65476782026834493187990343694010282e+07Q, 4.73445265723062610640363034466641760e+07Q, 6.15653406350951387288804933390908042e+07Q, 8.03684302689786924828741611740519293e+07Q, 1.05328028435969028854761469747289545e+08Q, 1.38592168908412628618546923035162247e+08Q, 1.83103698592568352374304770508191971e+08Q, 2.42910945745864082034287213456845086e+08Q, 3.23606239375966746282831194186614169e+08Q, 4.32947521859998666323053129499091577e+08Q, 5.81743296796292947920442886775592966e+08Q, 7.85117978938819178602650773642870262e+08Q, 1.06432919762707530726575966438229782e+09Q, 1.44938958291294548467516226794019366e+09Q, 1.98286646937799184909776706883985509e+09Q, 2.72541431469809432350206616813747393e+09Q, 3.76386796411162144400474070771015333e+09Q, 5.22313881495099093715899688078276720e+09Q, 7.28378581064439770444275729738468286e+09Q, 1.02080964238115874250636198090464363e+10Q, 1.43789931847051052135186583023967432e+10Q, 2.03583681254363357780032810961889254e+10Q, 2.89749982708002744366825785538936402e+10Q, 4.14577375164549487752492409798515416e+10Q, 5.96383768387242628650336526594122215e+10Q, 8.62622848391553079951805623395652598e+10Q, 1.25466704538982518000690409655705216e+11Q, 1.83521298226491318558080545133554232e+11Q, 2.69981220740015160361114268684563366e+11Q, 3.99492845215192295441294253418780644e+11Q, 5.94638055870143455023933020703444642e+11Q, 8.90440996742409110650533033931653292e+11Q, 1.34155194167777583831947241717631506e+12Q, 2.03376855033215189170105222958089604e+12Q, 3.10262795987575321360135087196684567e+12Q, 4.76359832170586206290082815387968037e+12Q, 7.36142036056081358397678626842794083e+12Q, 1.14512696145655742338758840735549183e+13Q, 1.79331418699627392634462052642542749e+13Q, 2.82758550128579223200239017529280909e+13Q, 4.48929705367844466861372765075631687e+13Q, 7.17780287265849957064212371548037347e+13Q, 1.15585509854582062520354852997620279e+14Q, 1.87483388636788309288478433629583664e+14Q, 3.06351035640217445409310567498133847e+14Q, 5.04340065300597024230361727189343426e+14Q, 8.36616339689242989007881200440488764e+14Q, 1.39855635164094728875364679627485304e+15Q, 2.35633574951616468243164849507855064e+15Q, 4.00176516738263745645278590345795707e+15Q, 6.85137512840494144543571512545601884e+15Q, 1.18269011176154399046326619510431010e+16Q, 2.05867352701380644281110910622942185e+16Q, 3.61396878431490463311666873476678412e+16Q, 6.39911218439421355095519025524482256e+16Q, 1.14301618562837692261569180960886276e+17Q, 2.05988138391566644299797673070467922e+17Q, 3.74584678835368091393630059068193045e+17Q, 6.87444303468314906803024757565005462e+17Q, 1.27340764361348531366853034790770231e+18Q, 2.38124191682989536626992792404294190e+18Q, 4.49583561730710839927340662784958898e+18Q, 8.57144202490195270096830399067728169e+18Q, 1.65044358418165696532477771893166700e+19Q, 3.21010035242131785085169993033188229e+19Q, 6.30778012444270309076492866733769742e+19Q, 1.25240403115766127899628450500249765e+20Q, 2.51300529564998539443832117224536420e+20Q, 5.09677625569083843571268035202853929e+20Q, 1.04501920001667304566512216455267827e+21Q, 2.16647647926087846601520265828431353e+21Q, 4.54213814567839546278770815494416868e+21Q, 9.63208232444913712819259248595549405e+21Q, 2.06638653668825452816630915727527426e+22Q, 4.48552978555442825059406438230470388e+22Q, 9.85387957361097750825498509227133701e+22Q, 2.19115887446437440814640517501285189e+23Q, 4.93283596439097166796547625560314132e+23Q, 1.12450152997177436346173556893175508e+24Q, 2.59626913615675600812300017445220112e+24Q, 6.07229293831362550112108555687653017e+24Q, 1.43898906630800383562329122001513450e+25Q, 3.45584195640657046905140678735606870e+25Q, 8.41265519171357648977229820858109463e+25Q, 2.07628906165081651016090959669418757e+26Q, 5.19651502464022032237151119613068945e+26Q, 1.31917319408964404296057699402007921e+27Q, 3.39745589598038079361346075350889747e+27Q, 8.87905745443850359109225209251728000e+27Q, 2.35527236149206412607849676379867126e+28Q, 6.34276200772262482389475687836384423e+28Q, 1.73453109399085970484897533524593455e+29Q, 4.81789317060683087119323058524624972e+29Q, 1.35959734649014823198654051259347560e+30Q, 3.89896968990650039174705544740914822e+30Q, 1.13654298652998993600898528562905634e+31Q, 3.36845004399178001650511659074612092e+31Q, 1.01530408470981725989945294876828360e+32Q, 3.11314437622191823730222798219255685e+32Q, 9.71307273973014040273869048577801862e+32Q, 3.08451764358172594571945077912559223e+33Q, 9.97268213982049728423469082288644341e+33Q, 3.28362505228849158612675319471610094e+34Q, 1.10137878539082753552686700652535380e+35Q, 3.76433336759271429732220889611944227e+35Q, 1.31140346593824292621577115225698644e+36Q, 4.65813571068281367213354863266427946e+36Q, 1.68751734747051139203189162215633629e+37Q, 6.23705368501832349017363870039959902e+37Q, 2.35257131442774486893301429598817553e+38Q, 9.05893824021969993626817980294413695e+38Q, 3.56224909761113607077591549609142868e+39Q, 1.43095929157855820977839447284784020e+40Q, 5.87397458498437504922632656680942932e+40Q, 2.46482854981128378684502286098690220e+41Q, 1.05764920309085562822396787743332401e+42Q, 4.64247563928107803530506772359734012e+42Q, 2.08528711827242177927156523287132065e+43Q, 9.58843998518663217709277358040461543e+43Q, 4.51498201124609227956079072858258677e+44Q, 2.17797404834197320411588567916752909e+45Q, 1.07672097682290045825361349736107198e+46Q, 5.45726743292908558875367244655615249e+46Q, 2.83686927045578113375020568154351117e+47Q, 1.51310320139201162564313806536303971e+48Q, 8.28397466722561707458255405781541923e+48Q, 4.65723949199597134403065145457979764e+49Q, 2.68979637071283693718739500357715726e+50Q, 1.59659784691197038762901224386099403e+51Q, 9.74415453825658662874112107295166701e+51Q, 6.11723839484331306452493250946377612e+52Q, 3.95204965058524182677586041680204451e+53Q, 2.62870159207425821306145211826683375e+54Q, 1.80099019650267939321809025642439880e+55Q, 1.27155446256306838318997486480890087e+56Q, 9.25588010447776071059464920928086632e+56Q, 6.94973792013391939342588109222317053e+57Q, 5.38516720076996562080859176422021036e+58Q, 4.30849366810297877444039522534393308e+59Q, 3.56095155754217837059656555899190815e+60Q, 3.04188852838464999204043663335571874e+61Q, 2.68709444193083718922293037351168007e+62Q, 2.45592053890000085507656649738841713e+63Q, 2.32364825416864153745578539849845717e+64Q, 2.27712974158489233058096874867974690e+65Q, 2.31263355291322473374027008502422191e+66Q, 2.43540759298129112939388619780518297e+67Q, 2.66091038882246524570905302589811279e+68Q, 3.01810594342353392025458974267831671e+69Q, 3.55582348951019250289525994888733340e+70Q, 4.35418887779384901315251041444022899e+71Q, 5.54497579551181331524599144834616000e+72Q, 7.34827648190988633565298908145357523e+73Q, 1.01399802572242326074394064208241565e+75Q, 1.45791146224460794340779409761344050e+76Q, 2.18548887681950529537081926891986328e+77Q, 3.41802215328662300798456454917771592e+78Q, 5.58084392060183572830239216435019125e+79Q, 9.51958650279973390758331753317257950e+80Q, 1.69757357824719778562747265505252946e+82Q, 3.16690667099018001388115076193097403e+83Q, 6.18509910641867543038652223056425804e+84Q, 1.26554113438693437654962757714903554e+86Q, 2.71482896587775689871692274985919286e+87Q, 6.11038680296449408162222719197467046e+88Q, 1.44405408617108323852658132530082074e+90Q, 3.58608372663838816495703910754396082e+91Q, 9.36523186806323959966772742491929522e+92Q, 2.57408011620512244881601880029319908e+94Q, 7.45213468986230271920125476429174422e+95Q, 2.27430990383616981910627355587444817e+97Q, 7.32301113412116474943463116371095103e+98Q, 2.48981642173793246167799170730006141e+100Q, 8.94653338635928158843209476986997508e+101Q, 3.40040137239116597862336436061445989e+103Q, 1.36828818620892821730981225660157834e+105Q, 5.83427748982959193060571787534606697e+106Q, 2.63848693767238342424340754241860221e+108Q, 1.26672888276713952132156406152434700e+110Q, 6.46222517831418280306046165038174027e+111Q, 3.50643232060757360375065377104810475e+113Q, 2.02560893394326816509670349356333828e+115Q, 1.24704167708478470702273287091045609e+117Q, 8.18986518840527903795498196049278255e+118Q, 5.74361089440609996487936977642920132e+120Q, 4.30580893408448976261136511421423734e+122Q, 3.45415696607949675499703108821962730e+124Q, 2.96831660153035273688363706593385374e+126Q, 2.73545624237218359223636851390580041e+128Q, 2.70631717669007784745026117275242499e+130Q, 2.87767991634206038473084413615742294e+132Q, 3.29241287826810639047033778585737812e+134Q, 4.05784096195372596931902665934287382e+136Q, 5.39378304910573732382585500687737807e+138Q, 7.74152390167223540621293648465491842e+140Q, 1.20120996231066845642412985516875175e+143Q, 2.01745607955680730064740044686640147e+145Q, 3.67217662348306252636962893865111425e+147Q, 7.25316379805857762968710347450256630e+149Q, 1.55659153530257056999948366173645285e+152Q, 3.63439983279039488510674235872123571e+154Q, 9.24438760046827764031744167566698413e+156Q, 2.56505460126015154661078513762260065e+159Q, 7.77468767447849521051212939671796074e+161Q, 2.57775340952739965069105894604883404e+164Q, 9.36236559200171990439286451934391539e+166Q, 3.73025928974609135812613567068392186e+169Q, 1.63280699411172483017448754437467171e+172Q, 7.86350685466830069681739337700194312e+174Q, 4.17288659053103260909421860789088638e+177Q, 2.44376468354452976432259429734905692e+180Q, 1.58182604983516221850901239956537929e+183Q, 1.13349408906485996833819126879692168e+186Q, 9.00609317867158059779746161516574861e+188Q, 7.94724581006520631483760616875503191e+191Q, 7.80148734070770210787180909072341250e+194Q, 8.53389980133319877537947288273508168e+197Q, 1.04199907704816359699116619452657846e+201Q, 1.42261945983107616739713958471131412e+204Q, 2.17558254343347922345039318825837040e+207Q, 3.73339221107050141050095672544726852e+210Q, 7.20212971416878494124587287789857735e+213Q, 1.56476087980246841023430707631510252e+217Q, 3.83599289170088247519036274601375492e+220Q, 1.06310518934360483187879118646180568e+224Q, 3.33719954606082150504485397744546787e+227Q, 1.18890644129926367056386614030943443e+231Q, 4.81657405194041613555936914144916344e+234Q, 2.22347874123114616622582251703709887e+238Q, 1.17199258523361213934517364652872480e+242Q, 7.06839671131322838482398929154588609e+245Q, 4.88812662946732326430886472870070228e+249Q, 3.88441192207630934518298772235960268e+253Q, 3.55483921979499015020713995580889030e+257Q, 3.75484510816250928148708038190395303e+261Q, 4.58798720933643387311573482445886110e+265Q, 6.49989966050568126079524541048529496e+269Q, 1.07018103272413666142402906179157598e+274Q, 2.05258799808849457042833394124187317e+278Q, 4.59710306190259625117029822247574448e+282Q, 1.20521640888055636836192268959955157e+287Q, 3.70784262206985650361726040790594581e+291Q, 1.34198324051735290325452259089861845e+296Q, 5.72866922601176092550850127280325304e+300Q, 2.89181208605318627652310091586335359e+305Q, 1.73078309894901278503683645288680165e+310Q, 1.23150483936231719457381379774516673e+315Q, 1.04456022524616506171048138618772005e+320Q, 1.05909848234544418123752752792015094e+325Q, 1.28725694153849592436889057738754649e+330Q, 1.88087499295689639308903342601320607e+335Q, 3.31344446614783992260480183208324732e+340Q, 7.05835740270200829603933852702222207e+345Q, 1.82360609116987101874149506325271866e+351Q, 5.73167527316999715111499624522524490e+356Q, 2.19834460849656433024448179756162490e+362Q, 1.03213196012299389398122351217909554e+368Q, 5.95090459142945173642877532664315820e+373Q, 4.22711326689560391486862730288730260e+379Q, 3.71146073815998244096864189178283440e+385Q, 4.04143285030597516778349567433491877e+391Q, 5.47630493200706306950643242188138639e+397Q, 9.26610183846830899934754118194991047e+403Q, 1.96463867218278875638951764233831097e+410Q, 5.23826401399289488725708314794013273e+416Q, 1.76269979359229195206462702562496087e+423Q, 7.51358974574222029659643352716721030e+429Q, 4.07203690042780566335825621191079282e+436Q, 2.81652392485993763998682010256080428e+443Q, 2.49586432083962995754217338958145192e+450Q, 2.84464997550187838898841424132533307e+457Q, 4.18656911271834268911740204159815337e+464Q, 7.98835944729121866190974499356597568e+471Q, 1.98427885725374523364086923738598523e+479Q, 6.44313780728812316513345459942694301e+486Q, 2.74646447436726496994650106679472135e+494Q, 1.54345331097536381511922890966406624e+502Q, 1.14854061177091306735355343708426610e+510Q, 1.13671799925771100929831048483235430e+518Q, 1.50301408173820906903238314130252343e+526Q, 2.66721742401349021977265127553699936e+534Q, 6.38191727827371990119668697526910696e+542Q, 2.06864189323929509313332139375745418e+551Q, 9.12718925758572793769909253655546363e+559Q, 5.50827087084343716443556503345322384e+568Q, 4.56943096951975523591483538202467049e+577Q, 5.23664812769936727945516120554813767e+586Q, 8.33295831969513770916127078276468196e+595Q, 1.85073603040006635508770638738735213e+605Q, 5.76725912854578779706265298662279369e+614Q, 2.53507221317613667093309510235117825e+624Q, 1.58037348241924263546145926074674624e+634Q, 1.40497098317887844492262106048698980e+644Q, 1.79118532756424559114150403858023467e+654Q, 3.29340233091271285034548969456076701e+664Q, 8.78384225704905897945003526718634352e+674Q, 3.41824790512159692619674889187554870e+685Q, 1.95247619547378795632466874007982924e+696Q, 1.64685615123975037569921507294781182e+707Q, 2.06385512057306929298128483493412318e+718Q, 3.86691162964076111860523245198584961e+729Q, 1.09008736553561747232882422664193409e+741Q, 4.65333391196364819411904308657257877e+752Q, 3.02768585771015415481330091322447912e+764Q, 3.02262040534180824988954474846992913e+776Q, 4.66133722640174567906974646266839244e+788Q, 1.11806119156969171229108916233644999e+801Q, 4.20019655376202192049844032418033555e+813Q, 2.48880615873773868126893696694455625e+826Q, 2.34286193142054643224145247339492426e+839Q, 3.52941162196327496812531706159176164e+852Q, 8.57183106082165907783781027353105401e+865Q, 3.38163627323872188047900717079347979e+879Q, 2.18363447282104345585022834572887531e+893Q, 2.32596275137292895223293068081362090e+907Q, 4.11925247272574761366980289309686003e+921Q, 1.22265489460258041690738897372130823e+936Q, 6.13184165651799059701146847592463464e+950Q, 5.23922303275545560334122760478493484e+965Q, 7.69087233862553980667730597006685190e+980Q, 1.95622275953749870274795206909257068e+996Q, 8.69670052895401378378113763338699958e+1011Q, 6.81715148318539616903616353647181598e+1027Q, 9.50697711954141544724599553989093262e+1043Q, 2.38019733682587370869498352841844428e+1060Q, 1.07973324919847346937291516197684901e+1077Q, 8.95814284819025403820872820042945790e+1093Q, 1.37229192661307043018958002675397831e+1111Q, 3.91918794622242371528673544888524419e+1128Q, 2.10730218913863433412315532873510194e+1146Q, 2.15459607934366008026324206138837528e+1164Q, 4.23163839295623007237154131840451677e+1182Q, 1.61294441956204958952970453918741185e+1201Q, 1.20568335528354973250479511763057854e+1220Q, 1.78630819666459637235669417819650103e+1239Q, 5.30233997012953850434143324467683139e+1258Q, 3.18800705045665419646272046839479382e+1278Q, 3.92589619613994835597705628928304867e+1298Q, 1.00145056975313260959947278741812233e+1319Q, 5.35268808912615454171856125906228237e+1339Q, 6.06491960606218539467705074145131193e+1360Q, 1.47410187506507259371724909079906759e+1382Q, 7.77855372825975976779550291054389078e+1403Q, 9.02071339187528665617085297107535931e+1425Q, 2.32776342394968427168699373890370933e+1448Q, 1.35351409374730203846799381939288903e+1471Q, 1.79625881886715571166142200348072039e+1494Q, 5.51189494978629938318487744451579809e+1517Q, 3.96270198063441303687686251379779681e+1541Q, 6.76492837567767494547614386605795924e+1565Q, 2.77991073110506719318274536923711255e+1590Q, 2.78805951202715437087943440518839779e+1615Q, 6.92116280606414755524296394791409952e+1640Q, 4.31380309053366092943951122004431463e+1666Q, 6.84923073262139995438889690748178280e+1692Q, 2.81137418164430829179092468240459910e+1719Q, 3.02821524237655652451836921974619657e+1746Q, 8.69048903533522694231765189726868666e+1773Q, 6.74828063313306417588175763238310325e+1801Q, 1.44026065844141159256872926595644247e+1830Q, 8.58426091495439408178371839859019039e+1858Q, 1.45211919400936944187225846047638548e+1888Q, 7.08718013380970011842483247147601395e+1917Q, 1.01475962220405902523641440040104610e+1948Q, 4.33542978691578087509838132034723750e+1978Q, 5.62285772227295495777726116212032065e+2009Q, 2.25285072921565445630031609130087435e+2041Q, 2.83839050906274298341210206309759282e+2073Q, 1.14501659579022194588844609345877849e+2106Q, 1.50629562239998305958408533546634030e+2139Q, 6.58342161306998815751663631350733541e+2172Q, 9.74198999952210922892973281379349494e+2206Q, 4.97552704772088889228035264268694867e+2241Q, 8.94330619882842342247706838649878784e+2276Q, 5.77072420156026800834371478648673792e+2312Q, 1.36388225285320494361282201451878478e+2349Q, 1.20508189950290985406298094670730466e+2386Q, 4.06413362486490272506766749982212198e+2423Q, 5.34308092015215251528601382597439463e+2461Q, },
   };
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   m_committed_refinements = static_cast<boost::math::detail::atomic_unsigned_integer_type>(m_weights.size() - 1);
#else
   m_committed_refinements = m_weights.size() - 1;
#endif
   m_t_max = 8.88600744303961370002819023592353264e+00Q;
   if (m_max_refinements >= m_abscissas.size())
   {
      m_abscissas.resize(m_max_refinements + 1);
      m_weights.resize(m_max_refinements + 1);
   }
   else
   {
      m_max_refinements = m_abscissas.size() - 1;
   }
}
#endif

}}}}
#endif
