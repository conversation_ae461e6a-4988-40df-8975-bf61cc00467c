//  Copyright <PERSON> 2017.
//  Copyright <PERSON> 2017.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_MATH_QUADRATURE_GAUSS_KRONROD_HPP
#define BOOST_MATH_QUADRATURE_GAUSS_KRONROD_HPP

#ifdef _MSC_VER
#pragma once
#pragma warning(push)
#pragma warning(disable: 4127)
#endif

#include <array>
#include <vector>
#include <algorithm>
#include <boost/math/special_functions/legendre.hpp>
#include <boost/math/special_functions/legendre_stieltjes.hpp>
#include <boost/math/quadrature/gauss.hpp>

namespace boost { namespace math{ namespace quadrature{ namespace detail{

#ifndef BOOST_MATH_GAUSS_NO_COMPUTE_ON_DEMAND

template <class Real, unsigned N, unsigned tag>
class gauss_kronrod_detail
{
   static legendre_stieltjes<Real> const& get_legendre_stieltjes()
   {
      static const legendre_stieltjes<Real> data((N - 1) / 2 + 1);
      return data;
   }
   static std::vector<Real> calculate_abscissa()
   {
      static std::vector<Real> result = boost::math::legendre_p_zeros<Real>((N - 1) / 2);
      const legendre_stieltjes<Real> E = get_legendre_stieltjes();
      std::vector<Real> ls_zeros = E.zeros();
      result.insert(result.end(), ls_zeros.begin(), ls_zeros.end());
      std::sort(result.begin(), result.end());
      return result;
   }
   static std::vector<Real> calculate_weights()
   {
      std::vector<Real> result(abscissa().size(), 0);
      unsigned gauss_order = (N - 1) / 2;
      unsigned gauss_start = gauss_order & 1 ? 0 : 1;
      const legendre_stieltjes<Real>& E = get_legendre_stieltjes();

      for (unsigned i = gauss_start; i < abscissa().size(); i += 2)
      {
         Real x = abscissa()[i];
         Real p = boost::math::legendre_p_prime(gauss_order, x);
         Real gauss_weight = 2 / ((1 - x * x) * p * p);
         result[i] = gauss_weight + static_cast<Real>(2) / (static_cast<Real>(gauss_order + 1) * legendre_p_prime(gauss_order, x) * E(x));
      }
      for (unsigned i = gauss_start ? 0 : 1; i < abscissa().size(); i += 2)
      {
         Real x = abscissa()[i];
         result[i] = static_cast<Real>(2) / (static_cast<Real>(gauss_order + 1) * legendre_p(gauss_order, x) * E.prime(x));
      }
      return result;
   }
public:
   static const std::vector<Real>& abscissa()
   {
      static std::vector<Real> data = calculate_abscissa();
      return data;
   }
   static const std::vector<Real>& weights()
   {
      static std::vector<Real> data = calculate_weights();
      return data;
   }
};

#else

template <class Real, unsigned N, unsigned tag>
class gauss_kronrod_detail;

#endif

template <class T>
class gauss_kronrod_detail<T, 15, 0>
{
public:
   static std::array<T, 8> const & abscissa()
   {
      static constexpr std::array<T, 8> data = {
         0.000000000e+00f,
         2.077849550e-01f,
         4.058451514e-01f,
         5.860872355e-01f,
         7.415311856e-01f,
         8.648644234e-01f,
         9.491079123e-01f,
         9.914553711e-01f,
      };
      return data;
   }
   static std::array<T, 8> const & weights()
   {
      static constexpr std::array<T, 8> data = {
         2.094821411e-01f,
         2.044329401e-01f,
         1.903505781e-01f,
         1.690047266e-01f,
         1.406532597e-01f,
         1.047900103e-01f,
         6.309209263e-02f,
         2.293532201e-02f,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 15, 1>
{
public:
   static std::array<T, 8> const & abscissa()
   {
      static constexpr std::array<T, 8> data = {
         0.00000000000000000e+00,
         2.07784955007898468e-01,
         4.05845151377397167e-01,
         5.86087235467691130e-01,
         7.41531185599394440e-01,
         8.64864423359769073e-01,
         9.49107912342758525e-01,
         9.91455371120812639e-01,
      };
      return data;
   }
   static std::array<T, 8> const & weights()
   {
      static constexpr std::array<T, 8> data = {
         2.09482141084727828e-01,
         2.04432940075298892e-01,
         1.90350578064785410e-01,
         1.69004726639267903e-01,
         1.40653259715525919e-01,
         1.04790010322250184e-01,
         6.30920926299785533e-02,
         2.29353220105292250e-02,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 15, 2>
{
public:
   static std::array<T, 8> const & abscissa()
   {
      static constexpr std::array<T, 8> data = {
         0.00000000000000000000000000000000000e+00L,
         2.07784955007898467600689403773244913e-01L,
         4.05845151377397166906606412076961463e-01L,
         5.86087235467691130294144838258729598e-01L,
         7.41531185599394439863864773280788407e-01L,
         8.64864423359769072789712788640926201e-01L,
         9.49107912342758524526189684047851262e-01L,
         9.91455371120812639206854697526328517e-01L,
      };
      return data;
   }
   static std::array<T, 8> const & weights()
   {
      static constexpr std::array<T, 8> data = {
         2.09482141084727828012999174891714264e-01L,
         2.04432940075298892414161999234649085e-01L,
         1.90350578064785409913256402421013683e-01L,
         1.69004726639267902826583426598550284e-01L,
         1.40653259715525918745189590510237920e-01L,
         1.04790010322250183839876322541518017e-01L,
         6.30920926299785532907006631892042867e-02L,
         2.29353220105292249637320080589695920e-02L,
      };
      return data;
   }
};

#ifdef BOOST_HAS_FLOAT128
template <class T>
class gauss_kronrod_detail<T, 15, 3>
{
public:
   static std::array<T, 8> const & abscissa()
   {
      static const std::array<T, 8> data = {
         0.00000000000000000000000000000000000e+00Q,
         2.07784955007898467600689403773244913e-01Q,
         4.05845151377397166906606412076961463e-01Q,
         5.86087235467691130294144838258729598e-01Q,
         7.41531185599394439863864773280788407e-01Q,
         8.64864423359769072789712788640926201e-01Q,
         9.49107912342758524526189684047851262e-01Q,
         9.91455371120812639206854697526328517e-01Q,
      };
      return data;
   }
   static std::array<T, 8> const & weights()
   {
      static const std::array<T, 8> data = {
         2.09482141084727828012999174891714264e-01Q,
         2.04432940075298892414161999234649085e-01Q,
         1.90350578064785409913256402421013683e-01Q,
         1.69004726639267902826583426598550284e-01Q,
         1.40653259715525918745189590510237920e-01Q,
         1.04790010322250183839876322541518017e-01Q,
         6.30920926299785532907006631892042867e-02Q,
         2.29353220105292249637320080589695920e-02Q,
      };
      return data;
   }
};
#endif

template <class T>
class gauss_kronrod_detail<T, 15, 4>
{
public:
   static  std::array<T, 8> const & abscissa()
   {
      static  std::array<T, 8> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e+00),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.0778495500789846760068940377324491347978440714517064971384573461986693844943520226910343227183698530560857645062738e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.0584515137739716690660641207696146334738201409937012638704325179466381322612565532831268972774658776528675866604802e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.8608723546769113029414483825872959843678075060436095130499289319880373607444407464511674498935942098956811555121368e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.4153118559939443986386477328078840707414764714139026011995535196742987467218051379282683236686324705969251809311201e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.6486442335976907278971278864092620121097230707408814860145771276706770813259572103585847859604590541475281326027862e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.4910791234275852452618968404785126240077093767061778354876910391306333035484014080573077002792572414430073966699522e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9145537112081263920685469752632851664204433837033470129108741357244173934653407235924503509626841760744349505339308e-01),
      };
      return data;
   }
   static  std::array<T, 8> const & weights()
   {
      static  std::array<T, 8> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.0948214108472782801299917489171426369776208022370431671299800656137515132325648616816908211675949102392971459688215e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.0443294007529889241416199923464908471651760418071835742447095312045467698546598879348374292009347554167803659293064e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.9035057806478540991325640242101368282607807545535835588544088036744058072410212679605964605106377593834568683551139e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.6900472663926790282658342659855028410624490030294424149734006755695680921619029112936702403855359908156070095656537e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.4065325971552591874518959051023792039988975724799857556174546893312708093090950408097379122415555910759700350860143e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.0479001032225018383987632254151801744375665421383061189339065133963746321576289524167571627509311333949422518201492e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.3092092629978553290700663189204286665071157211550707113605545146983997477964874928199170264504441995865872491871943e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.2935322010529224963732008058969591993560811275746992267507430254711815787976075946156368168156289483493617134063245e-02),
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 21, 0>
{
public:
   static std::array<T, 11> const & abscissa()
   {
      static constexpr std::array<T, 11> data = {
         0.000000000e+00f,
         1.488743390e-01f,
         2.943928627e-01f,
         4.333953941e-01f,
         5.627571347e-01f,
         6.794095683e-01f,
         7.808177266e-01f,
         8.650633667e-01f,
         9.301574914e-01f,
         9.739065285e-01f,
         9.956571630e-01f,
      };
      return data;
   }
   static std::array<T, 11> const & weights()
   {
      static constexpr std::array<T, 11> data = {
         1.494455540e-01f,
         1.477391049e-01f,
         1.427759386e-01f,
         1.347092173e-01f,
         1.234919763e-01f,
         1.093871588e-01f,
         9.312545458e-02f,
         7.503967481e-02f,
         5.475589657e-02f,
         3.255816231e-02f,
         1.169463887e-02f,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 21, 1>
{
public:
   static std::array<T, 11> const & abscissa()
   {
      static constexpr std::array<T, 11> data = {
         0.00000000000000000e+00,
         1.48874338981631211e-01,
         2.94392862701460198e-01,
         4.33395394129247191e-01,
         5.62757134668604683e-01,
         6.79409568299024406e-01,
         7.80817726586416897e-01,
         8.65063366688984511e-01,
         9.30157491355708226e-01,
         9.73906528517171720e-01,
         9.95657163025808081e-01,
      };
      return data;
   }
   static std::array<T, 11> const & weights()
   {
      static constexpr std::array<T, 11> data = {
         1.49445554002916906e-01,
         1.47739104901338491e-01,
         1.42775938577060081e-01,
         1.34709217311473326e-01,
         1.23491976262065851e-01,
         1.09387158802297642e-01,
         9.31254545836976055e-02,
         7.50396748109199528e-02,
         5.47558965743519960e-02,
         3.25581623079647275e-02,
         1.16946388673718743e-02,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 21, 2>
{
public:
   static std::array<T, 11> const & abscissa()
   {
      static constexpr std::array<T, 11> data = {
         0.00000000000000000000000000000000000e+00L,
         1.48874338981631210884826001129719985e-01L,
         2.94392862701460198131126603103865566e-01L,
         4.33395394129247190799265943165784162e-01L,
         5.62757134668604683339000099272694141e-01L,
         6.79409568299024406234327365114873576e-01L,
         7.80817726586416897063717578345042377e-01L,
         8.65063366688984510732096688423493049e-01L,
         9.30157491355708226001207180059508346e-01L,
         9.73906528517171720077964012084452053e-01L,
         9.95657163025808080735527280689002848e-01L,
      };
      return data;
   }
   static std::array<T, 11> const & weights()
   {
      static constexpr std::array<T, 11> data = {
         1.49445554002916905664936468389821204e-01L,
         1.47739104901338491374841515972068046e-01L,
         1.42775938577060080797094273138717061e-01L,
         1.34709217311473325928054001771706833e-01L,
         1.23491976262065851077958109831074160e-01L,
         1.09387158802297641899210590325804960e-01L,
         9.31254545836976055350654650833663444e-02L,
         7.50396748109199527670431409161900094e-02L,
         5.47558965743519960313813002445801764e-02L,
         3.25581623079647274788189724593897606e-02L,
         1.16946388673718742780643960621920484e-02L,
      };
      return data;
   }
};

#ifdef BOOST_HAS_FLOAT128
template <class T>
class gauss_kronrod_detail<T, 21, 3>
{
public:
   static std::array<T, 11> const & abscissa()
   {
      static const std::array<T, 11> data = {
         0.00000000000000000000000000000000000e+00Q,
         1.48874338981631210884826001129719985e-01Q,
         2.94392862701460198131126603103865566e-01Q,
         4.33395394129247190799265943165784162e-01Q,
         5.62757134668604683339000099272694141e-01Q,
         6.79409568299024406234327365114873576e-01Q,
         7.80817726586416897063717578345042377e-01Q,
         8.65063366688984510732096688423493049e-01Q,
         9.30157491355708226001207180059508346e-01Q,
         9.73906528517171720077964012084452053e-01Q,
         9.95657163025808080735527280689002848e-01Q,
      };
      return data;
   }
   static std::array<T, 11> const & weights()
   {
      static const std::array<T, 11> data = {
         1.49445554002916905664936468389821204e-01Q,
         1.47739104901338491374841515972068046e-01Q,
         1.42775938577060080797094273138717061e-01Q,
         1.34709217311473325928054001771706833e-01Q,
         1.23491976262065851077958109831074160e-01Q,
         1.09387158802297641899210590325804960e-01Q,
         9.31254545836976055350654650833663444e-02Q,
         7.50396748109199527670431409161900094e-02Q,
         5.47558965743519960313813002445801764e-02Q,
         3.25581623079647274788189724593897606e-02Q,
         1.16946388673718742780643960621920484e-02Q,
      };
      return data;
   }
};
#endif

template <class T>
class gauss_kronrod_detail<T, 21, 4>
{
public:
   static  std::array<T, 11> const & abscissa()
   {
      static  std::array<T, 11> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e+00),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.4887433898163121088482600112971998461756485942069169570798925351590361735566852137117762979946369123003116080525534e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.9439286270146019813112660310386556616268662515695791864888229172724611166332737888445523178268237359119185139299872e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.3339539412924719079926594316578416220007183765624649650270151314376698907770350122510275795011772122368293504099894e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.6275713466860468333900009927269414084301388194196695886034621458779266353216327549712087854169992422106448211158815e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.7940956829902440623432736511487357576929471183480946766481718895255857539507492461507857357048037949983390204739932e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.8081772658641689706371757834504237716340752029815717974694859999505607982761420654526977234238996241110129779403362e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.6506336668898451073209668842349304852754301496533045252195973184537475513805556135679072894604577069440463108641177e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.3015749135570822600120718005950834622516790998193924230349406866828415983091673055011194572851007884702013619684320e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.7390652851717172007796401208445205342826994669238211923121206669659520323463615962572356495626855625823304251877421e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9565716302580808073552728068900284792126058721947892436337916111757023046774867357152325996912076724298149077812671e-01),
      };
      return data;
   }
   static  std::array<T, 11> const & weights()
   {
      static  std::array<T, 11> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.4944555400291690566493646838982120374523631668747280383560851873698964478511841925721030705689540264726493367634340e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.4773910490133849137484151597206804552373162548520660451819195439885993016735696405732703959182882254268727823258502e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.4277593857706008079709427313871706088597905653190555560741004743970770449909340027811131706283756428281146832304737e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.3470921731147332592805400177170683276099191300855971406636668491320291400121282036676953159488271772384389604997640e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.2349197626206585107795810983107415951230034952864832764467994120974054238975454689681538622363738230836484113389878e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.0938715880229764189921059032580496027181329983434522007819675829826550372891432168683899432674553842507906611591517e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.3125454583697605535065465083366344390018828880760031970085038760177735672200775237414123061615827474831165614953012e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.5039674810919952767043140916190009395219382000910088173697048048430404342858495178813808730646554086856929327903059e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.4755896574351996031381300244580176373721114058333557524432615804784098927818975325116301569003298086458722055550981e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.2558162307964727478818972459389760617388939845662609571537504232714121820165498692381607605384626494546068817765276e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.1694638867371874278064396062192048396217332481931888927598147525622222058064992651806736704969967250888097490233242e-02),
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 31, 0>
{
public:
   static std::array<T, 16> const & abscissa()
   {
      static constexpr std::array<T, 16> data = {
         0.000000000e+00f,
         1.011420669e-01f,
         2.011940940e-01f,
         2.991800072e-01f,
         3.941513471e-01f,
         4.850818636e-01f,
         5.709721726e-01f,
         6.509967413e-01f,
         7.244177314e-01f,
         7.904185014e-01f,
         8.482065834e-01f,
         8.972645323e-01f,
         9.372733924e-01f,
         9.677390757e-01f,
         9.879925180e-01f,
         9.980022987e-01f,
      };
      return data;
   }
   static std::array<T, 16> const & weights()
   {
      static constexpr std::array<T, 16> data = {
         1.013300070e-01f,
         1.007698455e-01f,
         9.917359872e-02f,
         9.664272698e-02f,
         9.312659817e-02f,
         8.856444306e-02f,
         8.308050282e-02f,
         7.684968076e-02f,
         6.985412132e-02f,
         6.200956780e-02f,
         5.348152469e-02f,
         4.458975132e-02f,
         3.534636079e-02f,
         2.546084733e-02f,
         1.500794733e-02f,
         5.377479873e-03f,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 31, 1>
{
public:
   static std::array<T, 16> const & abscissa()
   {
      static constexpr std::array<T, 16> data = {
         0.00000000000000000e+00,
         1.01142066918717499e-01,
         2.01194093997434522e-01,
         2.99180007153168812e-01,
         3.94151347077563370e-01,
         4.85081863640239681e-01,
         5.70972172608538848e-01,
         6.50996741297416971e-01,
         7.24417731360170047e-01,
         7.90418501442465933e-01,
         8.48206583410427216e-01,
         8.97264532344081901e-01,
         9.37273392400705904e-01,
         9.67739075679139134e-01,
         9.87992518020485428e-01,
         9.98002298693397060e-01,
      };
      return data;
   }
   static std::array<T, 16> const & weights()
   {
      static constexpr std::array<T, 16> data = {
         1.01330007014791549e-01,
         1.00769845523875595e-01,
         9.91735987217919593e-02,
         9.66427269836236785e-02,
         9.31265981708253212e-02,
         8.85644430562117706e-02,
         8.30805028231330210e-02,
         7.68496807577203789e-02,
         6.98541213187282587e-02,
         6.20095678006706403e-02,
         5.34815246909280873e-02,
         4.45897513247648766e-02,
         3.53463607913758462e-02,
         2.54608473267153202e-02,
         1.50079473293161225e-02,
         5.37747987292334899e-03,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 31, 2>
{
public:
   static std::array<T, 16> const & abscissa()
   {
      static constexpr std::array<T, 16> data = {
         0.00000000000000000000000000000000000e+00L,
         1.01142066918717499027074231447392339e-01L,
         2.01194093997434522300628303394596208e-01L,
         2.99180007153168812166780024266388963e-01L,
         3.94151347077563369897207370981045468e-01L,
         4.85081863640239680693655740232350613e-01L,
         5.70972172608538847537226737253910641e-01L,
         6.50996741297416970533735895313274693e-01L,
         7.24417731360170047416186054613938010e-01L,
         7.90418501442465932967649294817947347e-01L,
         8.48206583410427216200648320774216851e-01L,
         8.97264532344081900882509656454495883e-01L,
         9.37273392400705904307758947710209471e-01L,
         9.67739075679139134257347978784337225e-01L,
         9.87992518020485428489565718586612581e-01L,
         9.98002298693397060285172840152271209e-01L,
      };
      return data;
   }
   static std::array<T, 16> const & weights()
   {
      static constexpr std::array<T, 16> data = {
         1.01330007014791549017374792767492547e-01L,
         1.00769845523875595044946662617569722e-01L,
         9.91735987217919593323931734846031311e-02L,
         9.66427269836236785051799076275893351e-02L,
         9.31265981708253212254868727473457186e-02L,
         8.85644430562117706472754436937743032e-02L,
         8.30805028231330210382892472861037896e-02L,
         7.68496807577203788944327774826590067e-02L,
         6.98541213187282587095200770991474758e-02L,
         6.20095678006706402851392309608029322e-02L,
         5.34815246909280872653431472394302968e-02L,
         4.45897513247648766082272993732796902e-02L,
         3.53463607913758462220379484783600481e-02L,
         2.54608473267153201868740010196533594e-02L,
         1.50079473293161225383747630758072681e-02L,
         5.37747987292334898779205143012764982e-03L,
      };
      return data;
   }
};

#ifdef BOOST_HAS_FLOAT128
template <class T>
class gauss_kronrod_detail<T, 31, 3>
{
public:
   static std::array<T, 16> const & abscissa()
   {
      static const std::array<T, 16> data = {
         0.00000000000000000000000000000000000e+00Q,
         1.01142066918717499027074231447392339e-01Q,
         2.01194093997434522300628303394596208e-01Q,
         2.99180007153168812166780024266388963e-01Q,
         3.94151347077563369897207370981045468e-01Q,
         4.85081863640239680693655740232350613e-01Q,
         5.70972172608538847537226737253910641e-01Q,
         6.50996741297416970533735895313274693e-01Q,
         7.24417731360170047416186054613938010e-01Q,
         7.90418501442465932967649294817947347e-01Q,
         8.48206583410427216200648320774216851e-01Q,
         8.97264532344081900882509656454495883e-01Q,
         9.37273392400705904307758947710209471e-01Q,
         9.67739075679139134257347978784337225e-01Q,
         9.87992518020485428489565718586612581e-01Q,
         9.98002298693397060285172840152271209e-01Q,
      };
      return data;
   }
   static std::array<T, 16> const & weights()
   {
      static const std::array<T, 16> data = {
         1.01330007014791549017374792767492547e-01Q,
         1.00769845523875595044946662617569722e-01Q,
         9.91735987217919593323931734846031311e-02Q,
         9.66427269836236785051799076275893351e-02Q,
         9.31265981708253212254868727473457186e-02Q,
         8.85644430562117706472754436937743032e-02Q,
         8.30805028231330210382892472861037896e-02Q,
         7.68496807577203788944327774826590067e-02Q,
         6.98541213187282587095200770991474758e-02Q,
         6.20095678006706402851392309608029322e-02Q,
         5.34815246909280872653431472394302968e-02Q,
         4.45897513247648766082272993732796902e-02Q,
         3.53463607913758462220379484783600481e-02Q,
         2.54608473267153201868740010196533594e-02Q,
         1.50079473293161225383747630758072681e-02Q,
         5.37747987292334898779205143012764982e-03Q,
      };
      return data;
   }
};
#endif

template <class T>
class gauss_kronrod_detail<T, 31, 4>
{
public:
   static  std::array<T, 16> const & abscissa()
   {
      static  std::array<T, 16> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e+00),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.0114206691871749902707423144739233878745105740164180495800189504151097862454083050931321451540380998341273193681967e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.0119409399743452230062830339459620781283645446263767961594972460994823900302018760183625806752105908967902257386509e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.9918000715316881216678002426638896266160338274382080184125545738918081102513884467602322020157243563662094470221235e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.9415134707756336989720737098104546836275277615869825503116534395160895778696141797549711416165976202589352169635648e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.8508186364023968069365574023235061286633893089407312129367943604080239955167155974371848690848595275551258416303565e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.7097217260853884753722673725391064123838639628274960485326541705419537986975857948341462856982614477912646497026257e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.5099674129741697053373589531327469254694822609259966708966160576093305841043840794460394747228060367236079289132544e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.2441773136017004741618605461393800963089929458410256355142342070412378167792521899610109760313432626923598549381925e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.9041850144246593296764929481794734686214051995697617332365280643308302974631807059994738664225445530963711137343440e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.4820658341042721620064832077421685136625617473699263409572755876067507517414548519760771975082148085090373835713340e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.9726453234408190088250965645449588283177871149442786763972687601078537721473771221195399661919716123038835639691946e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.3727339240070590430775894771020947124399627351530445790136307635020297379704552795054758617426808659746824044603157e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.6773907567913913425734797878433722528335733730013163797468062226335804249452174804319385048203118506304424717089291e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.8799251802048542848956571858661258114697281712376148999999751558738843736901942471272205036831914497667516843990079e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9800229869339706028517284015227120907340644231555723034839427970683348682837134566648979907760125278631896777136104e-01),
      };
      return data;
   }
   static  std::array<T, 16> const & weights()
   {
      static  std::array<T, 16> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.0133000701479154901737479276749254677092627259659629246734858372174107615774696665932418050683956749891773195816338e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.0076984552387559504494666261756972191634838013536373069278929029488122760822761077475060185965408326901925180106227e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9173598721791959332393173484603131059567260816713281734860095693651563064308745717056680128223790739026832596087552e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.6642726983623678505179907627589335136656568630495198973407668882934392359962841826511402504664592185391687490319950e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.3126598170825321225486872747345718561927881321317330560285879189052002874531855060114908990458716740695847509343865e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.8564443056211770647275443693774303212266732690655967817996052574877144544749814260718837576325109922207832119243346e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.3080502823133021038289247286103789601554188253368717607281604875233630643885056057630789228337088859687986285569521e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.6849680757720378894432777482659006722109101167947000584089097112470821092034084418224731527690291913686588446455555e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.9854121318728258709520077099147475786045435140671549698798093177992675624987998849748628778570667518643649536771245e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.2009567800670640285139230960802932190400004210329723569147829395618376206272317333030584268303808639229575334680414e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.3481524690928087265343147239430296771554760947116739813222888752727413616259625439714812475198987513183153639571249e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.4589751324764876608227299373279690223256649667921096570980823211805450700059906366455036418897149593261561551176267e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.5346360791375846222037948478360048122630678992420820868148023340902501837247680978434662724296810081131106317333086e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.5460847326715320186874001019653359397271745046864640508377984982400903447009185267605205778819712848080691366407461e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.5007947329316122538374763075807268094639436437387634979291759700896494746154334398961710227490402528151677469993935e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.3774798729233489877920514301276498183080402431284197876486169536848635554354599213793172596490038991436925569025913e-03),
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 41, 0>
{
public:
   static std::array<T, 21> const & abscissa()
   {
      static constexpr std::array<T, 21> data = {
         0.000000000e+00f,
         7.652652113e-02f,
         1.526054652e-01f,
         2.277858511e-01f,
         3.016278681e-01f,
         3.737060887e-01f,
         4.435931752e-01f,
         5.108670020e-01f,
         5.751404468e-01f,
         6.360536807e-01f,
         6.932376563e-01f,
         7.463319065e-01f,
         7.950414288e-01f,
         8.391169718e-01f,
         8.782768113e-01f,
         9.122344283e-01f,
         9.408226338e-01f,
         9.639719273e-01f,
         9.815078775e-01f,
         9.931285992e-01f,
         9.988590316e-01f,
      };
      return data;
   }
   static std::array<T, 21> const & weights()
   {
      static constexpr std::array<T, 21> data = {
         7.660071192e-02f,
         7.637786767e-02f,
         7.570449768e-02f,
         7.458287540e-02f,
         7.303069033e-02f,
         7.105442355e-02f,
         6.864867293e-02f,
         6.583459713e-02f,
         6.265323755e-02f,
         5.911140088e-02f,
         5.519510535e-02f,
         5.094457392e-02f,
         4.643482187e-02f,
         4.166887333e-02f,
         3.660016976e-02f,
         3.128730678e-02f,
         2.588213360e-02f,
         2.038837346e-02f,
         1.462616926e-02f,
         8.600269856e-03f,
         3.073583719e-03f,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 41, 1>
{
public:
   static std::array<T, 21> const & abscissa()
   {
      static constexpr std::array<T, 21> data = {
         0.00000000000000000e+00,
         7.65265211334973338e-02,
         1.52605465240922676e-01,
         2.27785851141645078e-01,
         3.01627868114913004e-01,
         3.73706088715419561e-01,
         4.43593175238725103e-01,
         5.10867001950827098e-01,
         5.75140446819710315e-01,
         6.36053680726515025e-01,
         6.93237656334751385e-01,
         7.46331906460150793e-01,
         7.95041428837551198e-01,
         8.39116971822218823e-01,
         8.78276811252281976e-01,
         9.12234428251325906e-01,
         9.40822633831754754e-01,
         9.63971927277913791e-01,
         9.81507877450250259e-01,
         9.93128599185094925e-01,
         9.98859031588277664e-01,
      };
      return data;
   }
   static std::array<T, 21> const & weights()
   {
      static constexpr std::array<T, 21> data = {
         7.66007119179996564e-02,
         7.63778676720807367e-02,
         7.57044976845566747e-02,
         7.45828754004991890e-02,
         7.30306903327866675e-02,
         7.10544235534440683e-02,
         6.86486729285216193e-02,
         6.58345971336184221e-02,
         6.26532375547811680e-02,
         5.91114008806395724e-02,
         5.51951053482859947e-02,
         5.09445739237286919e-02,
         4.64348218674976747e-02,
         4.16688733279736863e-02,
         3.66001697582007980e-02,
         3.12873067770327990e-02,
         2.58821336049511588e-02,
         2.03883734612665236e-02,
         1.46261692569712530e-02,
         8.60026985564294220e-03,
         3.07358371852053150e-03,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 41, 2>
{
public:
   static std::array<T, 21> const & abscissa()
   {
      static constexpr std::array<T, 21> data = {
         0.00000000000000000000000000000000000e+00L,
         7.65265211334973337546404093988382110e-02L,
         1.52605465240922675505220241022677528e-01L,
         2.27785851141645078080496195368574625e-01L,
         3.01627868114913004320555356858592261e-01L,
         3.73706088715419560672548177024927237e-01L,
         4.43593175238725103199992213492640108e-01L,
         5.10867001950827098004364050955250998e-01L,
         5.75140446819710315342946036586425133e-01L,
         6.36053680726515025452836696226285937e-01L,
         6.93237656334751384805490711845931533e-01L,
         7.46331906460150792614305070355641590e-01L,
         7.95041428837551198350638833272787943e-01L,
         8.39116971822218823394529061701520685e-01L,
         8.78276811252281976077442995113078467e-01L,
         9.12234428251325905867752441203298113e-01L,
         9.40822633831754753519982722212443380e-01L,
         9.63971927277913791267666131197277222e-01L,
         9.81507877450250259193342994720216945e-01L,
         9.93128599185094924786122388471320278e-01L,
         9.98859031588277663838315576545863010e-01L,
      };
      return data;
   }
   static std::array<T, 21> const & weights()
   {
      static constexpr std::array<T, 21> data = {
         7.66007119179996564450499015301017408e-02L,
         7.63778676720807367055028350380610018e-02L,
         7.57044976845566746595427753766165583e-02L,
         7.45828754004991889865814183624875286e-02L,
         7.30306903327866674951894176589131128e-02L,
         7.10544235534440683057903617232101674e-02L,
         6.86486729285216193456234118853678017e-02L,
         6.58345971336184221115635569693979431e-02L,
         6.26532375547811680258701221742549806e-02L,
         5.91114008806395723749672206485942171e-02L,
         5.51951053482859947448323724197773292e-02L,
         5.09445739237286919327076700503449487e-02L,
         4.64348218674976747202318809261075168e-02L,
         4.16688733279736862637883059368947380e-02L,
         3.66001697582007980305572407072110085e-02L,
         3.12873067770327989585431193238007379e-02L,
         2.58821336049511588345050670961531430e-02L,
         2.03883734612665235980102314327547051e-02L,
         1.46261692569712529837879603088683562e-02L,
         8.60026985564294219866178795010234725e-03L,
         3.07358371852053150121829324603098749e-03L,
      };
      return data;
   }
};

#ifdef BOOST_HAS_FLOAT128
template <class T>
class gauss_kronrod_detail<T, 41, 3>
{
public:
   static std::array<T, 21> const & abscissa()
   {
      static const std::array<T, 21> data = {
         0.00000000000000000000000000000000000e+00Q,
         7.65265211334973337546404093988382110e-02Q,
         1.52605465240922675505220241022677528e-01Q,
         2.27785851141645078080496195368574625e-01Q,
         3.01627868114913004320555356858592261e-01Q,
         3.73706088715419560672548177024927237e-01Q,
         4.43593175238725103199992213492640108e-01Q,
         5.10867001950827098004364050955250998e-01Q,
         5.75140446819710315342946036586425133e-01Q,
         6.36053680726515025452836696226285937e-01Q,
         6.93237656334751384805490711845931533e-01Q,
         7.46331906460150792614305070355641590e-01Q,
         7.95041428837551198350638833272787943e-01Q,
         8.39116971822218823394529061701520685e-01Q,
         8.78276811252281976077442995113078467e-01Q,
         9.12234428251325905867752441203298113e-01Q,
         9.40822633831754753519982722212443380e-01Q,
         9.63971927277913791267666131197277222e-01Q,
         9.81507877450250259193342994720216945e-01Q,
         9.93128599185094924786122388471320278e-01Q,
         9.98859031588277663838315576545863010e-01Q,
      };
      return data;
   }
   static std::array<T, 21> const & weights()
   {
      static const std::array<T, 21> data = {
         7.66007119179996564450499015301017408e-02Q,
         7.63778676720807367055028350380610018e-02Q,
         7.57044976845566746595427753766165583e-02Q,
         7.45828754004991889865814183624875286e-02Q,
         7.30306903327866674951894176589131128e-02Q,
         7.10544235534440683057903617232101674e-02Q,
         6.86486729285216193456234118853678017e-02Q,
         6.58345971336184221115635569693979431e-02Q,
         6.26532375547811680258701221742549806e-02Q,
         5.91114008806395723749672206485942171e-02Q,
         5.51951053482859947448323724197773292e-02Q,
         5.09445739237286919327076700503449487e-02Q,
         4.64348218674976747202318809261075168e-02Q,
         4.16688733279736862637883059368947380e-02Q,
         3.66001697582007980305572407072110085e-02Q,
         3.12873067770327989585431193238007379e-02Q,
         2.58821336049511588345050670961531430e-02Q,
         2.03883734612665235980102314327547051e-02Q,
         1.46261692569712529837879603088683562e-02Q,
         8.60026985564294219866178795010234725e-03Q,
         3.07358371852053150121829324603098749e-03Q,
      };
      return data;
   }
};
#endif

template <class T>
class gauss_kronrod_detail<T, 41, 4>
{
public:
   static  std::array<T, 21> const & abscissa()
   {
      static  std::array<T, 21> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e+00),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.6526521133497333754640409398838211004796266813497500804795244384256342048336978241545114181556215606998505646364133e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.5260546524092267550522024102267752791167622481841730660174156703809133685751696356987995886397049724808931527012542e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.2778585114164507808049619536857462474308893768292747231463573920717134186355582779495212519096870803177373131560430e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.0162786811491300432055535685859226061539650501373092456926374427956957435978384116066498234762220215751079886015902e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.7370608871541956067254817702492723739574632170568271182794861351564576437305952789589568363453337894476772208852815e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.4359317523872510319999221349264010784010101082300309613315028346299543059315258601993479156987847429893626854030516e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.1086700195082709800436405095525099842549132920242683347234861989473497039076572814403168305086777919832943068843526e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.7514044681971031534294603658642513281381264014771682537415885495717468074720062012357788489049470208285175093670561e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.3605368072651502545283669622628593674338911679936846393944662254654126258543013255870319549576130658211710937772596e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.9323765633475138480549071184593153338642585141021417904687378454301191710739219011546672416325022748282227809465165e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.4633190646015079261430507035564159031073067956917644413954590606853535503815506468110411362064752061238490065167656e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.9504142883755119835063883327278794295938959911578029703855163894322697871710382866701777890251824617748545658564370e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.3911697182221882339452906170152068532962936506563737325249272553286109399932480991922934056595764922060422035306914e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.7827681125228197607744299511307846671124526828251164853898086998248145904743220740840261624245683876748360309079747e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.1223442825132590586775244120329811304918479742369177479588221915807089120871907893644472619292138737876039175464603e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.4082263383175475351998272221244338027429557377965291059536839973186796006557571220888218676776618448841584569497535e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.6397192727791379126766613119727722191206032780618885606353759389204158078438305698001812525596471563131043491596423e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.8150787745025025919334299472021694456725093981023759869077533318793098857465723460898060491887511355706497739384103e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9312859918509492478612238847132027822264713090165589614818413121798471762775378083944940249657220927472894034724419e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9885903158827766383831557654586300999957020432629666866666860339324411793311982967839129772854179884971700274369367e-01),
      };
      return data;
   }
   static  std::array<T, 21> const & weights()
   {
      static  std::array<T, 21> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.6600711917999656445049901530101740827932500628670118055485349620314721456712029449597396569857880493210849110825276e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.6377867672080736705502835038061001800801036764945996714946431116936745542061941050008345047482501253320401746334511e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.5704497684556674659542775376616558263363155900414326194855223272348838596099414841886740468379707283366777797425290e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.4582875400499188986581418362487528616116493572092273080047040726969899567887364227664202642942357104526915332274625e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.3030690332786667495189417658913112760626845234552742380174250771849743831660040966804802312464527721645765620253776e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.1054423553444068305790361723210167412912159322210143921628270586407381879789525901086146473278095159807542174985045e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.8648672928521619345623411885367801715489704958239860400434264173923806029589970941711224257967651039544669425313433e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.5834597133618422111563556969397943147223506343381443709751749639944420314384296347503523810096842402960802728781816e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.2653237554781168025870122174254980585819744698897886186553324157100424088919284503451596742588386343548162830898103e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.9111400880639572374967220648594217136419365977042191748388047204015262840407696611508732839851952697839735487615776e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.5195105348285994744832372419777329194753456228153116909812131213177827707884692917845453999535518818940813085110223e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.0944573923728691932707670050344948664836365809262579747517140086119113476866735641054822574173198900379392130050979e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.6434821867497674720231880926107516842127071007077929289994127933243222585938804392953931185146446072587020288747981e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.1668873327973686263788305936894738043960843153010324860966353235271889596379726462208702081068715463576895020003842e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.6600169758200798030557240707211008487453496747498001651070009441973280061489266074044986901436324295513243878212345e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.1287306777032798958543119323800737887769280362813337359554598005322423266047996771926031069705049476071896145456496e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.5882133604951158834505067096153142999479118048674944526997797755374306421629440393392427198869345793286369198147609e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.0388373461266523598010231432754705122838627940185929365371868214433006532030353671253640300679157504987977281782909e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.4626169256971252983787960308868356163881050162249770342103474631076960029748751959380482484308382288261238476948520e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.6002698556429421986617879501023472521289227667077976622450602031426535362696437838448828009554532025301579670206091e-03),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.0735837185205315012182932460309874880335046882543449198461628212114333665590378156706265241414469306987988292234740e-03),
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 51, 0>
{
public:
   static std::array<T, 26> const & abscissa()
   {
      static constexpr std::array<T, 26> data = {
         0.000000000e+00f,
         6.154448301e-02f,
         1.228646926e-01f,
         1.837189394e-01f,
         2.438668837e-01f,
         3.030895389e-01f,
         3.611723058e-01f,
         4.178853822e-01f,
         4.730027314e-01f,
         5.263252843e-01f,
         5.776629302e-01f,
         6.268100990e-01f,
         6.735663685e-01f,
         7.177664068e-01f,
         7.592592630e-01f,
         7.978737980e-01f,
         8.334426288e-01f,
         8.658470653e-01f,
         8.949919979e-01f,
         9.207471153e-01f,
         9.429745712e-01f,
         9.616149864e-01f,
         9.766639215e-01f,
         9.880357945e-01f,
         9.955569698e-01f,
         9.992621050e-01f,
      };
      return data;
   }
   static std::array<T, 26> const & weights()
   {
      static constexpr std::array<T, 26> data = {
         6.158081807e-02f,
         6.147118987e-02f,
         6.112850972e-02f,
         6.053945538e-02f,
         5.972034032e-02f,
         5.868968002e-02f,
         5.743711636e-02f,
         5.595081122e-02f,
         5.425112989e-02f,
         5.236288581e-02f,
         5.027767908e-02f,
         4.798253714e-02f,
         4.550291305e-02f,
         4.287284502e-02f,
         4.008382550e-02f,
         3.711627148e-02f,
         3.400213027e-02f,
         3.079230017e-02f,
         2.747531759e-02f,
         2.400994561e-02f,
         2.043537115e-02f,
         1.684781771e-02f,
         1.323622920e-02f,
         9.473973386e-03f,
         5.561932135e-03f,
         1.987383892e-03f,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 51, 1>
{
public:
   static std::array<T, 26> const & abscissa()
   {
      static constexpr std::array<T, 26> data = {
         0.00000000000000000e+00,
         6.15444830056850789e-02,
         1.22864692610710396e-01,
         1.83718939421048892e-01,
         2.43866883720988432e-01,
         3.03089538931107830e-01,
         3.61172305809387838e-01,
         4.17885382193037749e-01,
         4.73002731445714961e-01,
         5.26325284334719183e-01,
         5.77662930241222968e-01,
         6.26810099010317413e-01,
         6.73566368473468364e-01,
         7.17766406813084388e-01,
         7.59259263037357631e-01,
         7.97873797998500059e-01,
         8.33442628760834001e-01,
         8.65847065293275595e-01,
         8.94991997878275369e-01,
         9.20747115281701562e-01,
         9.42974571228974339e-01,
         9.61614986425842512e-01,
         9.76663921459517511e-01,
         9.88035794534077248e-01,
         9.95556969790498098e-01,
         9.99262104992609834e-01,
      };
      return data;
   }
   static std::array<T, 26> const & weights()
   {
      static constexpr std::array<T, 26> data = {
         6.15808180678329351e-02,
         6.14711898714253167e-02,
         6.11285097170530483e-02,
         6.05394553760458629e-02,
         5.97203403241740600e-02,
         5.86896800223942080e-02,
         5.74371163615678329e-02,
         5.59508112204123173e-02,
         5.42511298885454901e-02,
         5.23628858064074759e-02,
         5.02776790807156720e-02,
         4.79825371388367139e-02,
         4.55029130499217889e-02,
         4.28728450201700495e-02,
         4.00838255040323821e-02,
         3.71162714834155436e-02,
         3.40021302743293378e-02,
         3.07923001673874889e-02,
         2.74753175878517378e-02,
         2.40099456069532162e-02,
         2.04353711458828355e-02,
         1.68478177091282982e-02,
         1.32362291955716748e-02,
         9.47397338617415161e-03,
         5.56193213535671376e-03,
         1.98738389233031593e-03,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 51, 2>
{
public:
   static std::array<T, 26> const & abscissa()
   {
      static constexpr std::array<T, 26> data = {
         0.00000000000000000000000000000000000e+00L,
         6.15444830056850788865463923667966313e-02L,
         1.22864692610710396387359818808036806e-01L,
         1.83718939421048892015969888759528416e-01L,
         2.43866883720988432045190362797451586e-01L,
         3.03089538931107830167478909980339329e-01L,
         3.61172305809387837735821730127640667e-01L,
         4.17885382193037748851814394594572487e-01L,
         4.73002731445714960522182115009192041e-01L,
         5.26325284334719182599623778158010178e-01L,
         5.77662930241222967723689841612654067e-01L,
         6.26810099010317412788122681624517881e-01L,
         6.73566368473468364485120633247622176e-01L,
         7.17766406813084388186654079773297781e-01L,
         7.59259263037357630577282865204360976e-01L,
         7.97873797998500059410410904994306569e-01L,
         8.33442628760834001421021108693569569e-01L,
         8.65847065293275595448996969588340088e-01L,
         8.94991997878275368851042006782804954e-01L,
         9.20747115281701561746346084546330632e-01L,
         9.42974571228974339414011169658470532e-01L,
         9.61614986425842512418130033660167242e-01L,
         9.76663921459517511498315386479594068e-01L,
         9.88035794534077247637331014577406227e-01L,
         9.95556969790498097908784946893901617e-01L,
         9.99262104992609834193457486540340594e-01L,
      };
      return data;
   }
   static std::array<T, 26> const & weights()
   {
      static constexpr std::array<T, 26> data = {
         6.15808180678329350787598242400645532e-02L,
         6.14711898714253166615441319652641776e-02L,
         6.11285097170530483058590304162927119e-02L,
         6.05394553760458629453602675175654272e-02L,
         5.97203403241740599790992919325618538e-02L,
         5.86896800223942079619741758567877641e-02L,
         5.74371163615678328535826939395064720e-02L,
         5.59508112204123173082406863827473468e-02L,
         5.42511298885454901445433704598756068e-02L,
         5.23628858064074758643667121378727149e-02L,
         5.02776790807156719633252594334400844e-02L,
         4.79825371388367139063922557569147550e-02L,
         4.55029130499217889098705847526603930e-02L,
         4.28728450201700494768957924394951611e-02L,
         4.00838255040323820748392844670756464e-02L,
         3.71162714834155435603306253676198760e-02L,
         3.40021302743293378367487952295512032e-02L,
         3.07923001673874888911090202152285856e-02L,
         2.74753175878517378029484555178110786e-02L,
         2.40099456069532162200924891648810814e-02L,
         2.04353711458828354565682922359389737e-02L,
         1.68478177091282982315166675363363158e-02L,
         1.32362291955716748136564058469762381e-02L,
         9.47397338617415160720771052365532387e-03L,
         5.56193213535671375804023690106552207e-03L,
         1.98738389233031592650785188284340989e-03L,
      };
      return data;
   }
};

#ifdef BOOST_HAS_FLOAT128
template <class T>
class gauss_kronrod_detail<T, 51, 3>
{
public:
   static std::array<T, 26> const & abscissa()
   {
      static const std::array<T, 26> data = {
         0.00000000000000000000000000000000000e+00Q,
         6.15444830056850788865463923667966313e-02Q,
         1.22864692610710396387359818808036806e-01Q,
         1.83718939421048892015969888759528416e-01Q,
         2.43866883720988432045190362797451586e-01Q,
         3.03089538931107830167478909980339329e-01Q,
         3.61172305809387837735821730127640667e-01Q,
         4.17885382193037748851814394594572487e-01Q,
         4.73002731445714960522182115009192041e-01Q,
         5.26325284334719182599623778158010178e-01Q,
         5.77662930241222967723689841612654067e-01Q,
         6.26810099010317412788122681624517881e-01Q,
         6.73566368473468364485120633247622176e-01Q,
         7.17766406813084388186654079773297781e-01Q,
         7.59259263037357630577282865204360976e-01Q,
         7.97873797998500059410410904994306569e-01Q,
         8.33442628760834001421021108693569569e-01Q,
         8.65847065293275595448996969588340088e-01Q,
         8.94991997878275368851042006782804954e-01Q,
         9.20747115281701561746346084546330632e-01Q,
         9.42974571228974339414011169658470532e-01Q,
         9.61614986425842512418130033660167242e-01Q,
         9.76663921459517511498315386479594068e-01Q,
         9.88035794534077247637331014577406227e-01Q,
         9.95556969790498097908784946893901617e-01Q,
         9.99262104992609834193457486540340594e-01Q,
      };
      return data;
   }
   static std::array<T, 26> const & weights()
   {
      static const std::array<T, 26> data = {
         6.15808180678329350787598242400645532e-02Q,
         6.14711898714253166615441319652641776e-02Q,
         6.11285097170530483058590304162927119e-02Q,
         6.05394553760458629453602675175654272e-02Q,
         5.97203403241740599790992919325618538e-02Q,
         5.86896800223942079619741758567877641e-02Q,
         5.74371163615678328535826939395064720e-02Q,
         5.59508112204123173082406863827473468e-02Q,
         5.42511298885454901445433704598756068e-02Q,
         5.23628858064074758643667121378727149e-02Q,
         5.02776790807156719633252594334400844e-02Q,
         4.79825371388367139063922557569147550e-02Q,
         4.55029130499217889098705847526603930e-02Q,
         4.28728450201700494768957924394951611e-02Q,
         4.00838255040323820748392844670756464e-02Q,
         3.71162714834155435603306253676198760e-02Q,
         3.40021302743293378367487952295512032e-02Q,
         3.07923001673874888911090202152285856e-02Q,
         2.74753175878517378029484555178110786e-02Q,
         2.40099456069532162200924891648810814e-02Q,
         2.04353711458828354565682922359389737e-02Q,
         1.68478177091282982315166675363363158e-02Q,
         1.32362291955716748136564058469762381e-02Q,
         9.47397338617415160720771052365532387e-03Q,
         5.56193213535671375804023690106552207e-03Q,
         1.98738389233031592650785188284340989e-03Q,
      };
      return data;
   }
};
#endif

template <class T>
class gauss_kronrod_detail<T, 51, 4>
{
public:
   static  std::array<T, 26> const & abscissa()
   {
      static  std::array<T, 26> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e+00),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.1544483005685078886546392366796631281724348039823545274305431751687279361558658545141048781022691067898008423227288e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.2286469261071039638735981880803680553220534604978373842389353789270883496885841582643884994633105537597765980412320e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.8371893942104889201596988875952841578528447834990555215034512653236752851109815617651867160645591242103823539931527e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.4386688372098843204519036279745158640563315632598447642113565325038747278585595067977636776325034060327548499765742e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.0308953893110783016747890998033932920041937876655194685731578452573120372337209717349617882111662416355753711853559e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.6117230580938783773582173012764066742207834704337506979457877784674538239569654860329531506093761400789294612122812e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.1788538219303774885181439459457248709336998140069528034955785068796932076966599548717224205109797297615032607570119e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.7300273144571496052218211500919204133181773846162729090723082769560327584128603010315684778279363544192787010704498e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.2632528433471918259962377815801017803683252320191114313002425180471455022502695302371008520604638341970901082293650e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.7766293024122296772368984161265406739573503929151825664548350776102301275263202227671659646579649084013116066120581e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.2681009901031741278812268162451788101954628995068510806525222008437260184181183053045236423845198752346149030569920e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.7356636847346836448512063324762217588341672807274931705965696177828773684928421158196368568030932194044282149314388e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.1776640681308438818665407977329778059771167555515582423493486823991612820974965089522905953765860328116692570706602e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.5925926303735763057728286520436097638752201889833412091838973544501862882026240760763679724185230331463919586229073e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.9787379799850005941041090499430656940863230009338267661706934499488650817643824077118950314443984031474353711531825e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.3344262876083400142102110869356956946096411382352078602086471546171813247709012525322973947759168107133491065937347e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.6584706529327559544899696958834008820284409402823690293965213246691432948180280120756708738064779055576005302835351e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.9499199787827536885104200678280495417455484975358390306170168295917151090119945137118600693039178162093726882638296e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.2074711528170156174634608454633063157457035996277199700642836501131385042631212407808952281702820179915510491592339e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.4297457122897433941401116965847053190520157060899014192745249713729532254404926130890521815127348327109666786665572e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.6161498642584251241813003366016724169212642963709676666624520141292893281185666917636407790823210892689040877316178e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.7666392145951751149831538647959406774537055531440674467098742731616386753588055389644670948300617866819865983054648e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.8803579453407724763733101457740622707248415209160748131449972199405186821347293686245404742032360498210710718706868e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9555696979049809790878494689390161725756264940480817121080493113293348134372793448728802635294700756868258870429256e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9926210499260983419345748654034059370452496042279618586228697762904524428167719073818746102238075978747461480736921e-01),
      };
      return data;
   }
   static  std::array<T, 26> const & weights()
   {
      static  std::array<T, 26> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.1580818067832935078759824240064553190436936903140808056908996403358367244202623293256774502185186717703954810463664e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.1471189871425316661544131965264177586537962876885022711111683500151700796198726558483367566537422877227096643444043e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.1128509717053048305859030416292711922678552321960938357322028070390133769952032831204895569347757809858568165047769e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.0539455376045862945360267517565427162312365710457079923487043144554747810689514408013582515489930908693681447570811e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.9720340324174059979099291932561853835363045476189975483372207816149988460708299020779612375010639778624011960832019e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.8689680022394207961974175856787764139795646254828315293243700305012569486054157617049685031506591863121580010947248e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.7437116361567832853582693939506471994832856823896682976509412313367495727224381199978598247737089593472710899482737e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.5950811220412317308240686382747346820271035112771802428932791066115158268338607019365831655460314732208940609352540e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.4251129888545490144543370459875606826076838441263383072163293312936923476650934130242315028422047795830492882862973e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.2362885806407475864366712137872714887351550723707596350905793656046659248541276597504566497990926306481919129870507e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.0277679080715671963325259433440084440587630604775975142050968279743014641141402310302584542633557037153607386127936e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.7982537138836713906392255756914754983592207423271169651235865196757913880334117810235517477328110033499422471098658e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.5502913049921788909870584752660393043707768935695327316724254392794299567957035458208970599641697203261236226745020e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.2872845020170049476895792439495161101999504199883328877919242515738957655253932048951366960802592343905647433925806e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.0083825504032382074839284467075646401410549266591308713115878386835777315058451955614116158949614066927183232852042e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.7116271483415543560330625367619875995997802688047764805628702762773009669395760582294525748583875707140577080663373e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.4002130274329337836748795229551203225670528250050443083264193121524339063344855010257660547708022429300203676502386e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.0792300167387488891109020215228585600877162393292487644544830559965388047996492709248618249084851477787538356572832e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.7475317587851737802948455517811078614796013288710603199613621069727810352835469926107822047433566792405123805901196e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.4009945606953216220092489164881081392931528209659330290734972342536012282191913069778658241972047765300060007037359e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.0435371145882835456568292235938973678758006097668937220074531550163622566841885855957623103354443247806459277197725e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.6847817709128298231516667536336315840402654624706139411175769276842182270078960078544597372646532637619276509222462e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.3236229195571674813656405846976238077578084997863654732213860488560614587634395544002156258192582265590155862296710e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.4739733861741516072077105236553238716453268483726334971394029603529306140359023187904705754719643032594360138998941e-03),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.5619321353567137580402369010655220701769295496290984052961210793810038857581724171021610100708799763006942755331129e-03),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.9873838923303159265078518828434098894299804282505973837653346298985629336820118753523093675303476883723992297810124e-03),
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 61, 0>
{
public:
   static std::array<T, 31> const & abscissa()
   {
      static constexpr std::array<T, 31> data = {
         0.000000000e+00f,
         5.147184256e-02f,
         1.028069380e-01f,
         1.538699136e-01f,
         2.045251167e-01f,
         2.546369262e-01f,
         3.040732023e-01f,
         3.527047255e-01f,
         4.004012548e-01f,
         4.470337695e-01f,
         4.924804679e-01f,
         5.366241481e-01f,
         5.793452358e-01f,
         6.205261830e-01f,
         6.600610641e-01f,
         6.978504948e-01f,
         7.337900625e-01f,
         7.677774321e-01f,
         7.997278358e-01f,
         8.295657624e-01f,
         8.572052335e-01f,
         8.825605358e-01f,
         9.055733077e-01f,
         9.262000474e-01f,
         9.443744447e-01f,
         9.600218650e-01f,
         9.731163225e-01f,
         9.836681233e-01f,
         9.916309969e-01f,
         9.968934841e-01f,
         9.994844101e-01f,
      };
      return data;
   }
   static std::array<T, 31> const & weights()
   {
      static constexpr std::array<T, 31> data = {
         5.149472943e-02f,
         5.142612854e-02f,
         5.122154785e-02f,
         5.088179590e-02f,
         5.040592140e-02f,
         4.979568343e-02f,
         4.905543456e-02f,
         4.818586176e-02f,
         4.718554657e-02f,
         4.605923827e-02f,
         4.481480013e-02f,
         4.345253970e-02f,
         4.196981022e-02f,
         4.037453895e-02f,
         3.867894562e-02f,
         3.688236465e-02f,
         3.497933803e-02f,
         3.298144706e-02f,
         3.090725756e-02f,
         2.875404877e-02f,
         2.650995488e-02f,
         2.419116208e-02f,
         2.182803582e-02f,
         1.941414119e-02f,
         1.692088919e-02f,
         1.436972951e-02f,
         1.182301525e-02f,
         9.273279660e-03f,
         6.630703916e-03f,
         3.890461127e-03f,
         1.389013699e-03f,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 61, 1>
{
public:
   static std::array<T, 31> const & abscissa()
   {
      static constexpr std::array<T, 31> data = {
         0.00000000000000000e+00,
         5.14718425553176958e-02,
         1.02806937966737030e-01,
         1.53869913608583547e-01,
         2.04525116682309891e-01,
         2.54636926167889846e-01,
         3.04073202273625077e-01,
         3.52704725530878113e-01,
         4.00401254830394393e-01,
         4.47033769538089177e-01,
         4.92480467861778575e-01,
         5.36624148142019899e-01,
         5.79345235826361692e-01,
         6.20526182989242861e-01,
         6.60061064126626961e-01,
         6.97850494793315797e-01,
         7.33790062453226805e-01,
         7.67777432104826195e-01,
         7.99727835821839083e-01,
         8.29565762382768397e-01,
         8.57205233546061099e-01,
         8.82560535792052682e-01,
         9.05573307699907799e-01,
         9.26200047429274326e-01,
         9.44374444748559979e-01,
         9.60021864968307512e-01,
         9.73116322501126268e-01,
         9.83668123279747210e-01,
         9.91630996870404595e-01,
         9.96893484074649540e-01,
         9.99484410050490638e-01,
      };
      return data;
   }
   static std::array<T, 31> const & weights()
   {
      static constexpr std::array<T, 31> data = {
         5.14947294294515676e-02,
         5.14261285374590259e-02,
         5.12215478492587722e-02,
         5.08817958987496065e-02,
         5.04059214027823468e-02,
         4.97956834270742064e-02,
         4.90554345550297789e-02,
         4.81858617570871291e-02,
         4.71855465692991539e-02,
         4.60592382710069881e-02,
         4.48148001331626632e-02,
         4.34525397013560693e-02,
         4.19698102151642461e-02,
         4.03745389515359591e-02,
         3.86789456247275930e-02,
         3.68823646518212292e-02,
         3.49793380280600241e-02,
         3.29814470574837260e-02,
         3.09072575623877625e-02,
         2.87540487650412928e-02,
         2.65099548823331016e-02,
         2.41911620780806014e-02,
         2.18280358216091923e-02,
         1.94141411939423812e-02,
         1.69208891890532726e-02,
         1.43697295070458048e-02,
         1.18230152534963417e-02,
         9.27327965951776343e-03,
         6.63070391593129217e-03,
         3.89046112709988405e-03,
         1.38901369867700762e-03,
      };
      return data;
   }
};

template <class T>
class gauss_kronrod_detail<T, 61, 2>
{
public:
   static std::array<T, 31> const & abscissa()
   {
      static constexpr std::array<T, 31> data = {
         0.00000000000000000000000000000000000e+00L,
         5.14718425553176958330252131667225737e-02L,
         1.02806937966737030147096751318000592e-01L,
         1.53869913608583546963794672743255920e-01L,
         2.04525116682309891438957671002024710e-01L,
         2.54636926167889846439805129817805108e-01L,
         3.04073202273625077372677107199256554e-01L,
         3.52704725530878113471037207089373861e-01L,
         4.00401254830394392535476211542660634e-01L,
         4.47033769538089176780609900322854000e-01L,
         4.92480467861778574993693061207708796e-01L,
         5.36624148142019899264169793311072794e-01L,
         5.79345235826361691756024932172540496e-01L,
         6.20526182989242861140477556431189299e-01L,
         6.60061064126626961370053668149270753e-01L,
         6.97850494793315796932292388026640068e-01L,
         7.33790062453226804726171131369527646e-01L,
         7.67777432104826194917977340974503132e-01L,
         7.99727835821839083013668942322683241e-01L,
         8.29565762382768397442898119732501916e-01L,
         8.57205233546061098958658510658943857e-01L,
         8.82560535792052681543116462530225590e-01L,
         9.05573307699907798546522558925958320e-01L,
         9.26200047429274325879324277080474004e-01L,
         9.44374444748559979415831324037439122e-01L,
         9.60021864968307512216871025581797663e-01L,
         9.73116322501126268374693868423706885e-01L,
         9.83668123279747209970032581605662802e-01L,
         9.91630996870404594858628366109485725e-01L,
         9.96893484074649540271630050918695283e-01L,
         9.99484410050490637571325895705810819e-01L,
      };
      return data;
   }
   static std::array<T, 31> const & weights()
   {
      static constexpr std::array<T, 31> data = {
         5.14947294294515675583404336470993075e-02L,
         5.14261285374590259338628792157812598e-02L,
         5.12215478492587721706562826049442083e-02L,
         5.08817958987496064922974730498046919e-02L,
         5.04059214027823468408930856535850289e-02L,
         4.97956834270742063578115693799423285e-02L,
         4.90554345550297788875281653672381736e-02L,
         4.81858617570871291407794922983045926e-02L,
         4.71855465692991539452614781810994865e-02L,
         4.60592382710069881162717355593735806e-02L,
         4.48148001331626631923555516167232438e-02L,
         4.34525397013560693168317281170732581e-02L,
         4.19698102151642461471475412859697578e-02L,
         4.03745389515359591119952797524681142e-02L,
         3.86789456247275929503486515322810503e-02L,
         3.68823646518212292239110656171359677e-02L,
         3.49793380280600241374996707314678751e-02L,
         3.29814470574837260318141910168539275e-02L,
         3.09072575623877624728842529430922726e-02L,
         2.87540487650412928439787853543342111e-02L,
         2.65099548823331016106017093350754144e-02L,
         2.41911620780806013656863707252320268e-02L,
         2.18280358216091922971674857383389934e-02L,
         1.94141411939423811734089510501284559e-02L,
         1.69208891890532726275722894203220924e-02L,
         1.43697295070458048124514324435800102e-02L,
         1.18230152534963417422328988532505929e-02L,
         9.27327965951776342844114689202436042e-03L,
         6.63070391593129217331982636975016813e-03L,
         3.89046112709988405126720184451550328e-03L,
         1.38901369867700762455159122675969968e-03L,
      };
      return data;
   }
};

#ifdef BOOST_HAS_FLOAT128
template <class T>
class gauss_kronrod_detail<T, 61, 3>
{
public:
   static std::array<T, 31> const & abscissa()
   {
      static const std::array<T, 31> data = {
         0.00000000000000000000000000000000000e+00Q,
         5.14718425553176958330252131667225737e-02Q,
         1.02806937966737030147096751318000592e-01Q,
         1.53869913608583546963794672743255920e-01Q,
         2.04525116682309891438957671002024710e-01Q,
         2.54636926167889846439805129817805108e-01Q,
         3.04073202273625077372677107199256554e-01Q,
         3.52704725530878113471037207089373861e-01Q,
         4.00401254830394392535476211542660634e-01Q,
         4.47033769538089176780609900322854000e-01Q,
         4.92480467861778574993693061207708796e-01Q,
         5.36624148142019899264169793311072794e-01Q,
         5.79345235826361691756024932172540496e-01Q,
         6.20526182989242861140477556431189299e-01Q,
         6.60061064126626961370053668149270753e-01Q,
         6.97850494793315796932292388026640068e-01Q,
         7.33790062453226804726171131369527646e-01Q,
         7.67777432104826194917977340974503132e-01Q,
         7.99727835821839083013668942322683241e-01Q,
         8.29565762382768397442898119732501916e-01Q,
         8.57205233546061098958658510658943857e-01Q,
         8.82560535792052681543116462530225590e-01Q,
         9.05573307699907798546522558925958320e-01Q,
         9.26200047429274325879324277080474004e-01Q,
         9.44374444748559979415831324037439122e-01Q,
         9.60021864968307512216871025581797663e-01Q,
         9.73116322501126268374693868423706885e-01Q,
         9.83668123279747209970032581605662802e-01Q,
         9.91630996870404594858628366109485725e-01Q,
         9.96893484074649540271630050918695283e-01Q,
         9.99484410050490637571325895705810819e-01Q,
      };
      return data;
   }
   static std::array<T, 31> const & weights()
   {
      static const std::array<T, 31> data = {
         5.14947294294515675583404336470993075e-02Q,
         5.14261285374590259338628792157812598e-02Q,
         5.12215478492587721706562826049442083e-02Q,
         5.08817958987496064922974730498046919e-02Q,
         5.04059214027823468408930856535850289e-02Q,
         4.97956834270742063578115693799423285e-02Q,
         4.90554345550297788875281653672381736e-02Q,
         4.81858617570871291407794922983045926e-02Q,
         4.71855465692991539452614781810994865e-02Q,
         4.60592382710069881162717355593735806e-02Q,
         4.48148001331626631923555516167232438e-02Q,
         4.34525397013560693168317281170732581e-02Q,
         4.19698102151642461471475412859697578e-02Q,
         4.03745389515359591119952797524681142e-02Q,
         3.86789456247275929503486515322810503e-02Q,
         3.68823646518212292239110656171359677e-02Q,
         3.49793380280600241374996707314678751e-02Q,
         3.29814470574837260318141910168539275e-02Q,
         3.09072575623877624728842529430922726e-02Q,
         2.87540487650412928439787853543342111e-02Q,
         2.65099548823331016106017093350754144e-02Q,
         2.41911620780806013656863707252320268e-02Q,
         2.18280358216091922971674857383389934e-02Q,
         1.94141411939423811734089510501284559e-02Q,
         1.69208891890532726275722894203220924e-02Q,
         1.43697295070458048124514324435800102e-02Q,
         1.18230152534963417422328988532505929e-02Q,
         9.27327965951776342844114689202436042e-03Q,
         6.63070391593129217331982636975016813e-03Q,
         3.89046112709988405126720184451550328e-03Q,
         1.38901369867700762455159122675969968e-03Q,
      };
      return data;
   }
};
#endif

template <class T>
class gauss_kronrod_detail<T, 61, 4>
{
public:
   static  std::array<T, 31> const & abscissa()
   {
      static  std::array<T, 31> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e+00),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.1471842555317695833025213166722573749141453666569564255160843987964755210427109055870090707285485841217089963590678e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.0280693796673703014709675131800059247190133296515840552101946914632788253917872738234797140786490207720254922664913e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.5386991360858354696379467274325592041855197124433846171896298291578714851081610139692310651074078557990111754952062e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.0452511668230989143895767100202470952410426459556377447604465028350321894663245495592565235317147819577892124850607e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.5463692616788984643980512981780510788278930330251842616428597508896353156907880290636628138423620257595521678255758e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.0407320227362507737267710719925655353115778980946272844421536998312150442387767304001423699909778588529370119457430e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.5270472553087811347103720708937386065363100802142562659418446890026941623319107866436039675211352945165817827083104e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.0040125483039439253547621154266063361104593297078395983186610656429170689311759061175527015710247383961903284673474e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.4703376953808917678060990032285400016240759386142440975447738172761535172858420700400688872124189834257262048739699e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.9248046786177857499369306120770879564426564096318697026073340982988422546396352776837047452262025983265531109327026e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.3662414814201989926416979331107279416417800693029710545274348291201490861897837863114116009718990258091585830703557e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.7934523582636169175602493217254049590705158881215289208126016612312833567812241903809970751783808208940322061083509e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.2052618298924286114047755643118929920736469282952813259505117012433531497488911774115258445532782106478789996137481e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.6006106412662696137005366814927075303835037480883390955067197339904937499734522076788020517029688190998858739703079e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.9785049479331579693229238802664006838235380065395465637972284673997672124315996069538163644008904690545069439941341e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.3379006245322680472617113136952764566938172775468549208701399518300016463613325382024664531597318795933262446521430e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.6777743210482619491797734097450313169488361723290845320649438736515857017299504505260960258623968420224697596501719e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 7.9972783582183908301366894232268324073569842937778450923647349548686662567326007229195202524185356472023967927713548e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.2956576238276839744289811973250191643906869617034167880695298345365650658958163508295244350814016004371545455777732e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.5720523354606109895865851065894385682080017062359612850504551739119887225712932688031120704657195642614071367390794e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 8.8256053579205268154311646253022559005668914714648423206832605312161626269519165572921583828573210485349058106849548e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.0557330769990779854652255892595831956897536366222841356404766397803760239449631913585074426842574155323901785046522e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.2620004742927432587932427708047400408647453682532906091103713367942299565110232681677288015055886244486106298320068e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.4437444474855997941583132403743912158564371496498093181748940139520917000657342753448871376849848523800667868447591e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.6002186496830751221687102558179766293035921740392339948566167242493995770706842922718944370380002378239172677454384e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.7311632250112626837469386842370688488763796428343933853755850185624118958166838288308561708261486365954975485787212e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.8366812327974720997003258160566280194031785470971136351718001015114429536479104370207597166035471368057762560137209e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9163099687040459485862836610948572485050033374616325510019923349807489603260796605556191495843575227494654783755353e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9689348407464954027163005091869528334088203811775079010809429780238769521016374081588201955806171741257405095963817e-01),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.9948441005049063757132589570581081946887394701850801923632642830748016674843587830656468823145435723317885056396548e-01),
      };
      return data;
   }
   static  std::array<T, 31> const & weights()
   {
      static  std::array<T, 31> data = {
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.1494729429451567558340433647099307532736880396464168074637323362474083844397567724480716864880173808112573901197920e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.1426128537459025933862879215781259829552034862395987263855824172761589259406892072066110681184224608133314131500422e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.1221547849258772170656282604944208251146952425246327553509056805511015401279553971190412722969308620984161625812560e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.0881795898749606492297473049804691853384914260919239920771942080972542646780575571132056254070929858650733836163479e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 5.0405921402782346840893085653585028902197018251622233664243959211066713308635283713447747907973700791599900911248852e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.9795683427074206357811569379942328539209602813696108951047392842948482646220377655098341924089250200477846596263918e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.9055434555029778887528165367238173605887405295296569579490717901328215644590555247522873065246297467067324397612445e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.8185861757087129140779492298304592605799236108429800057373350872433793583969368428942672063270298939865425225579922e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.7185546569299153945261478181099486482884807300628457194141861551725533289490897029020276525603515502104799540544222e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.6059238271006988116271735559373580594692875571824924004732379492293604006446052672252973438978639166425766841417488e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.4814800133162663192355551616723243757431392796373009889680201194063503947907899189061064792111919040540351834527742e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.3452539701356069316831728117073258074603308631703168064888805495738640839573863333942084117196541456054957383622173e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.1969810215164246147147541285969757790088656718992374820388720323852655511200365790379948462006156953358103259681948e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 4.0374538951535959111995279752468114216126062126030255633998289613810846761059740961836828802959573901107306640876603e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.8678945624727592950348651532281050250923629821553846790376130679337402056620700554139109487533759557982632153728099e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.6882364651821229223911065617135967736955164781030337670005198584196134970154169862584193360751243227989492571664973e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.4979338028060024137499670731467875097226912794818719972208457232177786702008744219498470603846784465175225933802357e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.2981447057483726031814191016853927510599291213858385714519347641452316582381008804994515341969205985818543200837577e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.0907257562387762472884252943092272635270458523807153426840486964022086189874056947717446328187131273807982629114591e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.8754048765041292843978785354334211144679160542074930035102280759132174815469834227854660515366003136772757344886331e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.6509954882333101610601709335075414366517579522748565770867438338472138903658077617652522759934474895733739329287706e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.4191162078080601365686370725232026760391377828182462432228943562944885267501070688006470962871743661192935455117297e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 2.1828035821609192297167485738338993401507296056834912773630422358720439403382559079356058602393879803560534375378340e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.9414141193942381173408951050128455851421014191431525770276066536497179079025540486072726114628763606440143557769099e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.6920889189053272627572289420322092368566703783835191139883410840546679978551861043620089451681146020853650713611444e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.4369729507045804812451432443580010195841899895001505873565899403000198662495821906144274682894222591414503342336172e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.1823015253496341742232898853250592896264406250607818326302431548265365155855182739401700032519141448997853772603766e-02),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 9.2732796595177634284411468920243604212700249381931076964956469143626665557434385492325784596343112153704094886248672e-03),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 6.6307039159312921733198263697501681336283882177812585973955597357837568277731921327731815844512598157843672104469554e-03),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 3.8904611270998840512672018445155032785151429848864649214200101281144733676455451061226273655941038347210163533085954e-03),
         BOOST_MATH_HUGE_CONSTANT(T, 0, 1.3890136986770076245515912267596996810488412919632724534411055332301367130989865366956251556423820479579333920310978e-03),
      };
      return data;
   }
};

}

template <class Real, unsigned N, class Policy = boost::math::policies::policy<> >
class gauss_kronrod : public detail::gauss_kronrod_detail<Real, N, detail::gauss_constant_category<Real>::value>
{
   typedef detail::gauss_kronrod_detail<Real, N, detail::gauss_constant_category<Real>::value> base;
public:
  typedef Real value_type;
private:
   template <class F>
   static auto integrate_non_adaptive_m1_1(F f, Real* error = nullptr, Real* pL1 = nullptr)->decltype(std::declval<F>()(std::declval<Real>()))
   {
      typedef decltype(f(Real(0))) K;
      using std::abs;
      unsigned gauss_start = 2;
      unsigned kronrod_start = 1;
      unsigned gauss_order = (N - 1) / 2;
      K kronrod_result = 0;
      K gauss_result = 0;
      K fp, fm;
      if (gauss_order & 1)
      {
         fp = f(value_type(0));
         kronrod_result = fp * base::weights()[0];
         gauss_result += fp * gauss<Real, (N - 1) / 2>::weights()[0];
      }
      else
      {
         fp = f(value_type(0));
         kronrod_result = fp * base::weights()[0];
         gauss_start = 1;
         kronrod_start = 2;
      }
      Real L1 = abs(kronrod_result);
      for (unsigned i = gauss_start; i < base::abscissa().size(); i += 2)
      {
         fp = f(base::abscissa()[i]);
         fm = f(-base::abscissa()[i]);
         kronrod_result += (fp + fm) * base::weights()[i];
         L1 += (abs(fp) + abs(fm)) *  base::weights()[i];
         gauss_result += (fp + fm) * gauss<Real, (N - 1) / 2>::weights()[i / 2];
      }
      for (unsigned i = kronrod_start; i < base::abscissa().size(); i += 2)
      {
         fp = f(base::abscissa()[i]);
         fm = f(-base::abscissa()[i]);
         kronrod_result += (fp + fm) * base::weights()[i];
         L1 += (abs(fp) + abs(fm)) *  base::weights()[i];
      }
      if (pL1)
         *pL1 = L1;
      if (error)
         *error = (std::max)(static_cast<Real>(abs(kronrod_result - gauss_result)), static_cast<Real>(abs(kronrod_result * tools::epsilon<Real>() * Real(2))));
      return kronrod_result;
   }

   template <class F>
   struct recursive_info
   {
      F f;
      Real tol;
   };

   template <class F>
   static auto recursive_adaptive_integrate(const recursive_info<F>* info, Real a, Real b, unsigned max_levels, Real abs_tol, Real* error, Real* L1)->decltype(std::declval<F>()(std::declval<Real>()))
   {
      typedef decltype(info->f(Real(a))) K;
      using std::abs;
      Real error_local;
      Real mean = (b + a) / 2;
      Real scale = (b - a) / 2;
      auto ff = [&](const Real& x)->K
      {
         return info->f(scale * x + mean);
      };
      K r1 = integrate_non_adaptive_m1_1(ff, &error_local, L1);
      K estimate = scale * r1;

      K tmp = estimate * info->tol;
      Real abs_tol1 = abs(tmp);
      if (abs_tol == 0)
         abs_tol = abs_tol1;

      if (max_levels && (abs_tol1 < error_local) && (abs_tol < error_local))
      {
         Real mid = (a + b) / 2;
         Real L1_local;
         estimate = recursive_adaptive_integrate(info, a, mid, max_levels - 1, abs_tol / 2, error, L1);
         estimate += recursive_adaptive_integrate(info, mid, b, max_levels - 1, abs_tol / 2, &error_local, &L1_local);
         if (error)
            *error += error_local;
         if (L1)
            *L1 += L1_local;
         return estimate;
      }
      if(L1)
         *L1 *= scale;
      if (error)
         *error = error_local;
      return estimate;
   }

public:
   template <class F>
   static auto integrate(F f, Real a, Real b, unsigned max_depth = 15, Real tol = tools::root_epsilon<Real>(), Real* error = nullptr, Real* pL1 = nullptr)->decltype(std::declval<F>()(std::declval<Real>()))
   {
      typedef decltype(f(a)) K;
      static_assert(!std::is_integral<K>::value,
                  "The return type cannot be integral, it must be either a real or complex floating point type.");
      static const char* function = "boost::math::quadrature::gauss_kronrod<%1%>::integrate(f, %1%, %1%)";
      if (!(boost::math::isnan)(a) && !(boost::math::isnan)(b))
      {
         // Infinite limits:
         if ((a <= -tools::max_value<Real>()) && (b >= tools::max_value<Real>()))
         {
            auto u = [&](const Real& t)->K
            {
               Real t_sq = t*t;
               Real inv = 1 / (1 - t_sq);
               Real w = (1 + t_sq)*inv*inv;
               Real arg = t*inv;
               K res = f(arg)*w;
               return res;
            };
            recursive_info<decltype(u)> info = { u, tol };
            K res = recursive_adaptive_integrate(&info, Real(-1), Real(1), max_depth, Real(0), error, pL1);
            return res;
         }

         // Right limit is infinite:
         if ((boost::math::isfinite)(a) && (b >= tools::max_value<Real>()))
         {
            auto u = [&](const Real& t)->K
            {
               Real z = 1 / (t + 1);
               Real arg = 2 * z + a - 1;
               K res = f(arg)*z*z;
               return res;
            };
            recursive_info<decltype(u)> info = { u, tol };
            K Q = Real(2) * recursive_adaptive_integrate(&info, Real(-1), Real(1), max_depth, Real(0), error, pL1);
            if (pL1)
            {
               *pL1 *= 2;
            }
            return Q;
         }

         if ((boost::math::isfinite)(b) && (a <= -tools::max_value<Real>()))
         {
            auto v = [&](const Real& t)->K
            {
               Real z = 1 / (t + 1);
               Real arg = 2 * z - 1;
               return f(b - arg) * z * z;
            };
            recursive_info<decltype(v)> info = { v, tol };
            K Q = Real(2) * recursive_adaptive_integrate(&info, Real(-1), Real(1), max_depth, Real(0), error, pL1);
            if (pL1)
            {
               *pL1 *= 2;
            }
            return Q;
         }

         if ((boost::math::isfinite)(a) && (boost::math::isfinite)(b))
         {
            if (a==b)
            {
               return K(0);
            }
            recursive_info<F> info = { f, tol };
            if (b < a)
            {
               return -recursive_adaptive_integrate(&info, b, a, max_depth, Real(0), error, pL1);
            }
            return recursive_adaptive_integrate(&info, a, b, max_depth, Real(0), error, pL1);
         }
      }
      return static_cast<K>(policies::raise_domain_error(function, "The domain of integration is not sensible; please check the bounds.", a, Policy()));
   }
};

} // namespace quadrature
} // namespace math
} // namespace boost

#ifdef _MSC_VER
#pragma warning(pop)
#endif

#endif // BOOST_MATH_QUADRATURE_GAUSS_KRONROD_HPP
