/*
 * Copyright <PERSON>, <PERSON> 2020
 * Use, modification and distribution are subject to the
 * Boost Software License, Version 1.0. (See accompanying file
 * LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 */
#ifndef BOOST_MATH_FILTERS_DAUBECHIES_HPP
#define BOOST_MATH_FILTERS_DAUBECHIES_HPP
#include <array>
#include <limits>
#include <boost/math/tools/big_constant.hpp>

#include <boost/math/tools/is_standalone.hpp>
#ifndef BOOST_MATH_STANDALONE
#include <boost/config.hpp>
#ifdef BOOST_NO_CXX17_IF_CONSTEXPR
#error "The header <boost/math/norms.hpp> can only be used in C++17 and later."
#endif
#endif

namespace boost::math::filters {

template <typename Real, unsigned p>
constexpr std::array<Real, 2*p> daubechies_scaling_filter()
{
    static_assert(p < 20, "Filter coefficients only implemented up to 19.");
    if constexpr (p == 1) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.70710678118654752440084436210484903928483593768847403658833986899536623923), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.70710678118654752440084436210484903928483593768847403658833986899536623923) };
    }
    if constexpr (p == 2) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.48296291314453414337487159986444868381695241950420227520117153815521160699), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.83651630373780790557529378091687320345937038834843929349534147265289472661), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.22414386804201338102597276224040035546788351818427176138716833084015463224), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.12940952255126038117444941881202416417453445065996525690700160365752848737) };
    }
    if constexpr (p == 3) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.33267055295008261599851158913900563001292339924506835970847057855179372371), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.80689150931109257649449360408871349051929739499482361816509206360348683533), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.45987750211849157009515194214761672080811017743149230664338678024864033563), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.135011020010254588696389906699374480562219845223781191975686255357062768), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.085441273882026661692819169181773311536197638988086629763517489805067820106), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.035226291885709536602740664715510029327758387917431610398934060748942171898) };
    }
    if constexpr (p == 4) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.23037781330889650086329118304407085000161524824830929779109684402827164374), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.71484657055291564708992195527399260370760840109930817584501100344262504653), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.63088076792985890788171633830061522020322292267719511740574732848435336141), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.027983769416859854211413747180075385411987320224491752840033582653363093001), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.18703481171909308407957067278908141958454417437458009120577708759399258629), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.030841381835560763627219362534959050170314821720034033418212190936063233775), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.032883011666885199735407513549244388664541941137549712597272784076733820371), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.01059740178506903210488320852402722918109996490637641983484974272995894807) };
    }
    if constexpr (p == 5) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.16010239797419291448072374802042073365054412462505783277256992020754721449), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.60382926979718967054011930652506210750742216310169869879692833603686283702), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.72430852843777292772807124410221864076875621823200737257673350480409279922), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.13842814590132073150539714633902469731410579117395610226946522108855217638), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.24229488706638203186257137947461636199149080806261859839137269134106547424), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.032244869584638374648479755062134928313564984163798472254342681319811609232), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.077571493840045713523130489388601819806230994520125279832101462389556715934), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0062414902127982742741905191129201929707635571656876073234174353259085160535), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.012580751999081999468509739931775792949204591626097850201692327064765016178), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0033357252854737712779981834158173557476365247423053150997064285156713511141) };
    }
    if constexpr (p == 6) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.11154074335010946362132391724092343904253959198442167590823604579765159644), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.4946238903984530856772041768778555886377863828962743623531834526188698465), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.75113390802109535067893449843973168558025478333826120097304206594799266889), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.31525035170919762908598965481092639664951992351729452444041638160625244927), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.22626469396543982007631450066090346567054015397289699401434877917897027718), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.12976686756726193556228960587658546084523374922358147015993106558172041337), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.097501605587323049102343552538125342339830747495255142798931931211277981638), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.027522865530305728625540839504193213657387587830434543214942028790000299266), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.031582039317486029565079080699848669057479532373148423375114649352606045848), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00055384220116149613925191839804650122061102627738649642954765245675247527348), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0047772575109455106396359752468207070502305012165814342975932545700203152873), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0010773010853084795648526216095872000352352336093344196898185808947884177076) };
    }
    if constexpr (p == 7) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.077852054085009179019963521957893748379183052927955684387029371799629765558), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.3965393194819173065390003909368428563587151149333287401110499620754878153), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.72913209084623511991694307033928205171796606119013637826977157495535702874), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.4697822874051931224715911609744517386817913056787359532392529141008369217), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.14390600392856497540506836221304600179527357054990848344017530142299184121), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.22403618499387498263814042023325096447578308967732465526650953072415165804), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.071309219266830264750876570501129048227113274514123146595751132202029061555), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.080612609151083071912922480359381905858238209656294890581392184772265275649), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.038029936935014413579592061601858035854461969384678698982831227165741061737), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.016574541630666880654107674891702654792045043948207137052392725487143490786), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.012550998556099840612989886034187779572894740460487100384118183441674102341), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00042957797292136652113212912281973222282353503969424097429463669354494184942), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0018016407040474909152682629127395509625856514696410906253238648145908160214), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00035371379997452024844629583630642543109590600595200400125242756452643355644) };
    }
    if constexpr (p == 8) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.054415842243104009955009405202999355035995542947330503977292808677193622648), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.31287159091429997065916237550571772194973197403702291856987124211531149794), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.67563073629728980680780076704718314998691159063363642277667598381172874696), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.58535468365420671277126552004509819443032666780533690557071753488957052267), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.015829105256349305667380547876466304157744711545028265597353359560312661545), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.28401554296154692651620313237416473246843501248714517935992048090937585953), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00047248457391328277036059000982589498619480112887700746440840960229954465856), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.12874742662047845885702928750970838430226015755564887955770001654977066319), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.017369301001807546169616148868095983114130865294883943169773153885119747929), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.044088253930794751506763723238963501897518391901109964727503919854754335188), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.013981027917398281648722930572633451442395595329343471691463681144262938301), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0087460940474057767163827432464756401804021470811406767426867470261177584378), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0048703529934515743104221815571098240166349785121570037647362085321921707468), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00039174037337694704629808035732377626752293500738904937244926946775919522535), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00067544940645056936636954757387929912184896300135584321036170773750596688963), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00011747678412476953373062823169889094440866939503115039276200135351481307347) };
    }
    if constexpr (p == 9) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.038077947363878346588697658879551184487717144962784174766471924848268047975), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.24383467461259035373204158164928441552636110856092313614290881035764194652), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.60482312369011111190307686743423617089595627118961175653337135326621948137), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.6572880780513005380782126390451732140305858669245918854436034065588449216), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.13319738582500757619095494589979555369217807684336611361543468378391202935), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.29327378327917490880640319524219873104389616285899068257251128264977749451), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.096840783222976460513508133537696602248254581045990996794712676084296381974), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.14854074933810638013507271750604230247912585772806030607716493946005155866), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.03072568147933337921231740072037882714105805024670744781503060505807447087), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.067632829061329973675642274829719015925787908713537399007483312098453778674), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00025094711483145195758718974998855433151762719937096333218341646914339924457), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0223616621236790972053737827026909524185564668830885375472181623365153339), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0047232047577513972779257078482424654057295149126279380187585268565784573968), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0042815036824634298344967950023145318764811818114632883748604550376910299936), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0018476468830562264766191294911256770511210813596003181607325150457467472311), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00023038576352319596720521639282454216929406620524637119722600068668037624505), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00025196318894271013697498868428786066072821815434780282141342653512309743183), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 3.934732031627159948068988306589150707782477055517013507359938155440548714e-05) };
    }
    if constexpr (p == 10) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.026670057900555553586617448771308582771924982908512899327799757762165073565), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.1881768000776914890208929736790939942702546758640393484348595444098866088), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.52720118893172558648174482795950819249814026808402234453185494714513982787), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.68845903945360356574187178254923585397713640424073395372796811583990344659), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.28117234366057746074872699844558928762438888590261504138315439518337480745), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.24984642432731537941610189792077910005646697371320737150131215971067630043), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.1959462743773770435042992543190981318766776476382778474396781876838561782), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.12736934033579326008267723320140097707861774804222459955630975237390670344), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.093057364603572351160352289835452732269429179989469258680639741022454756675), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.071394147166397087145336093076050647672926119837021509175237563479658240953), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.029457536821875812858283237601418391993882005160649487797696542831849016741), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.033212674059341001739763653182159128979783374132670960433233512708331299782), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0036065535669561696554232914171334032995173505186189947627306122912865631829), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.01073317548333057504431811410651364448111548781143923213370333937093436962), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0013953517470529011657893184479577075676605428556885524267211177234294314032), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0019924052951850561171587422426406432117625553655141052800679364782486446064), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00068585669495971162656137098192657141966250433367869205162119035617579793186), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00011646685512928545095148097102589918915274618543475973628192350744688585366), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 9.3588670320069591334050130342228543996884562152972764435218739396771960302e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -1.3264202894521244812436675312266833057492409606058297564006746194667132319e-05) };
    }
    if constexpr (p == 11) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.018694297761471084025435729395619757289677744559219585432866920854463984821), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.1440670211506245127951915849361001143023718967556239604318852482176121098), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.4498997643560453347688940373853603677806895378648933474599655795863440433), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.68568677491620051112093863169630979359402049645677034950515890174507509031), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.41196436894790746292593964857106673074304004101878453156972425113333086269), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.16227524502749036224058272699855115407442643242121302096496674298183214991), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.27423084681794696120210094528352666286480895217751782219057783900502396695), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.066043588196683191900614578881263026567531421689407915411134572260157270027), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.14981201246637849640665626170441932985882724202674846537969095942150753335), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.046479955116684187271617225890237445772232609668482607474503209875958431693), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.06643878569502520527899215536971203191819566896079739622858574023360652697), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.031335090219046076030947984083031445363581056808800319649364455090954740139), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.020840904360181063022948112556564910151577618327347156911266922007670347371), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.015364820906201599426198116099588227440143264957730001202058486279377738507), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0033408588730144456060908086179824061019306583594991908456567317772540817346), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0049284176560590411231707397417082736902855477299158024183974580101937664126), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00030859285881514316517545907262789533071802166050784885819215622760236544448), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00089302325066626461339008246226486539898795198786207287931333582240853430266), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00024915252355282349887122168726668010882211993028554253819713924909320282452), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 5.4439074699368471673578568795768321919366785256007939780436889201682941436e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -3.4634984186984995541280851599740432145064880482334580359436013556794022457e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 4.4942742772365100954156482823101309164104979873837534605717417484340085911e-06) };
    }
    if constexpr (p == 12) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.013112257957229517506746090888933280656655106419313250077482807981375136912), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.1095662728211851546057045050248905426075680503066774046383657434145947162), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.37735513521421265709282126048792061490109417060575263347058391156288509976), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.65719872257930708930276112866411698342502032899884121413942819500193920417), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.51588647842781560875603264805430327006776930870360900561276472986750316737), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.04476388565377462666762747311540166529284543631505924139071704101179655136), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.31617845375278553686480293534780310985088390325473643895742033716004955521), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.02377925725606972768399754609133225784553366558331741152482612716033502819), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.18247860592757967985404361161892417102947714480963026983290112608341065294), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0053595696743521503282762767297683322888626651841927058216363426180926941677), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.096432120096507082026503205343224841274308801430452205143464027486190605026), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.010849130255822184380890102377481521886616305676033346593225122643407774116), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.041546277495084440739270946819065748645135322213883748612870789941361661575), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.012218649069748280719987982664715677129824660931165581753448110455020838418), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.012840825198300683294660344718947284962061098323140976332752255734850878212), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0067114990087955091777670270682156724506481121858564567403794553368864243964), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.002248607240995237599950865211267234018343199786146177099262010279010074457), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0021795036186277604715989033795841711878400752918605712649809429870507326048), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 6.5451282125095955665004303993271107291117705688973566307145520074184844383e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00038865306282093144358972888377959817919174885734201775234360961313833182126), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -8.8504109208204324208216459615537265987383221514719328080154430300199184715e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -2.4241545757030784029789153205317195804237783626642822393775322072922884523e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 1.2776952219379766587140463626166208873759609414394287560553539204265370227e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -1.529071758068510902712239164522901223197615439660340672602696416832185426e-06) };
    }
    if constexpr (p == 13) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0092021335389623679729701634756441846675341719164165623860097030371191217305), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.082861243872902779644320271312304664052081133328901350725142774200948295461), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.31199632216043806339607841122140496939466835289671803171603901730851565412), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.61105585115878765282119951367441805620736126760182394385265829401520403811), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.58888957043121890807103953473953339276659863828128360422355734065024957534), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.08698572617964723731023739838087494399231884076619701250882016643780541724), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.31497290771138863299816982559322825828768884506787890259503068186813077354), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.12457673075081525894138083360212601807927392951736347195720693207526097203), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.17947607942933984323484500723393690135819662562441333930428814546967642382), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.072948933656777163809028306104776619833259290268798735536279632846163669865), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.10580761818793432645096673041964648494788607548012366582323605107782068188), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.026488406475343694639639122480347857264196048442976970162642241213351984244), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.05613947710028342886214501998387331119988378792543100244737056253966757901), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0023799722540590788114651709585542083580943946120519348684751392707093052796), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.023831420710323649032064030677577391342529227176362262740772986334105772526), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0039239414487974162433163702208155265588247466234514040439184072878721741534), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0072555894016175661945183933005026988989735296796466836952698286726005062515), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0027619112346568621780145762660984459953500933305018180249663164832661496432), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0013156739118922989366138353705936433760604125926536523072381245947457012192), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00093232613086726338622265178025485141009180882998019523079915692711270149142), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 4.9251525126289461921409573878665962101037782993888235008400944566113947075e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00016512898855650548946166877092380007558985482146597767033478014935998742089), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 3.0678537579325493466494832285754762366004282172379005631282307485528301752e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 1.0441930571408137081707149910805969516707064362173281696414740684382080067e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -4.7004164793608683256501951650617713216503835829709585565680597113341224593e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 5.2200350984548646917364243548431769767470521552435570015319010534889500617e-07) };
    }
    if constexpr (p == 14) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0064611534600879478181663974486228142723271594192011992181014041682131963958), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.062364758849398898327985667584348774283053336934076671646025188061380759653), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.25485026779262135366590778867782866861870424163671374437800842023823271874), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.55430561794089383599268314498511548440782698309516346096839977753222684278), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.63118784910485677955766171353581723486239524565700172897888095924843364054), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.21867068775890652149174759182175170517657743212704320590302732335253422232), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.27168855227874804141421924761811710946048824656833308143118966692384906639), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.21803352999327604475555588127023119119752406694706047527471270417978599672), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.13839521386480659107399396900215737139899004632296861190591199023906568404), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.13998901658446070124929431622711634403282215556143261813336838173012766117), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.086748411568169689045608220667277953829791495395175036574929644598228933263), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.071548955504046130735841451151738079909580696731295380999909130933544719175), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.055237126259216044116188340605334033979138336325116721576711076599596585739), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.026981408307912916973990314032151933433757665958072742332843492806013139065), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.030185351540390635187148226234891375737815754066586526248837561937906600698), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0056150495303569591332183713676914986374572972039258103876986801691601779992), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.012789493266333408961573307057840792993749038615720583134815345194814152872), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0007462189892683849371817160739181780971958187988813302900435487508701832207), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.003849638868022187445786349316095551774096818508285700493058915725327777667), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0010616910856067618430325667493884111730339415821478308638939395615438824882), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00070802115423552785864429776976171289834718634641815953716700944226680311764), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00038683194731295448210766633980573144273289021078421653799014684260953603847), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -4.1777245770372597352679795398392589283897265901327301310543237300793962875e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 6.8755042526975096038734370216280316018903706876518752798827278989286730311e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -1.0337209184570773946614073425948145862692725094907448506914430590564776387e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -4.3897049017813941152540425613671698293230853608008257181510491943726938635e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 1.7249946753678127698857126927417985235878947098673565769107179471945464675e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -1.7871399683113590763341929384708393438829903099769594469940228456912731758e-07) };
    }
    if constexpr (p == 15) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0045385373615788988814593949102116963466636712437887869979165135973061826537), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.046743394892766271891709693348435757765791517002149435131131973437721383622), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.20602386398699573153989150094763072193061385056419309027020472662490156122), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.49263177170813962360677570740299463726172215651309324021601600369752281377), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.64581314035742435817642091201069179964326082874940461810714894789553306679), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.3390025354547315276912641143835773918756769491793554669336690115093975569), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.19320413960914542870639905343214717463040900391428638279377549215058972689), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.28888259656696564624841250098223329813114356304353425949712929625629348294), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.065282952848772816922831079198695748820391742855961441259651013056195336897), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.19014671400712298234848931165860205179595012581743366968781560605525428795), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.039666176555790944483843667518962006683817428206837368054497453529283868676), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.11112093603723169336567103246740586088586237621659141205056578840053963186), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.033877143923507686208548178444335237708647446874112653694631959912301278547), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.05478055058450761268913790312581879108609415997422768564244845364080300918), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.025767007328439962585945257542698263922036416348253401383968368254171628001), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.020810050169693081677884834246770001620546579513648990409961661727192610178), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.015083918027835902363292744601703227362448928233056277162339688903695078372), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0051010003604075431697088601855653147248010665273442220555266310691657585506), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0064877345603157449951816831492186908169558456393888264079289674694518478686), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00024175649076162428116672253263001796052299469958145352233294119288913115604), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0019433239803822115417649123325410874410114248655795314014523026178946534131), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00037348235413761699200980942136454146113876309680302566257402267355583112012), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00035956524436246881216496200759098088581942024540840903056274804667283876243), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00015589648992059974794716582412271088162555670596254959152286036304283062436), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 2.5792699155318936809258624176168559129440423687673407091601197370095004239e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -2.8133296266047813647553247770784786657914438762937889042672556207530238846e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 3.3629871817375798031248452104201774721348466558640781871863045015033003361e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 1.8112704079405770837685109122858411605770859253375078505902906808655395748e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -6.3168823258816644212015972995176576541661379151211955104166416260677217926e-07), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 6.1333599133057520290562994602897886019891904508853965121738455950580467421e-08) };
    }
    if constexpr (p == 16) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0031892209253477380297695475646459586870670867501314287678758781777869486255), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.034907714323673346410301472240230200092182414305039841461400546477376377996), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.16506428348885311789912527305611348115848350023427232402135928290203409966), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.43031272284600381374039254243576846206339704780369867739246462911886492889), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.63735633208378889863198524129960305364985959408141981259677515932255242966), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.44029025688635690003908691635716792885278030351352725787898843221650113068), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.089751089402489642857187180774425974306592474455826601496247184152268478657), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.32706331052791770464629056756891196417572289182288124281417235311673360956), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.027918208133028276682645195950268732043399712191747360415354797624368497971), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.21119069394710428872096801632688379009284914261676794392510428265923934071), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.027340263752716041364852457572016179654290278195071302202315002681181103625), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.13238830556381039045004741477564933750922878177060279787985490274221256881), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0062397227524748717656745033941200258654446563116787609907614580866382455691), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.075924236044276315821484987439414224615304059461009433519403139995132210368), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.007588974368857737638494890864636995796586975144990925400097160943671446065), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.036888397691730142333526663208945543147187484297067308310640687651290473968), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.010297659640955969411650005800766169005288562658036622088541473449733491006), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.013993768859828731029504518736703297264098402917278689884901007836048645298), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0069900145634139166702842495365172883380578561996464690781157596750271110103), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0036442796214983899321690005409336293870553339733531086688412150466993897338), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0031280233812062688316612025598546787678214719061936081174503608103102062028), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00040789698084971283624174703234060957824319529723105467150713971109834118994), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00094102174935956758892664539536358754077547472167344805092502736794906570513), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00011424152003872239264402280995556629458396843449364726528770914231509741867), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00017478724522533818038017586376607468749860247286153998979719535958918304821), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -6.1035966214109358351623691505222128119572599819659191439617228139148512866e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -1.3945668988208893451990783119984019823252735691986753354087079704564463183e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 1.1336608661276258587588487628865369975194710682037536617578434288137908852e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -1.0435713423116065015254547372626154048874789306356764715460326802862590987e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -7.3636567854512055120996957197255636465854455458416633274335692328538306759e-07), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 2.3087840868575458664054127329420061213063067358666555253725448647310885248e-07), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -2.1093396301007430970005726236034899068362975845916053077453499495253372784e-08) };
    }
    if constexpr (p == 17) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0022418070010373128535359626770744369140621918805603707332505311813341919259), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.025985393703606043389148645917207883154739445248782412943999482768766372542), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.1312149033078244065775506231859069960144293609259978530067004394590336311), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.37035072415264115044925481907218864494770788768968038236504259357952962858), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.61099661568462281818866788676793720827370938933587262913717839049083529043), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.51831576405693783932545385280859680462168171977184164024399049868328890005), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.027314970403293635004312507191475864803504698189645630036729428228327122309), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.32832074836396173609096653407250617675815976981515580246791305268967637817), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.12659975221588270287446791109338255050539662601040861621037283235916492881), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.19731058956501099278540470447819301425514224141356469171222842260045667421), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.10113548917747027215096998564334348021966225454996648761094379629310502982), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.12681569177828631109485711286623316803847921859150170657321374899495682695), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.057091419631676927289112394786513823241611608698453470539901446405938289849), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.081105986654160885079658857485554292010243641909544991940206782277507046299), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.022312336178103795953391360595348137562322421140936892440208691931826899684), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.04692243838926973733300897059211400507138768125498030602878439278261681888), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0032709555358192937816553602221774944520695259580616093928092757512244970215), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.022733676583946270318456162447884489699067137413383394980248641327563508924), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.003042989981354637068592482637907206078633395457225096588287881355853146664), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0086029215203228548317137064132436599179267362842717306119209867025723028123), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0029679966915260948728064850600080382699594638465483789950441950342910257421), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0023012052421535456243020598690384236042419766801894474760647649372848372532), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0014368453048029761262228904029803849035036745307299358095614347112123643258), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0003281325194098379713954444017520115075812402442728749700195651589580752544), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00043946542776864367783856775273178416322892493197388921794659107576228639092), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -2.561010956654845882729891210949920221664082061531909655178413745234158599e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -8.2048032024533918390954825762821898661362730496367643386895936421343224507e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 2.3186813798745950844820682057062775721066951740918953385307347893104278256e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 6.9906009850767512732045497008553786277627585859020579640274813805087794281e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -4.5059424772229881941022682063783121297135726007164999449184166870668358606e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 3.016549609994557415605207594879939763476168705217646897702706189847154081e-07), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 2.9577009333168567549799052588161513678703456289243173073546394956952199848e-07), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -8.4239484460026801787870712969228770684103109422227996225931334161777920896e-08), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 7.2674929685616081108797674414090350341585817197897910888920464080702904824e-09) };
    }
    if constexpr (p == 18) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0015763102184407604315407449299397777476707537109916603636844298618471400136), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.019288531724146377059213917158290524199546670252884975722367148333214257583), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.10358846582242359622419104919372535964706965552202416729762241846965639407), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.3146789413370316990571998255652579931786706190489374509491307510367406906), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.57182680776660722348185893709006234193936737431309305612953240480326532941), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.5718016548886513352891119994065965025668047882818525060759395264214055574), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.14722311196992814157509772710810723125578641073557013878016771358441101668), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.29365404073655874424790309949811507239357107290350532396617523080121257803), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.21648093400514297112376786256682714714379372356694924083886923936637850636), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.14953397556537778935093017389136672088048166918937656102619430719129024846), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.16708131276325740451493181399501347453242056463539880831522506333701390259), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.092331884150846280604293725586594597314318480001445696120745084674620649946), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.10675224665982848559322005816149848613852664046241120839177020546784793523), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.06488721621190544281947577955141911463129382116634147846137149845158777673), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.057051247738536884120907688464996222605962261204310385246006764232075504463), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.044526141902982324715561435597446534929714778914398335927550346682023149009), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.023733210395860001032752095826652161101975193307134902330715657901430091468), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.026670705926470590299879086316720203432078959999360728133634715203759565359), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0062621679543057074852360931444978825019903252047450131902680523152575205897), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.013051480946612001772776364476008071697551910545075716666061335803862913986), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00011863003385811746573017415921618190845448994174523174051856157299661793944), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0049433436054667381306655295168029748342996383133664777652952032600306770112), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0011187326669924970728006588552386501823180604825849701455126871198522790015), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0013405962983361066295175672282515836098230445246859866403239426912072388874), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00062846568296514571256194498854208382175510227963015828743496528807647838152), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0002135815619103406884039052814341926025873200325996466522543440510602714976), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00019864855231174794857982454163624895549277978802640178761396052157321846575), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -1.5359171235347246750697703358767171937004724270215132365872881022095732925e-07), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 3.7412378807400381810922081380353939523042926157939850307313638513284983136e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -8.5206025374466952039192549116555230224375969562263765123059170652407353738e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -3.3326344788858218887824520333410368273115059077964984398293372795640596047e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 1.7687129836276154558763287307553751764125013591140588154531006192349912666e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -7.6916326898851761460001528785395984058173975881565251167699086518741626737e-08), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -1.1760987670282316984509823565612925613475797776953969535281413678099558364e-07), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 3.0688358630451748009354782949339753724501797878945744929305701163444205366e-08), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -2.5079344549485982671951731831471267318063171448682758199414032678582329327e-09) };
    }
    if constexpr (p == 19) {
       return {BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0011086697631817105710991541952097151642452996777734359321354552903838392437), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.014281098450764397374398891529501992347456634421636659578707159911077107554), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.081278113265459550652963067849016248398449799710286203664977268640601914766), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.26438843174089678467481003802894268738623778072119207184173858147735135325), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.5244363774646549153360575975484064626044633641048072116393160236280782601), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.60170454912753789488670771359218026205365656395859632933139314339208321996), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.26089495265103882928724566753105283241726731013019077399252138987658244676), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.22809139421548264637463257760546372070937872370864259095348224177661957967), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.2858386317558262418545975695028984237217356095588335149922119373572946758), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.074652269708103266367634331118788190058658661497319096563653999513477365762), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.21234974330627848880906085670598241970770742008788394484169086980366838817), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.033518541902302878681693884187857315069778450752389668198140325857348144255), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.14278569503873657497796027316261128129984977061524285086275624334185303587), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.027584350625628668750147435201621986553744745969634230807628183098383677126), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.086906755555812232488476454288084430347852080024681927596403529749277245343), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.026501236250123040899018358436763873610750680176867478081713456471923315261), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.045674226277230908056454442142957960179389357321156300508801097842329883502), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.021623767409585047130329842571723723543180970678587525425710208194990717941), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.019375549889176127646370943544579998144968850958758255464069637242167682526), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.013988388678535141632504012352486625219168138674530958368083666866969990669), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0058669222810121747265844934360543737738146083408087581773727651715064285159), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0070407473671052431530145112074006201094016898976653830782293980831623467743), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.0007689543592575483559749139148673955163477947086039406129546422516486233949), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.0026875518007015820039573638550703986365340389209824782901702671996059483895), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.000341808653458595776565165729046380813521421484881951725779403154942704075), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 0.00073580252050543520702604819053972818751831757927799048581894949173298761722), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00026067613567862800573183151308975227903839393620735634086135475594271631675), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -0.00012460079173415877534497844089016539903173414133419809047575920469834920407), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 8.7112704672199229654168623881911282684129338932820835177294434200975612019e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 5.105950487073886053049222809934231573687367992106282669389264164311557866e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -1.6640176297154944546206777198991986303336756088120181087391449229369242509e-05), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 3.0109643162965263396953344547259436326457989381624271688513825000813231431e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 1.5319314766911930639318323810866360312031230327234774636241410593622067271e-06), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -6.862755657769142701883554613486732854452740752771392411758418826691795609e-07), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 1.4470882987978445420782198632916154205516735740713678343161676502124977734e-08), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 4.6369377757826042234308577282109488988717482910859622966493207002061571346e-08), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, -1.116402067035825816390504769142472586464975799284473682246076559584239149e-08), BOOST_MATH_BIG_CONSTANT(Real, std::numeric_limits<Real>::digits, 8.6668488389976193503230135407821246272897421902730593191228406494140940156e-10) };
    }
}

template<class Real, size_t p>
std::array<Real, 2*p> daubechies_wavelet_filter() {
    std::array<Real, 2*p> g;
    auto h = daubechies_scaling_filter<Real, p>();
    for (size_t i = 0; i < g.size(); i += 2)
    {
        g[i] = h[g.size() - i - 1];
        g[i+1] = -h[g.size() - i - 2];
    }
    return g;
}

} // namespaces
#endif
