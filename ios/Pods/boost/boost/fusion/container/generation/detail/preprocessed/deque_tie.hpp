/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    This is an auto-generated file. Do not edit!
==============================================================================*/

#if FUSION_MAX_DEQUE_SIZE <= 10
#include <boost/fusion/container/generation/detail/preprocessed/deque_tie10.hpp>
#elif FUSION_MAX_DEQUE_SIZE <= 20
#include <boost/fusion/container/generation/detail/preprocessed/deque_tie20.hpp>
#elif FUSION_MAX_DEQUE_SIZE <= 30
#include <boost/fusion/container/generation/detail/preprocessed/deque_tie30.hpp>
#elif FUSION_MAX_DEQUE_SIZE <= 40
#include <boost/fusion/container/generation/detail/preprocessed/deque_tie40.hpp>
#elif FUSION_MAX_DEQUE_SIZE <= 50
#include <boost/fusion/container/generation/detail/preprocessed/deque_tie50.hpp>
#else
#error "FUSION_MAX_DEQUE_SIZE out of bounds for preprocessed headers"
#endif
