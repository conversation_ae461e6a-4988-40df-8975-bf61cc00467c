/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    This is an auto-generated file. Do not edit!
==============================================================================*/

#if FUSION_MAX_DEQUE_SIZE <= 10
#include <boost/fusion/container/deque/detail/cpp03/preprocessed/deque_initial_size10.hpp>
#elif FUSION_MAX_DEQUE_SIZE <= 20
#include <boost/fusion/container/deque/detail/cpp03/preprocessed/deque_initial_size20.hpp>
#elif FUSION_MAX_DEQUE_SIZE <= 30
#include <boost/fusion/container/deque/detail/cpp03/preprocessed/deque_initial_size30.hpp>
#elif FUSION_MAX_DEQUE_SIZE <= 40
#include <boost/fusion/container/deque/detail/cpp03/preprocessed/deque_initial_size40.hpp>
#elif FUSION_MAX_DEQUE_SIZE <= 50
#include <boost/fusion/container/deque/detail/cpp03/preprocessed/deque_initial_size50.hpp>
#else
#error "FUSION_MAX_DEQUE_SIZE out of bounds for preprocessed headers"
#endif
