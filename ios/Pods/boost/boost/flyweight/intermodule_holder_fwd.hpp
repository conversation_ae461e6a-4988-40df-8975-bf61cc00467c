/* Copyright 2006-2008 <PERSON>.
 * Distributed under the Boost Software License, Version 1.0.
 * (See accompanying file LICENSE_1_0.txt or copy at
 * http://www.boost.org/LICENSE_1_0.txt)
 *
 * See http://www.boost.org/libs/flyweight for library home page.
 */

#ifndef BOOST_FLYWEIGHT_INTERMODULE_HOLDER_FWD_HPP
#define BOOST_FLYWEIGHT_INTERMODULE_HOLDER_FWD_HPP

#if defined(_MSC_VER)
#pragma once
#endif

namespace boost{

namespace flyweights{

template<typename C>
struct intermodule_holder_class;

struct intermodule_holder;

} /* namespace flyweights */

} /* namespace boost */

#endif
