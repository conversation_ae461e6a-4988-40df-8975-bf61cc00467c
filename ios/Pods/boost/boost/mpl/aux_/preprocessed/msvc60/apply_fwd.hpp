
// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//

// Preprocessed version of "boost/mpl/apply_fwd.hpp" header
// -- DO NOT modify by hand!

namespace boost { namespace mpl {

template<
      typename F
    >
struct apply0;

template<
      typename F, typename T1
    >
struct apply1;

template<
      typename F, typename T1, typename T2
    >
struct apply2;

template<
      typename F, typename T1, typename T2, typename T3
    >
struct apply3;

template<
      typename F, typename T1, typename T2, typename T3, typename T4
    >
struct apply4;

template<
      typename F, typename T1, typename T2, typename T3, typename T4
    , typename T5
    >
struct apply5;

}}

