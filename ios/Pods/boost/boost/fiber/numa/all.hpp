

//          Copyright <PERSON> 2018.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_FIBERS_NUMA_H
#define BOOST_FIBERS_NUMA_H

#include <boost/fiber/numa/algo/work_stealing.hpp>
#include <boost/fiber/numa/pin_thread.hpp>
#include <boost/fiber/numa/topology.hpp>

#endif // BOOST_FIBERS_NUMA_H
