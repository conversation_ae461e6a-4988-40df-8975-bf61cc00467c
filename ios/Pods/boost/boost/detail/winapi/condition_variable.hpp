/*
 * Copyright 2017 <PERSON><PERSON>
 *
 * Distributed under the Boost Software License, Version 1.0.
 * See http://www.boost.org/LICENSE_1_0.txt
 *
 * This header is deprecated, use boost/winapi/condition_variable.hpp instead.
 */

#ifndef BOOST_DETAIL_WINAPI_CONDITION_VARIABLE_HPP
#define BOOST_DETAIL_WINAPI_CONDITION_VARIABLE_HPP

#include <boost/config/header_deprecated.hpp>

BOOST_HEADER_DEPRECATED("<boost/winapi/condition_variable.hpp>")

#include <boost/winapi/condition_variable.hpp>
#include <boost/detail/winapi/detail/deprecated_namespace.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

// Deprecated
#define BOOST_DETAIL_WINAPI_CONDITION_VARIABLE_INIT BOOST_WINAPI_CONDITION_VARIABLE_INIT

#endif // BOOST_DETAIL_WINAPI_CONDITION_VARIABLE_HPP
