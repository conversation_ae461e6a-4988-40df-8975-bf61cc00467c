/*
 * Copyright 2017 <PERSON><PERSON>
 *
 * Distributed under the Boost Software License, Version 1.0.
 * See http://www.boost.org/LICENSE_1_0.txt
 *
 * This header is deprecated, use boost/winapi/process.hpp instead.
 */

#ifndef BOOST_DETAIL_WINAPI_PROCESS_HPP_
#define BOOST_DETAIL_WINAPI_PROCESS_HPP_

#include <boost/config/header_deprecated.hpp>

BOOST_HEADER_DEPRECATED("<boost/winapi/process.hpp>")

#include <boost/winapi/process.hpp>
#include <boost/detail/winapi/detail/deprecated_namespace.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

#endif // BOOST_DETAIL_WINAPI_PROCESS_HPP_
