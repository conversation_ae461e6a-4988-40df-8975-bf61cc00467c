#ifndef BOOST_METAPARSE_FAIL_AT_FIRST_CHAR_EXPECTED_HPP
#define BOOST_METAPARSE_FAIL_AT_FIRST_CHAR_EXPECTED_HPP

// Copyright <PERSON> (<EMAIL>)  2015.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#include <boost/metaparse/v1/fail_at_first_char_expected.hpp>

namespace boost
{
  namespace metaparse
  {
    using v1::fail_at_first_char_expected;
  }
}

#endif

