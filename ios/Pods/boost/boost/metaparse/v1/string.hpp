#ifndef BOOST_METAPARSE_V1_STRING_HPP
#define BOOST_METAPARSE_V1_STRING_HPP

// Copyright <PERSON> (<EMAIL>)  2017.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#include <boost/metaparse/config.hpp>

#if BOOST_METAPARSE_STD >= 2011
#  include <boost/metaparse/v1/cpp11/string.hpp>
#else
#  include <boost/metaparse/v1/cpp98/string.hpp>
#endif

#endif

