#ifndef BOOST_METAPARSE_V1_IMPL_HAS_TYPE_HPP
#define BOOST_METAPARSE_V1_IMPL_HAS_TYPE_HPP

// Copyright <PERSON> (<EMAIL>)  2014.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#include <boost/mpl/has_xxx.hpp>

namespace boost
{
  namespace metaparse
  {
    namespace v1
    {
      namespace impl
      {
        BOOST_MPL_HAS_XXX_TRAIT_DEF(type)
      }
    }
  }
}

#endif

