#ifndef BOOST_METAPARSE_FOLDR_REJECT_INCOMPLETE_HPP
#define BOOST_METAPARSE_FOLDR_REJECT_INCOMPLETE_HPP

// Copyright <PERSON> (<EMAIL>)  2015.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#include <boost/metaparse/v1/foldr_reject_incomplete.hpp>

namespace boost
{
  namespace metaparse
  {
    using v1::foldr_reject_incomplete;
  }
}

#endif

